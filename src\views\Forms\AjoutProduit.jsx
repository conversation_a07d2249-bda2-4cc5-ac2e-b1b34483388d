import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Spinner, <PERSON><PERSON>, Modal } from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css';
import ProductStep1 from './ProductStep1';
import ProductStep2 from './ProductStep2';
import ProductStep3 from './ProductStep3';
import ProductStep4 from './ProductStep4';
import ProductStepVariants from './ProductStepVariants';

const AjoutProduit = () => {
  const API_URL = 'https://laravel-api.fly.dev/api';

  // États principaux
  const [formData, setFormData] = useState({
    nom_produit: '',
    description_produit: '',
    prix_produit: '',
    quantite_produit: 0,
    marque_id: '',
    sous_sous_categorie_id: '',
    image_produit: null,
    reference: '',
    attributs: [],
    poids: '',
    dimensions: '',
    couleur: '',
    materiau: ''
  });

  // États pour les données dynamiques
  const [categories, setCategories] = useState([]);
  const [sousCategories, setSousCategories] = useState([]);
  const [sousSousCategories, setSousSousCategories] = useState([]);
  const [marques, setMarques] = useState([]);
  const [attributs, setAttributs] = useState([]);
  const [attributsGroups, setAttributsGroups] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedAttributs, setSelectedAttributs] = useState([]);
  const [attributValues, setAttributValues] = useState({});
  const [images, setImages] = useState([]); // Multi-image support
  const [primaryImageIndex, setPrimaryImageIndex] = useState(0);
  const [imagePreview, setImagePreview] = useState(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [activeAttributTab, setActiveAttributTab] = useState('all');
  const [categorySpecificFields, setCategorySpecificFields] = useState({});

  // États pour les variantes
  const [variantes, setVariantes] = useState([]);
  const [showVarianteModal, setShowVarianteModal] = useState(false);
  const [newVariante, setNewVariante] = useState({
    sku: '',
    prix_supplement: 0,
    quantite: 0,
    image: null,
    valeurs: {}
  });
  const [varianteImagePreview, setVarianteImagePreview] = useState(null);

  const totalSteps = 5; // Now using 5 modular steps

  // Chargement des données initiales
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        setLoading(true);
        const [marquesRes, categoriesRes] = await Promise.all([fetch(`${API_URL}/marques`), fetch(`${API_URL}/categories`)]);

        const marquesData = await marquesRes.json();
        const categoriesData = await categoriesRes.json();

        setMarques(marquesData.data || marquesData || []);
        const cats = categoriesData.data || categoriesData || [];
        setCategories(cats);
        console.log('Fetched categories:', cats);
      } catch (err) {
        setError('Erreur de chargement des données initiales');
      } finally {
        setLoading(false);
      }
    };

    fetchInitialData();
  }, []);

  // Auto-preselect category and sous-category if sous_sous_categorie_id is set (mirroring EditProduit)
  useEffect(() => {
    const preselectCategoryHierarchy = async () => {
      if (formData.sous_sous_categorie_id && categories.length > 0) {
        let allSousSousCats = [];
        try {
          for (const cat of categories) {
            const sousCatsRes = await fetch(`${API_URL}/sousCategories?categorie_id=${cat.id}`);
            const sousCatsData = await sousCatsRes.json();
            const sousCats = sousCatsData.data || sousCatsData || [];
            for (const sousCat of sousCats) {
              const sousSousCatsRes = await fetch(`${API_URL}/sous_sousCategories?sous_categorie_id=${sousCat.id}`);
              const sousSousCatsData = await sousSousCatsRes.json();
              const sousSousCats = sousSousCatsData.data || sousSousCatsData || [];
              allSousSousCats = allSousSousCats.concat(sousSousCats);
            }
          }
        } catch (err) {
          // fallback: leave allSousSousCats empty
        }
        const sousSousCat = allSousSousCats.find((ssc) => ssc.id === formData.sous_sous_categorie_id);
        if (sousSousCat) {
          const sous_categorie_id = sousSousCat.sous_categorie_id;
          let matchedSousCat = null;
          let matchedCategorieId = null;
          for (const cat of categories) {
            const sousCatsRes = await fetch(`${API_URL}/sousCategories?categorie_id=${cat.id}`);
            const sousCatsData = await sousCatsRes.json();
            const sousCats = sousCatsData.data || sousCatsData || [];
            const foundSousCat = sousCats.find((sc) => sc.id === sous_categorie_id);
            if (foundSousCat) {
              matchedSousCat = foundSousCat;
              matchedCategorieId = cat.id;
              break;
            }
          }
          if (matchedSousCat && matchedCategorieId) {
            setFormData((prev) => ({
              ...prev,
              categorie_id: matchedCategorieId,
              sous_categorie_id: matchedSousCat.id,
              sous_sous_categorie_id: formData.sous_sous_categorie_id
            }));
          }
        }
      }
    };
    preselectCategoryHierarchy();
  }, [categories, formData.sous_sous_categorie_id]);

  // Chargement des sous-catégories quand la catégorie change
  useEffect(() => {
    const fetchSousCategories = async () => {
      if (!formData.categorie_id) {
        setSousCategories([]);
        setSousSousCategories([]);
        setFormData((prev) => ({
          ...prev,
          sous_sous_categorie_id: '',
          attributs: []
        }));
        return;
      }

      try {
        setLoading(true);
        const res = await fetch(`${API_URL}/sousCategories?categorie_id=${formData.categorie_id}`);
        const data = await res.json();
        setSousCategories(data.data || data || []);
      } catch (err) {
        setError('Erreur de chargement des sous-catégories');
      } finally {
        setLoading(false);
      }
    };

    fetchSousCategories();
  }, [formData.categorie_id]);

  // Chargement des sous-sous-catégories quand la sous-catégorie change
  useEffect(() => {
    const fetchSousSousCategories = async () => {
      if (!formData.sous_categorie_id) {
        setSousSousCategories([]);
        setFormData((prev) => ({
          ...prev,
          sous_sous_categorie_id: '',
          attributs: []
        }));
        return;
      }

      try {
        setLoading(true);
        const res = await fetch(`${API_URL}/sous_sousCategories?sous_categorie_id=${formData.sous_categorie_id}`);
        const data = await res.json();
        setSousSousCategories(data.data || data || []);
      } catch (err) {
        setError('Erreur de chargement des sous-sous-catégories');
      } finally {
        setLoading(false);
      }
    };

    fetchSousSousCategories();
  }, [formData.sous_categorie_id]);

  // Chargement des attributs quand la sous-catégorie change
  useEffect(() => {
    const fetchAttributs = async () => {
      if (!formData.sous_categorie_id) {
        setAttributs([]);
        setSelectedAttributs([]);
        setAttributValues({});
        setAttributsGroups({});
        setCategorySpecificFields({});
        setFormData((prev) => ({
          ...prev,
          attributs: []
        }));
        return;
      }

      try {
        setLoading(true);

        // Charger les attributs pour la sous-catégorie
        const attributsRes = await fetch(`${API_URL}/attributs/filtrables?sous_categorie_id=${formData.sous_categorie_id}`);
        if (!attributsRes.ok) throw new Error('Impossible de charger les attributs');

        const attributsData = await attributsRes.json();
        const attrs = attributsData.data || attributsData || [];
        setAttributs(attrs);

        // Initialiser les valeurs des attributs
        const initialValues = {};
        attrs.forEach((attr) => {
          initialValues[attr.id] = '';
        });
        setAttributValues(initialValues);

        // Grouper les attributs
        const groups = {};
        attrs.forEach((attr) => {
          const group = attr.groupe || 'Général';
          if (!groups[group]) groups[group] = [];
          groups[group].push(attr);
        });
        setAttributsGroups(groups);
        setActiveAttributTab(Object.keys(groups)[0] || 'all');

        // Configurer les champs spécifiques selon la sous-catégorie
        const selectedSousCategorie = sousCategories.find((cat) => cat.id == formData.sous_categorie_id)?.nom_sous_categorie.toLowerCase();

        const fieldsConfig = {};

        if (selectedSousCategorie?.includes('lit') || selectedSousCategorie?.includes('parure')) {
          fieldsConfig.dimensions = true;
          fieldsConfig.couleur = true;
          fieldsConfig.materiau = true;
        } else if (selectedSousCategorie?.includes('meuble')) {
          fieldsConfig.dimensions = true;
          fieldsConfig.poids = true;
          fieldsConfig.materiau = true;
        } else if (selectedSousCategorie?.includes('textile')) {
          fieldsConfig.couleur = true;
          fieldsConfig.materiau = true;
        }

        setCategorySpecificFields(fieldsConfig);
      } catch (err) {
        console.error('Erreur de chargement:', err);
        setAttributs([]);
        setAttributsGroups({});
        setCategorySpecificFields({});
      } finally {
        setLoading(false);
      }
    };

    fetchAttributs();
  }, [formData.sous_categorie_id]);

  const handleChange = (e) => {
    const { name, value } = e.target;

    if (name === 'marque_id') {
      setFormData({
        ...formData,
        marque_id: value,
        categorie_id: '',
        sous_categorie_id: '',
        sous_sous_categorie_id: '',
        attributs: []
      });
    } else if (name === 'categorie_id') {
      setFormData({
        ...formData,
        categorie_id: value,
        sous_categorie_id: '',
        sous_sous_categorie_id: '',
        attributs: []
      });
    } else if (name === 'sous_categorie_id') {
      setFormData({
        ...formData,
        sous_categorie_id: value,
        sous_sous_categorie_id: '',
        attributs: []
      });
    } else if (name === 'sous_sous_categorie_id') {
      setFormData({
        ...formData,
        sous_sous_categorie_id: value
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFormData({
        ...formData,
        image_produit: file
      });

      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleVarianteImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setNewVariante({
        ...newVariante,
        image: file
      });

      const reader = new FileReader();
      reader.onloadend = () => {
        setVarianteImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleAttributChange = (attributId, value) => {
    setAttributValues({
      ...attributValues,
      [attributId]: value
    });
  };

  const handleVarianteAttributChange = (attributId, value) => {
    setNewVariante({
      ...newVariante,
      valeurs: {
        ...newVariante.valeurs,
        [attributId]: value
      }
    });
  };

  const toggleAttributSelection = (attributId) => {
    setSelectedAttributs((prev) => {
      if (prev.includes(attributId)) {
        return prev.filter((id) => id !== attributId);
      } else {
        return [...prev, attributId];
      }
    });
  };

  const addVariante = () => {
    // Vérifier que tous les attributs obligatoires sont remplis
    const attributsObligatoires = attributs.filter((attr) => attr.obligatoire && !newVariante.valeurs[attr.id]);

    if (attributsObligatoires.length > 0) {
      setError(`Veuillez remplir les attributs obligatoires: ${attributsObligatoires.map((a) => a.nom).join(', ')}`);
      return;
    }

    // Générer un SKU automatique si non fourni
    let sku = newVariante.sku;
    if (!sku) {
      const randomPart = Math.floor(1000 + Math.random() * 9000);
      sku = `${formData.reference || 'VAR'}-${randomPart}`;
    }

    const variante = {
      ...newVariante,
      sku,
      prix_total: parseFloat(formData.prix_produit) + parseFloat(newVariante.prix_supplement || 0),
      imagePreview: varianteImagePreview
    };

    setVariantes([...variantes, variante]);
    setNewVariante({
      sku: '',
      prix_supplement: 0,
      quantite: 0,
      image: null,
      valeurs: {}
    });
    setVarianteImagePreview(null);
    setShowVarianteModal(false);
  };

  const removeVariante = (index) => {
    const newVariantes = [...variantes];
    newVariantes.splice(index, 1);
    setVariantes(newVariantes);
  };

  const submitForm = async () => {
    setShowConfirmModal(false);

    try {
      setLoading(true);
      setError(null);
      setSuccess(false);

      // Préparer les attributs sélectionnés avec leurs valeurs
      const attributsToSend = selectedAttributs
        .filter((attributId) => attributValues[attributId])
        .map((attributId) => ({
          attribut_id: attributId,
          valeur: attributValues[attributId]
        }));

      const formDataToSend = new FormData();
      formDataToSend.append('nom_produit', formData.nom_produit);
      formDataToSend.append('description_produit', formData.description_produit);
      formDataToSend.append('prix_produit', formData.prix_produit);
      formDataToSend.append('quantite_produit', formData.quantite_produit);
      formDataToSend.append('marque_id', formData.marque_id);
      formDataToSend.append('sous_sous_categorie_id', formData.sous_sous_categorie_id);
      formDataToSend.append('reference', formData.reference || '');

      // Ajouter les champs spécifiques si définis
      if (categorySpecificFields.poids && formData.poids) {
        formDataToSend.append('poids', formData.poids);
      }
      if (categorySpecificFields.dimensions && formData.dimensions) {
        formDataToSend.append('dimensions', formData.dimensions);
      }
      if (categorySpecificFields.couleur && formData.couleur) {
        formDataToSend.append('couleur', formData.couleur);
      }
      if (categorySpecificFields.materiau && formData.materiau) {
        formDataToSend.append('materiau', formData.materiau);
      }

      // Ajouter les attributs
      attributsToSend.forEach((attr, index) => {
        formDataToSend.append(`attributs[${index}][attribut_id]`, attr.attribut_id);
        formDataToSend.append(`attributs[${index}][valeur]`, attr.valeur);
      });

      // Ajouter les variantes
      variantes.forEach((variante, index) => {
        formDataToSend.append(`variantes[${index}][sku]`, variante.sku);
        formDataToSend.append(`variantes[${index}][prix_supplement]`, variante.prix_supplement);
        formDataToSend.append(`variantes[${index}][quantite]`, variante.quantite);

        // Ajouter les valeurs d'attributs pour la variante
        Object.entries(variante.valeurs).forEach(([attributId, valeur], valIndex) => {
          formDataToSend.append(`variantes[${index}][valeurs][${valIndex}][attribut_id]`, attributId);
          formDataToSend.append(`variantes[${index}][valeurs][${valIndex}][valeur]`, valeur);
        });
      });

      const response = await fetch(`${API_URL}/produits`, {
        method: 'POST',
        body: formDataToSend
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Erreur lors de l'ajout du produit");
      }

      // Get the created product's ID
      const productData = await response.json();
      const productId = productData?.data?.id || productData?.id;
      if (!productId) throw new Error("Impossible de récupérer l'ID du produit créé");

      // Upload all selected images (multi-image)
      if (images.length > 0) {
        const imagesFormData = new FormData();
        images.forEach((img) => {
          imagesFormData.append('images[]', img);
        });
        imagesFormData.append('model_type', 'produit');
        imagesFormData.append('model_id', productId);
        imagesFormData.append('primary_index', primaryImageIndex);
        try {
          const imageRes = await fetch(`${API_URL}/images/upload-multiple`, {
            method: 'POST',
            body: imagesFormData
          });
          if (!imageRes.ok) {
            const errorImg = await imageRes.json();
            throw new Error(errorImg.message || "Erreur lors de l'upload des images du produit");
          }
        } catch (imgErr) {
          setError(imgErr.message);
          setLoading(false);
          return;
        }
      }

      // Upload images for each variant if present
      for (let i = 0; i < variantes.length; i++) {
        const variante = variantes[i];
        // If the variant has an image
        if (variante.image && variante.id) {
          try {
            const varianteImgForm = new FormData();
            varianteImgForm.append('model_type', 'produit_variante');
            varianteImgForm.append('model_id', variante.id);
            varianteImgForm.append('image', variante.image);
            varianteImgForm.append('is_primary', 'true');
            const varianteImgRes = await fetch(`${API_URL}/images/upload`, {
              method: 'POST',
              body: varianteImgForm
            });
            if (!varianteImgRes.ok) {
              const errorVarImg = await varianteImgRes.json();
              throw new Error(errorVarImg.message || `Erreur lors de l'upload de l'image de la variante ${variante.sku}`);
            }
          } catch (varImgErr) {
            setError(varImgErr.message);
            setLoading(false);
            return;
          }
        }
      }
      // End of try block
    } catch (err) {
      setError(err.message);
      setLoading(false);
      return;
    }

    // Réinitialisation
    setFormData({
      nom_produit: '',
      description_produit: '',
      prix_produit: '',
      quantite_produit: 0,
      marque_id: '',
      sous_sous_categorie_id: '',
      image_produit: null,
      reference: '',
      attributs: [],
      poids: '',
      dimensions: '',
      couleur: '',
      materiau: ''
    });
    setSelectedAttributs([]);
    setAttributValues({});
    setImages([]);
    setPrimaryImageIndex(0);
    setImagePreview(null);
    setCategorySpecificFields({});
    setVariantes([]);

    setSuccess(true);
    setShowSuccessModal(true);
    setCurrentStep(1);
    setLoading(false);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setShowConfirmModal(true);
  };

  const nextStep = () => {
    // Step 1 validation
    if (currentStep === 1 && (!formData.nom_produit || !formData.description_produit || !formData.prix_produit)) {
      setError('Veuillez remplir tous les champs obligatoires');
      return;
    }
    // Step 2 validation
    if (currentStep === 2 && (!formData.marque_id || !formData.sous_sous_categorie_id)) {
      setError('Veuillez sélectionner une marque et une sous-sous-catégorie');
      return;
    }
    // Step 3: you may add validation for attributs if needed
    // Step 4: you may add validation for image if needed
    setError(null);
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const renderAttributField = (attribut, isVariante = false, onChangeHandler = null, values = {}) => {
    const currentValue = isVariante ? values[attribut.id] || '' : attributValues[attribut.id] || '';
    const handleChange = onChangeHandler || ((id, val) => handleAttributChange(id, val));

    switch (attribut.type_valeur) {
      case 'select':
        return (
          <Form.Select value={currentValue} onChange={(e) => handleChange(attribut.id, e.target.value)} required={attribut.obligatoire}>
            <option value="">Sélectionner {attribut.nom.toLowerCase()}</option>
            {attribut.options?.split(',').map((opt) => (
              <option key={opt.trim()} value={opt.trim()}>
                {opt.trim()}
              </option>
            ))}
          </Form.Select>
        );
      case 'textarea':
        return (
          <Form.Control
            as="textarea"
            value={currentValue}
            onChange={(e) => handleChange(attribut.id, e.target.value)}
            rows={3}
            required={attribut.obligatoire}
          />
        );
      case 'number':
        return (
          <Form.Control
            type="number"
            value={currentValue}
            onChange={(e) => handleChange(attribut.id, e.target.value)}
            required={attribut.obligatoire}
          />
        );
      case 'boolean':
        return (
          <Form.Check
            type="switch"
            label={attribut.nom}
            checked={currentValue || false}
            onChange={(e) => handleChange(attribut.id, e.target.checked)}
          />
        );
      case 'color':
        return (
          <div className="d-flex align-items-center">
            <Form.Control
              type="color"
              value={currentValue || '#000000'}
              onChange={(e) => handleChange(attribut.id, e.target.value)}
              required={attribut.obligatoire}
              title={`Choisir une couleur pour ${attribut.nom}`}
              className="me-2"
              style={{ width: '50px', height: '38px' }}
            />
            <Form.Control
              type="text"
              value={currentValue || ''}
              onChange={(e) => handleChange(attribut.id, e.target.value)}
              placeholder="Ou saisir une valeur"
            />
          </div>
        );
      case 'date':
        return (
          <Form.Control
            type="date"
            value={currentValue || ''}
            onChange={(e) => handleChange(attribut.id, e.target.value)}
            required={attribut.obligatoire}
          />
        );
      case 'dimensions':
        return (
          <Row>
            <Col>
              <Form.Control
                type="number"
                placeholder="Longueur"
                value={currentValue?.split('x')[0] || ''}
                onChange={(e) => {
                  const width = currentValue?.split('x')[1] || '';
                  const height = currentValue?.split('x')[2] || '';
                  handleChange(attribut.id, `${e.target.value}x${width}x${height}`);
                }}
                min="0"
                step="0.1"
              />
            </Col>
            <Col>
              <Form.Control
                type="number"
                placeholder="Largeur"
                value={currentValue?.split('x')[1] || ''}
                onChange={(e) => {
                  const length = currentValue?.split('x')[0] || '';
                  const height = currentValue?.split('x')[2] || '';
                  handleChange(attribut.id, `${length}x${e.target.value}x${height}`);
                }}
                min="0"
                step="0.1"
              />
            </Col>
            <Col>
              <Form.Control
                type="number"
                placeholder="Hauteur"
                value={currentValue?.split('x')[2] || ''}
                onChange={(e) => {
                  const length = currentValue?.split('x')[0] || '';
                  const width = currentValue?.split('x')[1] || '';
                  handleChange(attribut.id, `${length}x${width}x${e.target.value}`);
                }}
                min="0"
                step="0.1"
              />
            </Col>
          </Row>
        );
      default:
        return (
          <Form.Control
            type="text"
            value={currentValue || ''}
            onChange={(e) => handleChange(attribut.id, e.target.value)}
            required={attribut.obligatoire}
          />
        );
    }
  };

  return (
    <Container className="py-4">
      <Card className="shadow-sm">
        <Card.Body>
          {error && (
            <Alert variant="danger" onClose={() => setError(null)} dismissible>
              {error}
            </Alert>
          )}

          {/* Stepper UI */}
          <div className="mb-4">
            <div className="d-flex align-items-center">
              {[1, 2, 3, 4, 5].map((step) => (
                <React.Fragment key={step}>
                  <div
                    className={`d-flex flex-column align-items-center ${currentStep > step ? 'text-primary' : currentStep === step ? 'font-weight-bold text-primary' : 'text-muted'}`}
                  >
                    <div
                      className={`rounded-circle d-flex align-items-center justify-content-center mb-2 ${currentStep > step ? 'bg-primary text-white' : currentStep === step ? 'bg-primary text-white' : 'bg-light'}`}
                      style={{ width: '40px', height: '40px' }}
                    >
                      {currentStep > step ? <span>&#10003;</span> : step}
                    </div>
                    <span className="small">
                      {step === 1 && 'Informations'}
                      {step === 2 && 'Catégorie'}
                      {step === 3 && 'Attributs'}
                      {step === 4 && 'Image'}
                      {step === 5 && 'Variantes'}
                    </span>
                  </div>
                  {step < 5 && (
                    <div className={`flex-grow-1 mx-2 ${currentStep > step ? 'bg-primary' : 'bg-light'}`} style={{ height: '2px' }}></div>
                  )}
                </React.Fragment>
              ))}
            </div>
          </div>

          {/* Modular Step Components */}
          {currentStep === 1 && <ProductStep1 formData={formData} setFormData={setFormData} error={error} />}
          {currentStep === 2 && (
            <ProductStep2
              formData={formData}
              setFormData={setFormData}
              brands={marques}
              categories={categories}
              sousCategories={sousCategories}
              sousSousCategories={sousSousCategories}
              error={error}
            />
          )}
          {currentStep === 3 && (
            <ProductStep3
              attributs={attributs}
              attributsGroups={attributsGroups}
              selectedAttributs={selectedAttributs}
              setSelectedAttributs={setSelectedAttributs}
              attributFields={Array.isArray(attributs) ? attributs : []}
              attributValues={attributValues}
              setAttributValues={setAttributValues}
              categorySpecificFields={categorySpecificFields}
              setCategorySpecificFields={setCategorySpecificFields}
              error={error}
            />
          )}
          {currentStep === 4 && (
            <ProductStep4
              images={images}
              setImages={setImages}
              primaryImageIndex={primaryImageIndex}
              setPrimaryImageIndex={setPrimaryImageIndex}
              error={error}
            />
          )}
          {currentStep === 5 && (
            <ProductStepVariants
              variantes={variantes}
              setVariantes={setVariantes}
              newVariante={newVariante}
              setNewVariante={setNewVariante}
              showVarianteModal={showVarianteModal}
              setShowVarianteModal={setShowVarianteModal}
              handleVarianteImageChange={handleVarianteImageChange}
              varianteImagePreview={varianteImagePreview}
              setVarianteImagePreview={setVarianteImagePreview}
              attributs={attributs}
              handleVarianteAttributChange={handleVarianteAttributChange}
              addVariante={addVariante}
              removeVariante={removeVariante}
              error={error}
            />
          )}

          {/* Navigation Buttons */}
          <div className="d-flex justify-content-between mt-4">
            {currentStep > 1 && (
              <Button variant="outline-secondary" onClick={prevStep}>
                <i className="fas fa-arrow-left me-1"></i> Précédent
              </Button>
            )}
            <div className="ms-auto">
              {currentStep < totalSteps && (
                <Button variant="primary" onClick={nextStep}>
                  Suivant <i className="fas fa-arrow-right ms-1"></i>
                </Button>
              )}
              {currentStep === totalSteps && (
                <Button variant="success" type="submit" onClick={submitForm} disabled={loading}>
                  {loading ? (
                    <>
                      <Spinner as="span" animation="border" size="sm" role="status" aria-hidden="true" className="me-2" />
                      Enregistrement en cours...
                    </>
                  ) : (
                    <>
                      <i className="fas fa-check me-1"></i> Ajouter le produit
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </Card.Body>
      </Card>

      {/* Loading Modal */}
      <Modal show={loading} backdrop="static" keyboard={false} centered>
        <Modal.Body className="text-center py-4">
          <Spinner animation="border" variant="primary" className="mb-3" />
          <h5>Enregistrement en cours...</h5>
          <p className="text-muted mb-0">Veuillez patienter pendant que nous sauvegardons votre produit.</p>
        </Modal.Body>
      </Modal>

      {/* Success Modal */}
      <Modal show={showSuccessModal} onHide={() => setShowSuccessModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title className="text-success">
            <i className="fas fa-check-circle me-2"></i>
            Produit créé avec succès !
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="text-center py-4">
          <div className="mb-3">
            <i className="fas fa-check-circle text-success" style={{ fontSize: '3rem' }}></i>
          </div>
          <h5>Votre produit a été ajouté avec succès !</h5>
          <p className="text-muted">Le produit et ses images ont été sauvegardés dans la base de données.</p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="primary" onClick={() => setShowSuccessModal(false)}>
            <i className="fas fa-plus me-2"></i>
            Ajouter un autre produit
          </Button>
          <Button variant="outline-secondary" onClick={() => setShowSuccessModal(false)}>
            Fermer
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default AjoutProduit;
