import React, { useState, useEffect } from 'react';
import { Tabs, Tab, Card, Container, Row, Col, Button, Form, Badge, Modal, Table, Dropdown, Al<PERSON>, Spinner } from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css';
import { fetchOrders, fetchOrderById, updateOrderStatus, deleteOrder } from '../../services/orderService';

const fontStyle = {
  fontFamily: "'Montserrat', sans-serif",
  fontWeight: 500
};

const colors = {
  primaryDark: '#2a3f5f',
  primaryLight: '#3a537b',
  accent: '#3a8dde',
  partner: '#e74c3c',
  loyal: '#f39c12',
  regular: '#2ecc71',
  pending: '#f39c12',
  completed: '#2ecc71',
  cancelled: '#e74c3c'
};

const OrderManagement = () => {
  // États
  const [activeTab, setActiveTab] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [yearFilter, setYearFilter] = useState('all');
  const [showOrderModal, setShowOrderModal] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [showInvoice, setShowInvoice] = useState(false);
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Clients
  const clients = [
    {
      id: 1,
      name: 'Chaima Bentaher',
      email: '<EMAIL>',
      phone: '+216 55 123 456',
      category: 'partner',
      partnerType: 'hotel',
      company: 'Hôtel La Maison Blanche',
      lastPurchase: '2023-11-15',
      totalPurchases: 6,
      location: 'Sfax',
      sellerGroup: '',
      sellerBrand: ''
    },
    {
      id: 2,
      name: 'Mohamed Bouaziz',
      email: '<EMAIL>',
      phone: '+216 22 987 654',
      category: 'partner',
      partnerType: 'hotel',
      company: 'Hôtel Les Palmiers',
      lastPurchase: '2023-12-02',
      totalPurchases: 8,
      location: 'Sfax',
      sellerGroup: '',
      sellerBrand: ''
    },
    {
      id: 3,
      name: 'Sirine Khalfaoui',
      email: '<EMAIL>',
      phone: '+216 98 765 432',
      category: 'regular',
      partnerType: '',
      company: '',
      lastPurchase: '2023-10-20',
      totalPurchases: 2,
      location: 'Monastir',
      sellerGroup: '',
      sellerBrand: ''
    },
    {
      id: 4,
      name: 'Chaima Chelly',
      email: '<EMAIL>',
      phone: '+216 54 321 987',
      category: 'partner',
      partnerType: 'hotel',
      company: 'Hôtel Méditerranée',
      lastPurchase: '2023-11-28',
      totalPurchases: 15,
      location: 'Hammamet',
      sellerGroup: '',
      sellerBrand: ''
    },
    {
      id: 5,
      name: 'Youssef Ben Ahmed',
      email: '<EMAIL>',
      phone: '+216 21 654 987',
      category: 'loyal',
      partnerType: '',
      company: 'Déco Maison',
      lastPurchase: '2023-12-10',
      totalPurchases: 5,
      location: 'Tunis',
      sellerGroup: '',
      sellerBrand: ''
    },
    {
      id: 6,
      name: 'Islem Trigui',
      email: '<EMAIL>',
      phone: '+216 92 876 543',
      category: 'regular',
      partnerType: '',
      company: '',
      lastPurchase: '2023-09-15',
      totalPurchases: 3,
      location: 'Sousse',
      sellerGroup: '',
      sellerBrand: ''
    },
    {
      id: 7,
      name: 'jihenLine Tunis',
      email: '<EMAIL>',
      phone: '+216 71 123 456',
      category: 'partner',
      partnerType: '',
      company: '',
      lastPurchase: '2023-12-15',
      totalPurchases: 25,
      location: 'Tunis',
      sellerGroup: 'jihenLine GROUPE',
      sellerBrand: 'Luxoria'
    },
    {
      id: 8,
      name: 'jihenLine Sfax',
      email: '<EMAIL>',
      phone: '+216 74 987 654',
      category: 'partner',
      partnerType: '',
      company: '',
      lastPurchase: '2023-12-10',
      totalPurchases: 18,
      location: 'Sfax',
      sellerGroup: 'jihenLine GROUPE',
      sellerBrand: 'Modernio'
    },
    {
      id: 9,
      name: 'jihenLine Monastir',
      email: '<EMAIL>',
      phone: '+216 73 456 789',
      category: 'partner',
      partnerType: '',
      company: '',
      lastPurchase: '2023-11-25',
      totalPurchases: 12,
      location: 'Monastir',
      sellerGroup: 'jihenLine GROUPE',
      sellerBrand: 'Eleganto'
    },
    {
      id: 10,
      name: 'Hosni Chelly',
      email: '<EMAIL>',
      phone: '+216 22 987 654',
      category: 'loyal',
      partnerType: '',
      company: 'Déco Moderne',
      lastPurchase: '2023-12-02',
      totalPurchases: 8,
      location: 'Tunis',
      sellerGroup: '',
      sellerBrand: ''
    }
  ];

  // Articles de linge de maison
  const products = [
    { id: 1, name: 'Drap housse 200x200 cm coton égyptien', price: 120, category: 'literie' },
    { id: 2, name: 'Housse de couette 240x220 cm satin', price: 150, category: 'literie' },
    { id: 3, name: "Taie d'oreiller 50x70 cm jacquard", price: 45, category: 'literie' },
    { id: 4, name: 'Serviette de bain 100x150 cm éponge', price: 65, category: 'bain' },
    { id: 5, name: 'Peignoir de bain adulte coton', price: 180, category: 'bain' },
    { id: 6, name: 'Tapis de bain antidérapant', price: 85, category: 'bain' },
    { id: 7, name: 'Nappe rectangulaire 160x220 cm lin', price: 220, category: 'table' },
    { id: 8, name: 'Jeu de 12 serviettes de table', price: 95, category: 'table' },
    { id: 9, name: 'Rideau doublé 140x250 cm', price: 175, category: 'décoration' },
    { id: 10, name: 'Plaid 150x200 cm laine', price: 130, category: 'décoration' }
  ];

  // Load orders from API
  const loadOrders = async () => {
    try {
      setLoading(true);
      setError('');

      const params = {
        page: currentPage,
        per_page: 10
      };

      if (searchTerm) {
        params.search = searchTerm;
      }

      const ordersData = await fetchOrders(params);
      console.log('Loaded orders:', ordersData);

      setOrders(ordersData || []);
    } catch (err) {
      console.error('Error loading orders:', err);
      setError('Erreur lors du chargement des commandes: ' + err.message);
      setOrders([]);
    } finally {
      setLoading(false);
    }
  };

  // Load orders on component mount and when dependencies change
  useEffect(() => {
    loadOrders();
  }, [currentPage, searchTerm]);

  // Obtenir tous les années disponibles
  const availableYears = [...new Set(orders.map((order) => new Date(order.created_at || order.date).getFullYear()))].sort((a, b) => b - a);

  // Filtrer les commandes selon l'onglet actif, l'année et la recherche
  const filteredOrders = orders.filter((order) => {
    const orderDate = order.created_at || order.date;
    const orderYear = new Date(orderDate).getFullYear().toString();
    const matchesStatus = activeTab === 'all' || order.status === activeTab;
    const matchesYear = yearFilter === 'all' || orderYear === yearFilter;
    const matchesSearch =
      searchTerm === '' ||
      (order.order_number || order.orderNumber || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (order.customer_name || order.client?.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (order.customer_email || order.client?.email || '').toLowerCase().includes(searchTerm.toLowerCase());
    return matchesStatus && matchesSearch && matchesYear;
  });

  // Formatage de la date
  const formatDate = (dateString) => {
    const options = { day: '2-digit', month: '2-digit', year: 'numeric' };
    return new Date(dateString).toLocaleDateString('fr-FR', options);
  };

  // Obtenir le libellé du statut
  const getStatusLabel = (status) => {
    switch (status) {
      case 'pending':
        return 'En attente';
      case 'completed':
        return 'Complétée';
      case 'cancelled':
        return 'Annulée';
      default:
        return '';
    }
  };

  // Obtenir la couleur du statut
  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return colors.pending;
      case 'completed':
        return colors.completed;
      case 'cancelled':
        return colors.cancelled;
      default:
        return '';
    }
  };

  // Obtenir la couleur de la catégorie client
  const getClientCategoryColor = (category) => {
    switch (category) {
      case 'partner':
        return colors.partner;
      case 'loyal':
        return colors.loyal;
      case 'regular':
        return colors.regular;
      default:
        return colors.primaryLight;
    }
  };

  // Obtenir le libellé de la catégorie client
  const getClientCategoryLabel = (category) => {
    switch (category) {
      case 'partner':
        return 'Partenaire';
      case 'loyal':
        return 'Fidèle';
      case 'regular':
        return 'Régulier';
      default:
        return '';
    }
  };

  // Ouvrir le modal de détail de commande
  const openOrderDetails = (order) => {
    setSelectedOrder(order);
    setShowOrderModal(true);
  };

  // Formater le prix
  const formatPrice = (price) => {
    return new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(price);
  };

  // Composant de facture
  const Invoice = ({ order }) => {
    if (!order) return null;

    return (
      <div className="p-4 mt-3" style={{ border: '1px solid #dee2e6', borderRadius: '5px' }}>
        <div className="d-flex justify-content-between align-items-start mb-4">
          <div>
            <h2 style={{ color: colors.primaryDark }}>FACTURE</h2>
            <p className="mb-0">Numéro: {order.orderNumber}</p>
            <p>Date: {formatDate(order.date)}</p>
          </div>
          <div className="text-end">
            <h4>LINGE DE MAISON PREMIUM</h4>
            <p className="mb-0">123 Avenue Habib Bourguiba</p>
            <p className="mb-0">1000 Tunis, Tunisie</p>
            <p className="mb-0"><EMAIL></p>
            <p>+216 71 234 567</p>
          </div>
        </div>

        <div className="row mb-4">
          <div className="col-md-6">
            <div className="p-3" style={{ backgroundColor: '#f8f9fa', borderRadius: '5px' }}>
              <h5>Facturé à:</h5>
              <p className="mb-0">
                <strong>{order.client.name}</strong>
              </p>
              {order.client.company && <p className="mb-0">{order.client.company}</p>}
              <p className="mb-0">{order.client.email}</p>
              <p>{order.client.phone}</p>
            </div>
          </div>
          <div className="col-md-6">
            <div className="p-3" style={{ backgroundColor: '#f8f9fa', borderRadius: '5px' }}>
              <h5>Détails:</h5>
              <p className="mb-0">
                <strong>Statut:</strong> {getStatusLabel(order.status)}
              </p>
              <p className="mb-0">
                <strong>Mode de paiement:</strong> Virement bancaire
              </p>
              <p>
                <strong>Livraison:</strong> {order.client.location}
              </p>
            </div>
          </div>
        </div>

        <Table bordered>
          <thead className="bg-light">
            <tr>
              <th>Article</th>
              <th className="text-center">Quantité</th>
              <th className="text-end">Prix unitaire</th>
              <th className="text-end">Total</th>
            </tr>
          </thead>
          <tbody>
            {order.items.map((item) => (
              <tr key={item.id}>
                <td>{item.name}</td>
                <td className="text-center">{item.quantity}</td>
                <td className="text-end">{formatPrice(item.price)}</td>
                <td className="text-end">{formatPrice(item.total)}</td>
              </tr>
            ))}
          </tbody>
          <tfoot>
            <tr>
              <td colSpan="3" className="text-end">
                <strong>Total HT:</strong>
              </td>
              <td className="text-end">{formatPrice(order.total / 1.19)}</td>
            </tr>
            <tr>
              <td colSpan="3" className="text-end">
                <strong>TVA (19%):</strong>
              </td>
              <td className="text-end">{formatPrice(order.total - order.total / 1.19)}</td>
            </tr>
            <tr>
              <td colSpan="3" className="text-end">
                <strong>Total TTC:</strong>
              </td>
              <td className="text-end">
                <strong>{formatPrice(order.total)}</strong>
              </td>
            </tr>
          </tfoot>
        </Table>

        <div className="mt-4" style={{ borderTop: '1px solid #dee2e6', paddingTop: '20px' }}>
          <h5>Conditions & Notes</h5>
          <p className="small">Merci pour votre confiance. Pour toute question concernant cette facture, veuillez nous contacter.</p>
          <p className="small">
            <strong>Retour:</strong> Les articles non utilisés peuvent être retournés sous 14 jours.
          </p>
        </div>

        <div className="text-center mt-4">
          <Button onClick={() => window.print()} variant="primary">
            <i className="fas fa-print me-2"></i>
            Imprimer cette facture
          </Button>
        </div>
      </div>
    );
  };

  return (
    <>
      {/* Interface principale */}
      <div className={showInvoice ? 'd-none' : ''}>
        <Container className="py-4" style={fontStyle}>
          {/* Titre principal */}
          <div className="text-center mb-5 position-relative">
            <h1
              className="mb-3"
              style={{
                fontSize: '2.2rem',
                fontWeight: 600,
                color: colors.primaryDark,
                letterSpacing: '1px'
              }}
            >
              GESTION DES COMMANDES
            </h1>
            <div
              className="mx-auto"
              style={{
                height: '3px',
                width: '120px',
                background: 'linear-gradient(90deg, rgba(58,83,155,0.2) 0%, rgba(58,83,155,1) 50%, rgba(58,83,155,0.2) 100%)',
                borderRadius: '3px'
              }}
            />
          </div>

          {/* Error Alert */}
          {error && (
            <Alert variant="danger" dismissible onClose={() => setError('')} className="mb-4">
              <Alert.Heading>Erreur</Alert.Heading>
              <p>{error}</p>
            </Alert>
          )}

          {/* Barre de recherche et filtres */}
          <div className="d-flex justify-content-between align-items-center mb-4 flex-wrap">
            <Form.Group style={{ width: '300px' }}>
              <Form.Control
                type="text"
                placeholder="Rechercher une commande..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                disabled={loading}
                style={{
                  borderRadius: '20px',
                  padding: '10px 20px'
                }}
              />
            </Form.Group>

            <div className="d-flex flex-wrap mt-3 mt-md-0">
              <Dropdown className="me-2">
                <Dropdown.Toggle variant="outline-secondary" id="year-filter">
                  {yearFilter === 'all' ? 'Toutes les années' : `Année ${yearFilter}`}
                </Dropdown.Toggle>
                <Dropdown.Menu>
                  <Dropdown.Item onClick={() => setYearFilter('all')}>Toutes les années</Dropdown.Item>
                  {availableYears.map((year) => (
                    <Dropdown.Item key={year} onClick={() => setYearFilter(year.toString())}>
                      {year}
                    </Dropdown.Item>
                  ))}
                </Dropdown.Menu>
              </Dropdown>

              <Badge bg="light" text="dark">
                {filteredOrders.length} commande(s) trouvée(s)
              </Badge>
            </div>
          </div>

          {/* Onglets de statuts */}
          <Tabs
            activeKey={activeTab}
            onSelect={(k) => setActiveTab(k)}
            className="mb-4"
            style={{
              borderBottom: '2px solid #dee2e6'
            }}
          >
            <Tab eventKey="all" title={<span style={fontStyle}>Toutes les commandes</span>} />
            <Tab eventKey="pending" title={<span style={fontStyle}>En attente</span>} />
            <Tab eventKey="completed" title={<span style={fontStyle}>Complétées</span>} />
            <Tab eventKey="cancelled" title={<span style={fontStyle}>Annulées</span>} />
          </Tabs>

          {/* Liste des commandes */}
          {filteredOrders.length > 0 ? (
            <Row className="g-4">
              {filteredOrders.map((order) => (
                <Col key={order.id} xs={12} md={6} lg={4}>
                  <Card className="h-100 border-0 shadow-sm">
                    <Card.Body>
                      <div className="d-flex justify-content-between align-items-start mb-3">
                        <div>
                          <Card.Title
                            style={{
                              fontSize: '1.2rem',
                              fontWeight: 600,
                              color: colors.primaryDark,
                              marginBottom: '0.5rem'
                            }}
                          >
                            {order.orderNumber}
                          </Card.Title>
                          <div className="d-flex align-items-center gap-2">
                            <Badge
                              style={{
                                backgroundColor: getStatusColor(order.status),
                                fontSize: '0.75rem',
                                padding: '0.35em 0.65em'
                              }}
                            >
                              {getStatusLabel(order.status)}
                            </Badge>
                            <Badge
                              style={{
                                backgroundColor: getClientCategoryColor(order.client.category),
                                fontSize: '0.75rem',
                                padding: '0.35em 0.65em'
                              }}
                            >
                              {getClientCategoryLabel(order.client.category)}
                            </Badge>
                          </div>
                        </div>
                        <div className="text-end">
                          <div
                            style={{
                              fontWeight: 'bold',
                              color: colors.primaryDark
                            }}
                          >
                            {formatPrice(order.total)}
                          </div>
                          <small className="text-muted">{formatDate(order.date)}</small>
                        </div>
                      </div>

                      <div className="mb-3">
                        <div className="d-flex align-items-center mb-2">
                          <i className="fas fa-user me-2" style={{ color: colors.accent }}></i>
                          <span>{order.client.name}</span>
                        </div>
                        {order.client.company && (
                          <div className="d-flex align-items-center mb-2">
                            <i className="fas fa-building me-2" style={{ color: colors.accent }}></i>
                            <span>{order.client.company}</span>
                          </div>
                        )}
                        <div className="d-flex align-items-center mb-2">
                          <i className="fas fa-map-marker-alt me-2" style={{ color: colors.accent }}></i>
                          <span>{order.client.location}</span>
                        </div>
                      </div>

                      <div className="small mb-3">
                        <strong>Articles:</strong>
                        <ul className="ps-3 mb-0">
                          {order.items.slice(0, 2).map((item) => (
                            <li key={item.id}>
                              {item.name.split(' ').slice(0, 3).join(' ')}... ({item.quantity})
                            </li>
                          ))}
                          {order.items.length > 2 && <li>+ {order.items.length - 2} autres articles</li>}
                        </ul>
                      </div>
                    </Card.Body>
                    <Card.Footer className="bg-white border-0">
                      <div className="d-flex justify-content-end">
                        <Button variant="outline-primary" size="sm" className="me-2" onClick={() => openOrderDetails(order)}>
                          <i className="fas fa-eye me-1"></i> Voir
                        </Button>
                        <Button
                          variant={order.status === 'completed' ? 'outline-success' : 'outline-secondary'}
                          size="sm"
                          disabled={order.status !== 'completed'}
                          onClick={() => {
                            setSelectedOrder(order);
                            setShowInvoice(true);
                          }}
                        >
                          <i className="fas fa-file-pdf me-1"></i> Facture
                        </Button>
                      </div>
                    </Card.Footer>
                  </Card>
                </Col>
              ))}
            </Row>
          ) : (
            <Card className="text-center py-5 border-0 shadow-sm">
              <Card.Body>
                <i className="fas fa-receipt mb-3" style={{ fontSize: '3rem', color: colors.primaryLight }}></i>
                <Card.Title style={{ color: colors.primaryLight }}>Aucune commande trouvée</Card.Title>
                <Card.Text className="text-muted">
                  {searchTerm ? "Essayez avec d'autres termes de recherche" : 'Aucune commande ne correspond aux critères sélectionnés'}
                </Card.Text>
                <Button variant="primary" className="mt-3">
                  <i className="fas fa-plus me-2"></i> Nouvelle commande
                </Button>
              </Card.Body>
            </Card>
          )}

          {/* Modal de détail de commande */}
          <Modal show={showOrderModal} onHide={() => setShowOrderModal(false)} size="lg" centered>
            <Modal.Header closeButton>
              <Modal.Title>Détails de la commande {selectedOrder?.orderNumber}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              {selectedOrder && (
                <>
                  <div className="mb-4">
                    <div className="d-flex justify-content-between align-items-center">
                      <h5 className="mb-0">Informations générales</h5>
                      <div className="d-flex gap-2">
                        <Badge
                          style={{
                            backgroundColor: getStatusColor(selectedOrder.status),
                            fontSize: '0.75rem',
                            padding: '0.5em 0.75em'
                          }}
                        >
                          {getStatusLabel(selectedOrder.status)}
                        </Badge>
                        <Badge
                          style={{
                            backgroundColor: getClientCategoryColor(selectedOrder.client.category),
                            fontSize: '0.75rem',
                            padding: '0.5em 0.75em'
                          }}
                        >
                          {getClientCategoryLabel(selectedOrder.client.category)}
                        </Badge>
                      </div>
                    </div>
                    <hr />
                    <Row>
                      <Col md={6}>
                        <p className="mb-1">
                          <strong>Date:</strong> {formatDate(selectedOrder.date)}
                        </p>
                        <p className="mb-1">
                          <strong>Montant total:</strong> {formatPrice(selectedOrder.total)}
                        </p>
                        <p className="mb-1">
                          <strong>Localisation:</strong> {selectedOrder.client.location}
                        </p>
                      </Col>
                      <Col md={6}>
                        <p className="mb-1">
                          <strong>Client:</strong> {selectedOrder.client.name}
                        </p>
                        {selectedOrder.client.company && (
                          <p className="mb-1">
                            <strong>Société:</strong> {selectedOrder.client.company}
                          </p>
                        )}
                        <p className="mb-1">
                          <strong>Email:</strong> {selectedOrder.client.email}
                        </p>
                        <p className="mb-1">
                          <strong>Téléphone:</strong> {selectedOrder.client.phone}
                        </p>
                      </Col>
                    </Row>
                  </div>

                  <div className="mb-4">
                    <h5>Articles commandés</h5>
                    <hr />
                    <Table responsive striped>
                      <thead>
                        <tr>
                          <th>Article</th>
                          <th className="text-center">Quantité</th>
                          <th className="text-end">Prix unitaire</th>
                          <th className="text-end">Total</th>
                        </tr>
                      </thead>
                      <tbody>
                        {selectedOrder.items.map((item) => (
                          <tr key={item.id}>
                            <td>{item.name}</td>
                            <td className="text-center">{item.quantity}</td>
                            <td className="text-end">{formatPrice(item.price)}</td>
                            <td className="text-end">{formatPrice(item.total)}</td>
                          </tr>
                        ))}
                      </tbody>
                      <tfoot>
                        <tr>
                          <td colSpan="3" className="text-end">
                            <strong>Total:</strong>
                          </td>
                          <td className="text-end">
                            <strong>{formatPrice(selectedOrder.total)}</strong>
                          </td>
                        </tr>
                      </tfoot>
                    </Table>
                  </div>
                </>
              )}
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={() => setShowOrderModal(false)}>
                Fermer
              </Button>
              {selectedOrder?.status === 'completed' && (
                <Button
                  variant="success"
                  onClick={() => {
                    setShowOrderModal(false);
                    setShowInvoice(true);
                  }}
                >
                  <i className="fas fa-file-pdf me-2"></i>
                  Voir la facture
                </Button>
              )}
            </Modal.Footer>
          </Modal>
        </Container>
      </div>

      {/* Page de facture */}
      {showInvoice && selectedOrder && (
        <Container className="py-4" style={fontStyle}>
          <div className="d-flex justify-content-between mb-4">
            <Button variant="outline-secondary" onClick={() => setShowInvoice(false)}>
              <i className="fas fa-arrow-left me-2"></i>
              Retour aux commandes
            </Button>
            <Button variant="primary" onClick={() => window.print()}>
              <i className="fas fa-print me-2"></i>
              Imprimer
            </Button>
          </div>

          <Invoice order={selectedOrder} />
        </Container>
      )}

      {/* Styles pour l'impression */}
      <style>{`
        @media print {
          body * {
            visibility: hidden;
          }
          .container * {
            visibility: visible;
          }
          button {
            display: none !important;
          }
        }
      `}</style>
    </>
  );
};

export default OrderManagement;
