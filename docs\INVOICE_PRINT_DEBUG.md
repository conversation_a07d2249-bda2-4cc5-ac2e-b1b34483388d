# 🔍 Debug Impression Factures - Diagnostic

## 🎯 **Problème Identifié**

L'impression des factures ne fonctionne pas correctement alors que l'impression des commandes fonctionne. J'ai ajouté des logs de debug pour identifier le problème.

## 🔧 **Logs de Debug Ajoutés**

### **1. 📄 Page Factures (Invoices.jsx)**
```javascript
// Handle print invoice
const handlePrintInvoice = (invoice) => {
  console.log('📄 handlePrintInvoice called with:', invoice);
  printInvoice(invoice);
};
```

### **2. 🖨️ Hook d'Impression (useProfessionalPrint.js)**
```javascript
const printInvoice = (invoiceData) => {
  console.log('🖨️ printInvoice called with data:', invoiceData);
  setIsPrinting(true);
  
  // ... génération du contenu ...
  
  const printWindow = window.open('', '_blank');
  console.log('🖨️ Print window opened:', !!printWindow);
  if (printWindow) {
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();
    console.log('🖨️ Content written to print window, starting print...');
    
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
      setIsPrinting(false);
      console.log('🖨️ Print completed and window closed');
    }, 500);
  } else {
    console.error('❌ Failed to open print window');
    setIsPrinting(false);
  }
};
```

## 🧪 **Test de Diagnostic**

### **Pour tester l'impression des factures :**

1. **Accédez à** : http://localhost:3000/app/invoices
2. **Ouvrez la console** : F12 → Console
3. **Cliquez** : Icône d'impression sur une facture
4. **Observez les logs** :
   - `📄 handlePrintInvoice called with:` → Données de facture
   - `🖨️ printInvoice called with data:` → Données reçues par le hook
   - `🖨️ Print window opened:` → true/false
   - `🖨️ Content written to print window` → Contenu écrit
   - `🖨️ Print completed` → Impression terminée

### **Logs Attendus (Fonctionnement Normal) :**
```
📄 handlePrintInvoice called with: {
  id: 123,
  invoice_number: "FAC-0123",
  amount: 150.000,
  order: { ... },
  methode_paiement: "cash_on_delivery",
  ...
}
🖨️ printInvoice called with data: { ... }
🖨️ Print window opened: true
🖨️ Content written to print window, starting print...
🖨️ Print completed and window closed
```

### **Problèmes Possibles :**

#### **❌ Données Manquantes**
```
📄 handlePrintInvoice called with: undefined
🖨️ printInvoice called with data: undefined
```
**Solution** : Vérifier que les données de facture sont correctement passées

#### **❌ Fenêtre Bloquée**
```
🖨️ Print window opened: false
❌ Failed to open print window
```
**Solution** : Vérifier les bloqueurs de popup du navigateur

#### **❌ Erreur de Contenu**
```
🖨️ Print window opened: true
🖨️ Content written to print window, starting print...
(Pas de "Print completed")
```
**Solution** : Vérifier le contenu HTML généré

## 🔍 **Problèmes Potentiels Identifiés**

### **1. 🏗️ Structure des Données**

La page des factures utilise une structure de données complexe :
```javascript
const invoiceObject = {
  ...payment,
  order: orderData,
  invoice_number: `FAC-${payment.id.toString().padStart(4, '0')}`,
  invoice_date: payment.created_at,
  amount: payment.montant,
  status: payment.status || 'completed',
  methode_paiement: finalPaymentMethod
};
```

**Problème Possible** : Les champs peuvent ne pas correspondre à ce que le hook d'impression attend.

### **2. 🔗 Champs Attendus vs Reçus**

#### **Hook d'Impression Attend :**
```javascript
// Template utilise ces champs :
invoiceData.invoice_number
invoiceData.id
invoiceData.invoice_date
invoiceData.created_at
invoiceData.status
invoiceData.order?.numero_commande
invoiceData.order?.user?.name
invoiceData.order?.user?.email
invoiceData.methode_paiement
invoiceData.amount
invoiceData.montant
```

#### **Page Factures Fournit :**
```javascript
// Structure de l'objet facture :
{
  ...payment,           // Données de paiement originales
  order: orderData,     // Données de commande
  invoice_number: "FAC-XXXX",
  invoice_date: payment.created_at,
  amount: payment.montant,
  status: payment.status || 'completed',
  methode_paiement: finalPaymentMethod
}
```

### **3. 🚨 Problèmes Bootstrap dans Modal**

La page des factures utilise encore du code Bootstrap dans le modal :
```jsx
<Modal.Header closeButton className="bg-primary text-white">
<Modal.Title className="d-flex align-items-center">
<Modal.Body className="p-0" style={{ maxHeight: '70vh', overflowY: 'auto' }}>
```

**Problème** : Ces composants n'existent pas en Material-UI et peuvent causer des erreurs.

## 🔧 **Solutions Proposées**

### **1. 🔍 Vérification des Données**

Ajoutons une validation dans le hook d'impression :
```javascript
const printInvoice = (invoiceData) => {
  console.log('🖨️ printInvoice called with data:', invoiceData);
  
  // Validation des données
  if (!invoiceData) {
    console.error('❌ No invoice data provided');
    return;
  }
  
  if (!invoiceData.amount && !invoiceData.montant) {
    console.error('❌ No amount found in invoice data');
    return;
  }
  
  console.log('✅ Invoice data validation passed');
  setIsPrinting(true);
  // ... reste du code
};
```

### **2. 🛠️ Correction du Modal Bootstrap**

Remplacer le modal Bootstrap par Material-UI :
```jsx
// AVANT (Bootstrap - Erreur)
<Modal.Header closeButton className="bg-primary text-white">
  <Modal.Title>Facture {selectedInvoice?.invoice_number}</Modal.Title>
</Modal.Header>

// APRÈS (Material-UI - Correct)
<Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider', bgcolor: COLORS.primary.main }}>
  <Typography variant="h6" sx={{ color: 'white', display: 'flex', alignItems: 'center' }}>
    <FaFileInvoice style={{ marginRight: 8 }} />
    Facture {selectedInvoice?.invoice_number}
  </Typography>
</Box>
```

### **3. 🎯 Amélioration de la Gestion des Erreurs**

Ajouter une gestion d'erreur dans handlePrintInvoice :
```javascript
const handlePrintInvoice = (invoice) => {
  console.log('📄 handlePrintInvoice called with:', invoice);
  
  if (!invoice) {
    console.error('❌ No invoice provided to print');
    // Afficher un message d'erreur à l'utilisateur
    return;
  }
  
  try {
    printInvoice(invoice);
  } catch (error) {
    console.error('❌ Error printing invoice:', error);
    // Afficher un message d'erreur à l'utilisateur
  }
};
```

## 📊 **Comparaison Commandes vs Factures**

### **✅ Commandes (Fonctionne)**
- **Données simples** : Objet commande direct de l'API
- **Champs cohérents** : order.total_commande, order.produits, etc.
- **Pas de modal** : Impression directe depuis la page
- **Material-UI pur** : Pas de mélange Bootstrap

### **❌ Factures (Problème)**
- **Données complexes** : Objet construit avec payment + order
- **Champs mixtes** : amount vs montant, invoice_date vs created_at
- **Modal Bootstrap** : Composants non compatibles
- **Structure imbriquée** : invoice.order.user.name

## 🚀 **Plan de Correction**

### **Étape 1 : Diagnostic**
1. **Tester avec logs** : Voir les données dans la console
2. **Identifier le problème** : Données, fenêtre, ou contenu
3. **Vérifier les erreurs** : Console pour erreurs JavaScript

### **Étape 2 : Correction**
1. **Valider les données** : Ajouter vérifications dans le hook
2. **Corriger le modal** : Remplacer Bootstrap par Material-UI
3. **Harmoniser les champs** : Assurer compatibilité avec le template

### **Étape 3 : Test**
1. **Tester l'impression** : Vérifier que ça fonctionne
2. **Supprimer les logs** : Nettoyer le code de debug
3. **Documenter** : Mettre à jour la documentation

## 📞 **Instructions de Test**

### **Pour diagnostiquer maintenant :**
1. **Ouvrez** : http://localhost:3000/app/invoices
2. **Console** : F12 → Console
3. **Cliquez** : Icône impression sur une facture
4. **Analysez** : Les logs dans la console
5. **Rapportez** : Ce que vous voyez dans les logs

### **Logs à Rechercher :**
- ✅ `📄 handlePrintInvoice called with:` → Données reçues
- ✅ `🖨️ printInvoice called with data:` → Données traitées
- ✅ `🖨️ Print window opened: true` → Fenêtre ouverte
- ❌ `❌ Failed to open print window` → Problème de popup
- ❌ Erreurs JavaScript → Problème de code

---

**🔍 Status** : Diagnostic en cours avec logs de debug  
**🎯 Objectif** : Identifier pourquoi l'impression factures ne fonctionne pas  
**🔧 Méthode** : Logs détaillés + comparaison avec commandes  
**📊 Résultat** : Attente des logs de test utilisateur  
**🕒 Debug** : 31 Mai 2025  
**🔧 Version** : 2.14.0 (Invoice Print Debug)
