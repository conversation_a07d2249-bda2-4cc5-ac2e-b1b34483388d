# Authentification

## Introduction

Le système utilise Keycloak comme fournisseur d'identité pour gérer l'authentification et les autorisations. Les utilisateurs sont synchronisés entre Keycloak et la base de donn<PERSON>.

## Endpoints d'authentification

### Vérification du token Keycloak

```http
POST /api/auth/verify
```

Vérifie la validité d'un token Keycloak et retourne les informations de l'utilisateur.

#### Paramètres de la requête

| Paramètre | Type   | Description                                |
|-----------|--------|--------------------------------------------|
| token     | string | Token d'accès Keycloak (JWT)               |

#### Exemple de requête

```json
{
  "token": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJfT3B2Ym1..."
}
```

#### Exemple de réponse

```json
{
  "user": {
    "id": 1,
    "name": "Yousse<PERSON> Mrabet",
    "email": "<EMAIL>",
    "roles": ["client"],
    "keycloak_id": "5b6d0ab9-edf1-46d6-98e4-5a49fcb3b85a"
  },
  "token": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJfT3B2Ym1..."
}
```

## Gestion des rôles et profils de remise

### Rôles utilisateur dans Keycloak

Les rôles sont définis dans Keycloak et utilisés pour le contrôle d'accès aux fonctionnalités du système :

| Rôle       | Description                                                                |
|------------|----------------------------------------------------------------------------|
| admin      | Accès complet à toutes les fonctionnalités d'administration                |
| client     | Accès aux fonctionnalités de base pour les clients                         |
| partenaire | Accès aux fonctionnalités de partenaire (remises spéciales)                |

### Profils de remise

Les profils de remise sont utilisés pour la logique métier et le calcul des remises :

| Profil de remise | Description                                                       |
|------------------|-------------------------------------------------------------------|
| standard         | Client standard sans remise spécifique par défaut                  |
| premium          | Client bénéficiant d'une remise partenaire                         |
| affilie          | Client associé à un point de vente physique                        |
| groupe           | Client appartenant à un groupe avec une remise commune             |

### Synchronisation entre rôles Keycloak et profils de remise

Le système maintient automatiquement la cohérence entre les rôles Keycloak et les profils de remise :

- Tous les utilisateurs ont automatiquement le rôle 'client' dans Keycloak
- Les utilisateurs avec le profil 'premium' ont automatiquement le rôle 'partenaire' dans Keycloak
- Lorsqu'un rôle est ajouté ou supprimé dans Keycloak, le profil de remise est automatiquement mis à jour dans l'application
- Lorsqu'un profil de remise est modifié dans l'application, les rôles correspondants sont automatiquement mis à jour dans Keycloak

## Middleware d'authentification

Les routes protégées utilisent les middleware suivants:

### Middleware 'protected'

Vérifie que l'utilisateur est authentifié avec un token valide.

### Middleware 'role'

Vérifie que l'utilisateur possède un rôle spécifique.

Exemple d'utilisation:

```php
Route::prefix('v1')->middleware(['protected', 'role:admin'])->group(function () {
    // Routes accessibles uniquement aux administrateurs
});
```

## Flux d'authentification

1. L'utilisateur s'authentifie via Keycloak et obtient un token JWT
2. Le client (frontend) envoie ce token avec chaque requête API
3. Le backend vérifie la validité du token et les autorisations
4. Si l'utilisateur n'existe pas dans la base de données Laravel, il est créé automatiquement

## Synchronisation des utilisateurs

Les utilisateurs sont synchronisés entre Keycloak et la base de données Laravel. Lorsqu'un utilisateur s'authentifie pour la première fois, ses informations sont automatiquement importées dans la base de données Laravel.

### Processus de synchronisation

1. **Extraction des rôles du token Keycloak** :
   - Les rôles du realm sont extraits du token JWT
   - Les rôles spécifiques au client sont également extraits
   - Tous les rôles sont combinés dans un tableau unique

2. **Création ou mise à jour de l'utilisateur** :
   - Si l'utilisateur existe déjà, ses informations sont mises à jour
   - Si l'utilisateur n'existe pas, il est créé avec les informations du token
   - Le rôle 'client' est automatiquement ajouté si nécessaire

3. **Détermination du profil de remise** :
   - Le profil de remise est déterminé en fonction des rôles de l'utilisateur
   - Si l'utilisateur a le rôle 'partenaire', son profil est défini comme 'premium'
   - Sinon, le profil est défini par défaut à 'standard'

4. **Synchronisation bidirectionnelle** :
   - Lorsqu'un profil de remise est modifié dans l'application, les rôles correspondants sont mis à jour dans Keycloak
   - Lorsqu'un rôle est modifié dans Keycloak, le profil de remise est mis à jour lors de la prochaine authentification

## Sécurité

- Tous les échanges doivent se faire en HTTPS
- Les tokens ont une durée de validité limitée
- Les mots de passe ne sont jamais stockés dans la base de données Laravel (gérés par Keycloak)
- Les rôles sont vérifiés à chaque requête sur les endpoints protégés
