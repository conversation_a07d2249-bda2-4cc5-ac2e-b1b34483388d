import React, { useState, useEffect } from 'react';
import { Con<PERSON>er, <PERSON>, Col, Card, Button, Form, Table, Alert, Spinner, Badge, InputGroup, Tabs, Tab } from 'react-bootstrap';
import { FaPlus, FaPencilAlt, FaTrashAlt, FaPercentage, FaSearch, FaFilter, FaSort, FaEye, FaTag, FaLayerGroup } from 'react-icons/fa';
import ProfessionalModal from '../../ui-component/extended/ProfessionalModal';
import { FormModal, ConfirmationModal } from '../../ui-component/extended/ModalVariants';
import {
  fetchClientDiscountProfiles,
  createClientDiscountProfile,
  updateClientDiscountProfile,
  deleteClientDiscountProfile
} from '../../services/clientService';

const ClientDiscountProfiles = () => {
  // State for discount profiles
  const [discountProfiles, setDiscountProfiles] = useState([]);
  const [profileForm, setProfileForm] = useState({
    name: '',
    description: '',
    global_discount_percentage: 0,
    is_active: true,
    category_discounts: [],
    product_discounts: []
  });
  const [editingProfileId, setEditingProfileId] = useState(null);
  const [profileLoading, setProfileLoading] = useState(false);
  const [profileSubmitting, setProfileSubmitting] = useState(false);

  // UI state
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [profileToDelete, setProfileToDelete] = useState(null);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredProfiles, setFilteredProfiles] = useState([]);
  const [activeTab, setActiveTab] = useState('global');

  // Load discount profiles
  const loadDiscountProfiles = async () => {
    setProfileLoading(true);
    setError('');
    try {
      const data = await fetchClientDiscountProfiles();
      setDiscountProfiles(data);
      setFilteredProfiles(data);
    } catch (e) {
      setError(`Error loading discount profiles: ${e.message}`);
    }
    setProfileLoading(false);
  };

  // Initial data load
  useEffect(() => {
    loadDiscountProfiles();
  }, []);

  // Filter profiles when search term changes
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredProfiles(discountProfiles);
    } else {
      const filtered = discountProfiles.filter(
        (profile) =>
          profile.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (profile.description && profile.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredProfiles(filtered);
    }
  }, [searchTerm, discountProfiles]);

  // Handle profile form changes
  const handleProfileChange = (e) => {
    const { name, value, type, checked } = e.target;
    setProfileForm({
      ...profileForm,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle category discount changes
  const handleCategoryDiscountChange = (categoryId, value) => {
    const updatedDiscounts = [...profileForm.category_discounts];
    const existingIndex = updatedDiscounts.findIndex((d) => d.category_id === categoryId);

    if (existingIndex >= 0) {
      if (value === 0) {
        // Remove if discount is 0
        updatedDiscounts.splice(existingIndex, 1);
      } else {
        // Update existing
        updatedDiscounts[existingIndex].discount_percentage = value;
      }
    } else if (value > 0) {
      // Add new
      updatedDiscounts.push({
        category_id: categoryId,
        discount_percentage: value
      });
    }

    setProfileForm({
      ...profileForm,
      category_discounts: updatedDiscounts
    });
  };

  // Handle product discount changes
  const handleProductDiscountChange = (productId, value) => {
    const updatedDiscounts = [...profileForm.product_discounts];
    const existingIndex = updatedDiscounts.findIndex((d) => d.product_id === productId);

    if (existingIndex >= 0) {
      if (value === 0) {
        // Remove if discount is 0
        updatedDiscounts.splice(existingIndex, 1);
      } else {
        // Update existing
        updatedDiscounts[existingIndex].discount_percentage = value;
      }
    } else if (value > 0) {
      // Add new
      updatedDiscounts.push({
        product_id: productId,
        discount_percentage: value
      });
    }

    setProfileForm({
      ...profileForm,
      product_discounts: updatedDiscounts
    });
  };

  // Submit profile form
  const handleProfileSubmit = async () => {
    if (!profileForm.name) {
      setError('Profile name is required');
      return;
    }

    setProfileSubmitting(true);
    setError('');

    try {
      if (editingProfileId) {
        await updateClientDiscountProfile(editingProfileId, profileForm);
        setSuccess('Discount profile updated successfully');
      } else {
        await createClientDiscountProfile(profileForm);
        setSuccess('Discount profile created successfully');
      }

      setShowProfileModal(false);
      resetProfileForm();
      loadDiscountProfiles();
    } catch (e) {
      setError(`Error saving discount profile: ${e.message}`);
    }

    setProfileSubmitting(false);
    setTimeout(() => setSuccess(''), 3000);
  };

  // Reset profile form
  const resetProfileForm = () => {
    setProfileForm({
      name: '',
      description: '',
      global_discount_percentage: 0,
      is_active: true,
      category_discounts: [],
      product_discounts: []
    });
    setEditingProfileId(null);
    setActiveTab('global');
  };

  // Edit profile
  const handleEditProfile = (profile) => {
    setProfileForm({
      name: profile.name,
      description: profile.description || '',
      global_discount_percentage: profile.global_discount_percentage || 0,
      is_active: profile.is_active,
      category_discounts: profile.category_discounts || [],
      product_discounts: profile.product_discounts || []
    });
    setEditingProfileId(profile.id);
    setShowProfileModal(true);
  };

  // Confirm delete
  const confirmDeleteProfile = (profile) => {
    setProfileToDelete(profile);
    setShowDeleteModal(true);
  };

  // Handle delete
  const handleDeleteProfile = async () => {
    setError('');

    try {
      await deleteClientDiscountProfile(profileToDelete.id);
      loadDiscountProfiles();
      setSuccess('Discount profile deleted successfully');
    } catch (e) {
      setError(`Error deleting discount profile: ${e.message}`);
    }

    setShowDeleteModal(false);
    setTimeout(() => setSuccess(''), 3000);
  };

  return (
    <Container fluid className="py-4">
      {/* Header */}
      <div className="mb-4">
        <h2 className="fw-bold text-primary mb-2">
          <FaPercentage className="me-2" />
          Client Discount Profiles
        </h2>
        <p className="text-muted">Manage discount profiles for clients with global, category, and product-specific discounts.</p>
      </div>

      {error && <Alert variant="danger">{error}</Alert>}
      {success && <Alert variant="success">{success}</Alert>}

      {/* Search and Actions */}
      <Card className="shadow-sm mb-4 border-0">
        <Card.Body>
          <Row className="align-items-center">
            <Col md={6}>
              <InputGroup>
                <InputGroup.Text>
                  <FaSearch />
                </InputGroup.Text>
                <Form.Control
                  placeholder="Search discount profiles..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </InputGroup>
            </Col>
            <Col md={6} className="text-md-end mt-3 mt-md-0">
              <Button
                variant="primary"
                onClick={() => {
                  resetProfileForm();
                  setShowProfileModal(true);
                }}
              >
                <FaPlus className="me-2" />
                Add New Profile
              </Button>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Discount Profiles List */}
      <Card className="shadow-sm border-0">
        <Card.Body className="p-0">
          {profileLoading ? (
            <div className="text-center py-5">
              <Spinner animation="border" variant="primary" />
              <p className="mt-3 text-muted">Loading discount profiles...</p>
            </div>
          ) : filteredProfiles.length === 0 ? (
            <div className="text-center py-5">
              <div className="mb-3">
                <FaPercentage style={{ fontSize: '3rem' }} className="text-muted" />
              </div>
              <p className="text-muted">No discount profiles found.</p>
              <Button
                variant="primary"
                onClick={() => {
                  resetProfileForm();
                  setShowProfileModal(true);
                }}
              >
                <FaPlus className="me-2" />
                Create First Profile
              </Button>
            </div>
          ) : (
            <Table hover responsive className="align-middle mb-0">
              <thead className="bg-light">
                <tr>
                  <th>ID</th>
                  <th>Name</th>
                  <th>Description</th>
                  <th>Global Discount</th>
                  <th>Category Discounts</th>
                  <th>Product Discounts</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredProfiles.map((profile) => (
                  <tr key={profile.id}>
                    <td>{profile.id}</td>
                    <td>
                      <span className="fw-medium">{profile.name}</span>
                    </td>
                    <td>
                      <div className="text-truncate" style={{ maxWidth: '200px' }}>
                        {profile.description || <span className="text-muted fst-italic">No description</span>}
                      </div>
                    </td>
                    <td>
                      {profile.global_discount_percentage > 0 ? (
                        <Badge bg="success">{profile.global_discount_percentage}%</Badge>
                      ) : (
                        <span className="text-muted">No global discount</span>
                      )}
                    </td>
                    <td>
                      {profile.category_discounts && profile.category_discounts.length > 0 ? (
                        <Badge bg="info">{profile.category_discounts.length} categories</Badge>
                      ) : (
                        <span className="text-muted">None</span>
                      )}
                    </td>
                    <td>
                      {profile.product_discounts && profile.product_discounts.length > 0 ? (
                        <Badge bg="info">{profile.product_discounts.length} products</Badge>
                      ) : (
                        <span className="text-muted">None</span>
                      )}
                    </td>
                    <td>{profile.is_active ? <Badge bg="success">Active</Badge> : <Badge bg="secondary">Inactive</Badge>}</td>
                    <td>
                      <Button size="sm" variant="outline-primary" className="me-1" onClick={() => handleEditProfile(profile)}>
                        <FaPencilAlt className="me-1" /> Edit
                      </Button>
                      <Button size="sm" variant="outline-danger" onClick={() => confirmDeleteProfile(profile)}>
                        <FaTrashAlt className="me-1" /> Delete
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}
        </Card.Body>
      </Card>

      {/* Professional Profile Modal */}
      <FormModal
        show={showProfileModal}
        onHide={() => setShowProfileModal(false)}
        onSubmit={handleProfileSubmit}
        title={editingProfileId ? 'Edit Discount Profile' : 'Add New Discount Profile'}
        isEdit={!!editingProfileId}
        loading={profileSubmitting}
        size="lg"
        submitText="Save Profile"
        cancelText="Cancel"
        icon={<FaPercentage />}
      >
        <Tabs activeKey={activeTab} onSelect={setActiveTab} className="mb-3">
          <Tab
            eventKey="global"
            title={
              <span>
                <FaPercentage className="me-2" />
                Global Settings
              </span>
            }
          >
            <Form>
              <Form.Group className="mb-3">
                <Form.Label>Profile Name</Form.Label>
                <Form.Control
                  type="text"
                  name="name"
                  value={profileForm.name}
                  onChange={handleProfileChange}
                  placeholder="Enter profile name"
                  required
                />
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>Description</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  name="description"
                  value={profileForm.description}
                  onChange={handleProfileChange}
                  placeholder="Enter profile description"
                />
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>Global Discount Percentage</Form.Label>
                <Form.Control
                  type="number"
                  name="global_discount_percentage"
                  value={profileForm.global_discount_percentage}
                  onChange={handleProfileChange}
                  min="0"
                  max="100"
                />
                <Form.Text className="text-muted">Default discount percentage applied to all products (0-100)</Form.Text>
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Check
                  type="checkbox"
                  id="is_active"
                  name="is_active"
                  label="Active"
                  checked={profileForm.is_active}
                  onChange={handleProfileChange}
                />
              </Form.Group>
            </Form>
          </Tab>
          <Tab
            eventKey="categories"
            title={
              <span>
                <FaLayerGroup className="me-2" />
                Category Discounts
              </span>
            }
          >
            <Alert variant="info">Category-specific discounts override the global discount for products in these categories.</Alert>
            <p className="text-muted mb-3">This feature will be implemented in the next phase.</p>
          </Tab>
          <Tab
            eventKey="products"
            title={
              <span>
                <FaTag className="me-2" />
                Product Discounts
              </span>
            }
          >
            <Alert variant="info">Product-specific discounts override both global and category discounts for these products.</Alert>
            <p className="text-muted mb-3">This feature will be implemented in the next phase.</p>
          </Tab>
        </Tabs>
      </FormModal>

      {/* Professional Delete Confirmation Modal */}
      <ConfirmationModal
        show={showDeleteModal}
        onHide={() => setShowDeleteModal(false)}
        onConfirm={handleDeleteProfile}
        title="Confirm Delete"
        message={
          <>
            Are you sure you want to delete the discount profile "{profileToDelete?.name}"?
            {profileToDelete?.clients_count > 0 && (
              <Alert variant="warning" className="mt-3">
                <strong>Warning:</strong> This profile is assigned to {profileToDelete.clients_count} clients. Deleting this profile will
                remove these discounts from the clients.
              </Alert>
            )}
          </>
        }
        confirmText="Delete"
        cancelText="Cancel"
        variant="danger"
        icon={<FaTrashAlt />}
      />
    </Container>
  );
};

export default ClientDiscountProfiles;
