# Documentation API Laravel

## Introduction

Cette documentation détaille les API disponibles dans le système de gestion e-commerce. Le système permet de gérer des produits, des catégories, des commandes, des clients et différents types de remises.

## Sommaire

1. [**Authentification**](./authentification.md)
   - Vérification avec Keycloak
   - Gestion des rôles et profils de remise

2. [**Gestion des Clients**](./clients.md)
   - Profils de remise (standard, premium, affilié, groupe)
   - Gestion des remises personnelles
   - Récupération des commandes d'un client

3. [**Gestion des Produits**](./produits.md)
   - Liste des produits
   - Filtrage par marque, catégorie, sous-catégorie
   - Gestion des caractéristiques

4. [**Gestion des Catégories**](./categories.md)
   - Catégories
   - Sous-catégories
   - Relations entre catégories et produits

5. [**Gestion des Commandes**](./commandes.md)
   - Création de commandes
   - Suivi des commandes
   - Application des remises

6. [**Gestion des Partenaires**](./partenaires.md)
   - Création et gestion des partenaires
   - Remises partenaires

7. [**Gestion des Points de Vente**](./points-de-vente.md)
   - Création et gestion des points de vente
   - Association des clients

8. [**Gestion des Groupes de Clients**](./groupes-clients.md)
   - Création et gestion des groupes
   - Remises de groupe

9. [**Gestion des Collections**](./collections.md)
   - Création et gestion des collections de produits
   - Association de produits aux collections
   - Mise en avant de produits dans les collections

10. [**Système de Remises et Promotions**](./remises-promotions.md)
    - Règles de remise par profil client
    - Promotions sur produits et collections
    - Logique de calcul des remises

11. [**Synchronisation des Utilisateurs**](./synchronisation-utilisateurs.md)
    - Processus de synchronisation entre Keycloak et Laravel
    - Gestion des rôles et profils de remise
    - Maintien de la cohérence des données

12. [**Système de Panier**](./panier.md)
    - Gestion du panier avec cookies
    - Compatibilité avec l'authentification Keycloak
    - Fusion des paniers anonymes et authentifiés
    - Vérification des stocks

## Architecture du système

Le système est basé sur une architecture REST API avec Laravel comme framework backend. L'authentification est gérée via Keycloak. Les données sont stockées dans une base de données PostgreSQL.

## Flux de travail typiques

- [Flux de création d'une commande](./flux-commande.md)
- [Flux de gestion des remises](./flux-remises.md)
- [Flux d'administration des clients](./flux-admin-clients.md)

## Notes techniques

- Toutes les API retournent des réponses au format JSON
- Les codes d'état HTTP standards sont utilisés (200, 201, 400, 401, 404, 500)
- Les dates sont au format ISO 8601 (YYYY-MM-DDTHH:MM:SS.sssZ)
- Les montants sont représentés en décimal avec 2 chiffres après la virgule
- Les pourcentages de remise sont représentés en décimal (ex: 10.50 pour 10,5%)
