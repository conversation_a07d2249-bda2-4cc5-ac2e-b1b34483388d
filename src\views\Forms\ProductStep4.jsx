import React from 'react';
import { Form, Row, Col, Button } from 'react-bootstrap';

const ProductStep4 = ({ images = [], setImages, primaryImageIndex, setPrimaryImageIndex, error }) => {
  // Handle file input change
  const handleImagesChange = (e) => {
    const files = Array.from(e.target.files);
    // Only add files that are not already present (avoid duplicates by name)
    const newImages = files.filter((f) => !images.some((img) => (img instanceof File ? img.name : img.id) === f.name));
    setImages((prev) => [...prev, ...newImages]);
    // If no primary selected, set the first as primary
    if (images.length === 0 && newImages.length > 0) setPrimaryImageIndex(0);
  };

  const handleSetPrimaryImage = (index) => {
    setPrimaryImageIndex(index);
  };

  const handleRemoveImage = (idx) => {
    setImages((prev) => prev.filter((_, i) => i !== idx));
    if (primaryImageIndex === idx) setPrimaryImageIndex(0);
    else if (primaryImageIndex > idx) setPrimaryImageIndex(primaryImageIndex - 1);
  };

  return (
    <Row>
      <Col md={12}>
        <Form.Group controlId="productImages">
          <Form.Label>
            Images du produit <span style={{ color: 'red' }}>*</span>
          </Form.Label>
          <Form.Control type="file" multiple accept="image/*" onChange={handleImagesChange} />
          <Form.Text className="text-muted">
            Veuillez sélectionner au moins une image. Cliquez sur une image pour la définir comme principale.
          </Form.Text>
          <div className="d-flex mt-2 flex-wrap">
            {images.map((img, idx) => (
              <div
                key={idx}
                className={`m-2 border ${idx === primaryImageIndex ? 'border-primary' : ''}`}
                style={{ cursor: 'pointer', width: 80, height: 80, position: 'relative' }}
              >
                <img
                  src={img instanceof File ? URL.createObjectURL(img) : img.url}
                  alt={img.name || img.filename || 'image'}
                  style={{ width: '100%', height: '100%', objectFit: 'cover', borderRadius: 4 }}
                  onClick={() => handleSetPrimaryImage(idx)}
                />
                {idx === primaryImageIndex && (
                  <span className="badge bg-primary" style={{ position: 'absolute', top: 0, left: 0 }}>
                    Principale
                  </span>
                )}
                <Button
                  variant="danger"
                  size="sm"
                  style={{ position: 'absolute', top: 0, right: 0, borderRadius: '50%' }}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemoveImage(idx);
                  }}
                >
                  &times;
                </Button>
              </div>
            ))}
          </div>
          {error && <div className="text-danger mt-2">{error}</div>}
        </Form.Group>
      </Col>
    </Row>
  );
};

export default ProductStep4;
