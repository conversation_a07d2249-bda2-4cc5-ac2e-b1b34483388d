import { useState, useEffect } from 'react';

// material-ui
import { Box, Typography, TextField, InputAdornment, Chip } from '@mui/material';
import { IconSearch } from '@tabler/icons-react';

// Standardized components
import StandardTable from '../../components/StandardTable';
import StandardCard from '../../components/StandardCard';
import MainCard from 'ui-component/cards/MainCard';

// Design system
import { COLORS, TYPOGRAPHY } from '../../themes/designSystem';

// Services
import { fetchClients } from '../../services/clientService';

// assets

// ==============================|| CLIENT LIST ||============================== //

const ClientList = () => {
  const [isLoading, setLoading] = useState(true);
  const [clients, setClients] = useState([]);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredClients, setFilteredClients] = useState([]);

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const handleSearchChange = (event) => {
    const term = event.target.value;
    setSearchTerm(term);
    setCurrentPage(1); // Reset to first page when searching

    if (term === '') {
      setFilteredClients(clients);
    } else {
      const filtered = clients.filter(
        (client) => client.name?.toLowerCase().includes(term.toLowerCase()) || client.email?.toLowerCase().includes(term.toLowerCase())
      );
      setFilteredClients(filtered);
    }
  };

  // Pagination logic
  const totalPages = Math.ceil(filteredClients.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentClients = filteredClients.slice(startIndex, endIndex);

  // Handle pagination
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const loadClients = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('🔄 Fetching clients from API...');
      const data = await fetchClients();
      console.log('✅ Clients data received:', data);

      // Handle different response formats
      const clientsArray = Array.isArray(data) ? data : data?.data || [];
      console.log('📊 Processed clients array:', clientsArray);

      setClients(clientsArray);
      setFilteredClients(clientsArray);
    } catch (err) {
      console.error('❌ Error loading clients:', err);
      setError('Erreur lors du chargement des clients: ' + err.message);
      setClients([]);
      setFilteredClients([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadClients();
  }, []);

  // Table columns configuration
  const columns = [
    { id: 'id', label: 'ID', minWidth: 70 },
    { id: 'name', label: 'Nom', minWidth: 150 },
    { id: 'email', label: 'Email', minWidth: 200 },
    { id: 'created_at', label: "Date d'inscription", minWidth: 150 },
    { id: 'updated_at', label: 'Dernière mise à jour', minWidth: 150 }
  ];

  // Custom cell renderer
  const renderCell = (column, row, value) => {
    switch (column.id) {
      case 'id':
        return (
          <Chip
            label={value || 'N/A'}
            size="small"
            sx={{
              backgroundColor: COLORS.grey[200],
              color: COLORS.text.dark,
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontWeight: TYPOGRAPHY.fontWeight.medium
            }}
          />
        );
      case 'name':
        return (
          <Typography
            variant="body2"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontWeight: TYPOGRAPHY.fontWeight.semibold,
              color: COLORS.text.dark
            }}
          >
            {value || 'N/A'}
          </Typography>
        );
      case 'created_at':
      case 'updated_at':
        return value ? new Date(value).toLocaleDateString('fr-FR') : 'N/A';
      default:
        return value || 'N/A';
    }
  };

  return (
    <MainCard title="Client Management - Enhanced">
      <Box sx={{ width: '100%' }}>
        {/* Search Field */}
        <StandardCard title="Recherche" size="small" sx={{ mb: 3 }}>
          <TextField
            fullWidth
            variant="outlined"
            placeholder="Rechercher par nom ou email..."
            value={searchTerm}
            onChange={handleSearchChange}
            sx={{
              '& .MuiInputBase-root': {
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                fontSize: TYPOGRAPHY.fontSize.sm
              }
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <IconSearch size={20} color={COLORS.grey[500]} />
                </InputAdornment>
              )
            }}
          />
        </StandardCard>

        {/* Results Summary */}
        <Box sx={{ mb: 2 }}>
          <Typography
            variant="h5"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontSize: TYPOGRAPHY.fontSize.lg,
              fontWeight: TYPOGRAPHY.fontWeight.semibold,
              color: COLORS.text.dark
            }}
          >
            Tous les Clients ({filteredClients.length})
          </Typography>
          {searchTerm && (
            <Typography
              variant="body2"
              sx={{
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                color: COLORS.text.secondary,
                mt: 0.5
              }}
            >
              Résultats pour "{searchTerm}"
            </Typography>
          )}
        </Box>

        {/* Enhanced Table */}
        <StandardTable
          columns={columns}
          data={currentClients}
          loading={isLoading}
          error={error}
          emptyMessage="Aucun client trouvé"
          renderCell={renderCell}
          hover={true}
          pagination={{
            page: currentPage,
            totalPages: totalPages,
            onPageChange: handlePageChange
          }}
        />

        {/* Additional Info */}
        {!isLoading && !error && filteredClients.length > 0 && (
          <Box sx={{ mt: 2 }}>
            <Typography
              variant="body2"
              sx={{
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                color: COLORS.text.secondary,
                textAlign: 'center'
              }}
            >
              Affichage de {startIndex + 1} à {Math.min(endIndex, filteredClients.length)} sur {filteredClients.length} client(s)
            </Typography>
          </Box>
        )}
      </Box>
    </MainCard>
  );
};

export default ClientList;
