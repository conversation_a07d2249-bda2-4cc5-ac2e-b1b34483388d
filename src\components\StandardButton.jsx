import React from 'react';
import { Button, CircularProgress } from '@mui/material';
import { COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS } from '../themes/designSystem';

/**
 * Standardized Button Component
 * Provides consistent button styling across the application
 *
 * @param {Object} props
 * @param {'primary'|'secondary'|'success'|'error'|'warning'|'outline'} props.variant - Button variant
 * @param {'small'|'medium'|'large'} props.size - Button size
 * @param {boolean} props.loading - Loading state
 * @param {boolean} props.disabled - Disabled state
 * @param {boolean} props.fullWidth - Full width button
 * @param {React.ReactNode} props.startIcon - Icon at the start
 * @param {React.ReactNode} props.endIcon - Icon at the end
 * @param {React.ReactNode} props.children - Button content
 * @param {Function} props.onClick - Click handler
 * @param {Object} props.sx - Additional styling
 */
const StandardButton = ({
  variant = 'primary',
  size = 'medium',
  loading = false,
  disabled = false,
  fullWidth = false,
  startIcon = null,
  endIcon = null,
  children,
  onClick,
  sx = {},
  ...props
}) => {
  // Size configurations
  const sizeConfig = {
    small: {
      padding: `${SPACING.xs} ${SPACING.md}`,
      fontSize: TYPOGRAPHY.fontSize.xs,
      minHeight: '32px'
    },
    medium: {
      padding: `${SPACING.sm} ${SPACING.lg}`,
      fontSize: TYPOGRAPHY.fontSize.sm,
      minHeight: '40px'
    },
    large: {
      padding: `${SPACING.md} ${SPACING.xl}`,
      fontSize: TYPOGRAPHY.fontSize.base,
      minHeight: '48px'
    }
  };

  // Enhanced variant configurations with modern styling
  const variantConfig = {
    primary: {
      background: `linear-gradient(135deg, ${COLORS.primary.main} 0%, ${COLORS.primary[600]} 100%)`,
      color: '#ffffff',
      border: 'none',
      boxShadow: '0 4px 14px 0 rgba(59, 130, 246, 0.15)',
      '&:hover': {
        background: `linear-gradient(135deg, ${COLORS.primary[600]} 0%, ${COLORS.primary[700]} 100%)`,
        boxShadow: '0 6px 20px 0 rgba(59, 130, 246, 0.25)',
        transform: 'translateY(-2px)'
      },
      '&:active': {
        transform: 'translateY(0)',
        boxShadow: '0 2px 8px 0 rgba(59, 130, 246, 0.2)'
      }
    },
    secondary: {
      background: `linear-gradient(135deg, ${COLORS.secondary.main} 0%, ${COLORS.secondary[600]} 100%)`,
      color: '#ffffff',
      border: 'none',
      boxShadow: '0 4px 14px 0 rgba(139, 92, 246, 0.15)',
      '&:hover': {
        background: `linear-gradient(135deg, ${COLORS.secondary[600]} 0%, ${COLORS.secondary[700]} 100%)`,
        boxShadow: '0 6px 20px 0 rgba(139, 92, 246, 0.25)',
        transform: 'translateY(-2px)'
      }
    },
    success: {
      background: `linear-gradient(135deg, ${COLORS.success.main} 0%, ${COLORS.success[600]} 100%)`,
      color: '#ffffff',
      border: 'none',
      boxShadow: '0 4px 14px 0 rgba(16, 185, 129, 0.15)',
      '&:hover': {
        background: `linear-gradient(135deg, ${COLORS.success[600]} 0%, ${COLORS.success[700]} 100%)`,
        boxShadow: '0 6px 20px 0 rgba(16, 185, 129, 0.25)',
        transform: 'translateY(-2px)'
      }
    },
    error: {
      background: `linear-gradient(135deg, ${COLORS.error.main} 0%, ${COLORS.error[600]} 100%)`,
      color: '#ffffff',
      border: 'none',
      boxShadow: '0 4px 14px 0 rgba(239, 68, 68, 0.15)',
      '&:hover': {
        background: `linear-gradient(135deg, ${COLORS.error[600]} 0%, ${COLORS.error[700]} 100%)`,
        boxShadow: '0 6px 20px 0 rgba(239, 68, 68, 0.25)',
        transform: 'translateY(-2px)'
      }
    },
    warning: {
      background: `linear-gradient(135deg, ${COLORS.warning.main} 0%, ${COLORS.warning[600]} 100%)`,
      color: COLORS.text.dark,
      border: 'none',
      boxShadow: '0 4px 14px 0 rgba(245, 158, 11, 0.15)',
      '&:hover': {
        background: `linear-gradient(135deg, ${COLORS.warning[600]} 0%, ${COLORS.warning[700]} 100%)`,
        color: '#ffffff',
        boxShadow: '0 6px 20px 0 rgba(245, 158, 11, 0.25)',
        transform: 'translateY(-2px)'
      }
    },
    outline: {
      backgroundColor: 'transparent',
      color: COLORS.primary.main,
      border: `2px solid ${COLORS.primary.main}`,
      boxShadow: 'none',
      '&:hover': {
        backgroundColor: COLORS.primary.main,
        color: '#ffffff',
        boxShadow: '0 6px 20px 0 rgba(59, 130, 246, 0.25)',
        transform: 'translateY(-2px)'
      }
    },
    ghost: {
      backgroundColor: COLORS.grey[50],
      color: COLORS.text.primary,
      border: `1px solid ${COLORS.grey[200]}`,
      '&:hover': {
        backgroundColor: COLORS.grey[100],
        borderColor: COLORS.grey[300],
        boxShadow: '0 2px 8px 0 rgba(0, 0, 0, 0.1)'
      }
    }
  };

  const currentSizeConfig = sizeConfig[size];
  const currentVariantConfig = variantConfig[variant];

  return (
    <Button
      disabled={disabled || loading}
      fullWidth={fullWidth}
      startIcon={loading ? <CircularProgress size={16} color="inherit" /> : startIcon}
      endIcon={!loading ? endIcon : null}
      onClick={onClick}
      sx={{
        ...currentSizeConfig,
        ...currentVariantConfig,
        fontFamily: TYPOGRAPHY.fontFamily.primary,
        fontWeight: TYPOGRAPHY.fontWeight.medium,
        borderRadius: BORDER_RADIUS.lg,
        textTransform: 'none',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: '-100%',
          width: '100%',
          height: '100%',
          background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
          transition: 'left 0.5s'
        },
        '&:hover::before': {
          left: '100%'
        },
        '&.Mui-disabled': {
          backgroundColor: COLORS.grey[300],
          color: COLORS.grey[500],
          border: 'none',
          boxShadow: 'none',
          transform: 'none'
        },
        ...sx
      }}
      {...props}
    >
      {children}
    </Button>
  );
};

export default StandardButton;
