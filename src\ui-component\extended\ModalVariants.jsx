import React from 'react';
import PropTypes from 'prop-types';
import ProfessionalModal from './ProfessionalModal';
import { FaPlus, FaEdit, FaTrash, FaExclamationTriangle, FaInfoCircle, FaCheckCircle } from 'react-icons/fa';

// Confirmation Modal
export const ConfirmationModal = ({
  show,
  onHide,
  onConfirm,
  title = "Confirmer l'action",
  message,
  confirmText = 'Confirmer',
  cancelText = 'Annuler',
  variant = 'warning',
  loading = false,
  ...props
}) => {
  const getIcon = () => {
    switch (variant) {
      case 'danger':
        return <FaTrash />;
      case 'warning':
        return <FaExclamationTriangle />;
      case 'info':
        return <FaInfoCircle />;
      case 'success':
        return <FaCheckCircle />;
      default:
        return <FaExclamationTriangle />;
    }
  };

  return (
    <ProfessionalModal
      show={show}
      onHide={onHide}
      title={title}
      icon={getIcon()}
      variant={variant}
      size="sm"
      primaryAction={onConfirm}
      primaryText={confirmText}
      secondaryText={cancelText}
      primaryVariant={variant}
      loading={loading}
      loadingText="Traitement..."
      {...props}
    >
      <div className="text-center py-3">
        <p className="mb-0">{message}</p>
      </div>
    </ProfessionalModal>
  );
};

// Delete Confirmation Modal
export const DeleteConfirmationModal = ({ show, onHide, onConfirm, itemName, itemType = 'élément', loading = false, ...props }) => {
  return (
    <ConfirmationModal
      show={show}
      onHide={onHide}
      onConfirm={onConfirm}
      title="Confirmer la suppression"
      message={
        <>
          Êtes-vous sûr de vouloir supprimer {itemType} <strong>{itemName}</strong> ?
          <br />
          <small className="text-muted">Cette action est irréversible.</small>
        </>
      }
      confirmText="Supprimer"
      cancelText="Annuler"
      variant="danger"
      loading={loading}
      {...props}
    />
  );
};

// Form Modal (Add/Edit)
export const FormModal = ({
  show,
  onHide,
  onSubmit,
  title,
  children,
  isEdit = false,
  loading = false,
  submitText,
  cancelText = 'Annuler',
  size = 'md',
  ...props
}) => {
  const defaultTitle = isEdit ? 'Modifier' : 'Ajouter';
  const defaultSubmitText = isEdit ? 'Enregistrer' : 'Ajouter';
  const icon = isEdit ? <FaEdit /> : <FaPlus />;

  return (
    <ProfessionalModal
      show={show}
      onHide={onHide}
      title={title || defaultTitle}
      icon={icon}
      variant="primary"
      size={size}
      primaryAction={onSubmit}
      primaryText={submitText || defaultSubmitText}
      secondaryText={cancelText}
      loading={loading}
      loadingText={isEdit ? 'Enregistrement...' : 'Ajout...'}
      {...props}
    >
      {children}
    </ProfessionalModal>
  );
};

// Info Modal
export const InfoModal = ({ show, onHide, title = 'Information', children, showFooter = true, primaryText = 'OK', ...props }) => {
  return (
    <ProfessionalModal
      show={show}
      onHide={onHide}
      title={title}
      icon={<FaInfoCircle />}
      variant="info"
      size="md"
      showFooter={showFooter}
      secondaryButton={false}
      primaryText={primaryText}
      primaryAction={onHide}
      {...props}
    >
      {children}
    </ProfessionalModal>
  );
};

// Success Modal
export const SuccessModal = ({ show, onHide, title = 'Succès', message, primaryText = 'OK', ...props }) => {
  return (
    <ProfessionalModal
      show={show}
      onHide={onHide}
      title={title}
      icon={<FaCheckCircle />}
      variant="success"
      size="sm"
      secondaryButton={false}
      primaryText={primaryText}
      primaryAction={onHide}
      primaryVariant="success"
      {...props}
    >
      <div className="text-center py-3">
        <p className="mb-0">{message}</p>
      </div>
    </ProfessionalModal>
  );
};

// Error Modal
export const ErrorModal = ({ show, onHide, title = 'Erreur', message, primaryText = 'OK', ...props }) => {
  return (
    <ProfessionalModal
      show={show}
      onHide={onHide}
      title={title}
      icon={<FaExclamationTriangle />}
      variant="danger"
      size="sm"
      secondaryButton={false}
      primaryText={primaryText}
      primaryAction={onHide}
      primaryVariant="danger"
      {...props}
    >
      <div className="text-center py-3">
        <p className="mb-0">{message}</p>
      </div>
    </ProfessionalModal>
  );
};

// Loading Modal
export const LoadingModal = ({ show, title = 'Chargement...', message = 'Veuillez patienter', ...props }) => {
  return (
    <ProfessionalModal
      show={show}
      onHide={() => {}} // Prevent closing during loading
      title={title}
      variant="info"
      size="sm"
      showFooter={false}
      showCloseButton={false}
      backdrop="static"
      keyboard={false}
      {...props}
    >
      <div className="text-center py-4">
        <div className="spinner-border text-primary mb-3" role="status">
          <span className="visually-hidden">Chargement...</span>
        </div>
        <p className="mb-0">{message}</p>
      </div>
    </ProfessionalModal>
  );
};

// PropTypes
ConfirmationModal.propTypes = {
  show: PropTypes.bool.isRequired,
  onHide: PropTypes.func.isRequired,
  onConfirm: PropTypes.func.isRequired,
  title: PropTypes.string,
  message: PropTypes.node.isRequired,
  confirmText: PropTypes.string,
  cancelText: PropTypes.string,
  variant: PropTypes.oneOf(['primary', 'secondary', 'success', 'danger', 'warning', 'info']),
  loading: PropTypes.bool
};

DeleteConfirmationModal.propTypes = {
  show: PropTypes.bool.isRequired,
  onHide: PropTypes.func.isRequired,
  onConfirm: PropTypes.func.isRequired,
  itemName: PropTypes.string.isRequired,
  itemType: PropTypes.string,
  loading: PropTypes.bool
};

FormModal.propTypes = {
  show: PropTypes.bool.isRequired,
  onHide: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
  title: PropTypes.string,
  children: PropTypes.node.isRequired,
  isEdit: PropTypes.bool,
  loading: PropTypes.bool,
  submitText: PropTypes.string,
  cancelText: PropTypes.string,
  size: PropTypes.oneOf(['sm', 'md', 'lg', 'xl'])
};

InfoModal.propTypes = {
  show: PropTypes.bool.isRequired,
  onHide: PropTypes.func.isRequired,
  title: PropTypes.string,
  children: PropTypes.node.isRequired,
  showFooter: PropTypes.bool,
  primaryText: PropTypes.string
};

SuccessModal.propTypes = {
  show: PropTypes.bool.isRequired,
  onHide: PropTypes.func.isRequired,
  title: PropTypes.string,
  message: PropTypes.string.isRequired,
  primaryText: PropTypes.string
};

ErrorModal.propTypes = {
  show: PropTypes.bool.isRequired,
  onHide: PropTypes.func.isRequired,
  title: PropTypes.string,
  message: PropTypes.string.isRequired,
  primaryText: PropTypes.string
};

LoadingModal.propTypes = {
  show: PropTypes.bool.isRequired,
  title: PropTypes.string,
  message: PropTypes.string
};
