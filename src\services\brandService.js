const API_URL = import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';

// Brands (Marques)
export async function fetchBrands(params = {}) {
  try {
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append('page', params.page);
    if (params.per_page) queryParams.append('per_page', params.per_page);
    if (params.search) queryParams.append('search', params.search);

    const url = `${API_URL}/marques${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

    const res = await fetch(url);
    if (!res.ok) {
      throw new Error(`Failed to fetch brands. Status: ${res.status}`);
    }

    return await res.json();
  } catch (error) {
    throw error;
  }
}

export async function fetchBrandById(id) {
  try {
    const res = await fetch(`${API_URL}/marques/${id}`);
    if (!res.ok) {
      throw new Error(`Failed to fetch brand. Status: ${res.status}`);
    }
    return await res.json();
  } catch (error) {
    throw error;
  }
}

export async function createBrand(data) {
  try {
    const res = await fetch(`${API_URL}/marques`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });

    if (!res.ok) {
      const errorData = await res.json();
      throw new Error(errorData.message || 'Erreur lors de la création de la marque');
    }

    return await res.json();
  } catch (error) {
    throw error;
  }
}

export async function updateBrand(id, data) {
  try {
    const res = await fetch(`${API_URL}/marques/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });

    if (!res.ok) {
      const errorData = await res.json();
      throw new Error(errorData.message || 'Erreur lors de la mise à jour de la marque');
    }

    return await res.json();
  } catch (error) {
    throw error;
  }
}

export async function deleteBrand(id) {
  try {
    const url = `${API_URL}/marques/${id}`;

    const res = await fetch(url, {
      method: 'DELETE',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });

    if (!res.ok) {
      let errorData;
      try {
        errorData = await res.json();
      } catch (parseError) {
        throw new Error(`Erreur HTTP ${res.status}: ${res.statusText}`);
      }
      throw new Error(errorData.message || `Erreur lors de la suppression de la marque (${res.status})`);
    }

    const result = await res.json();

    // Check if the response contains an error even with 200 status
    if (result.error) {
      // Handle foreign key constraint violation
      if (result.error.includes('Foreign key violation') || result.error.includes('SQLSTATE[23503]')) {
        throw new Error('Impossible de supprimer cette marque car elle contient encore des produits. Veuillez d\'abord supprimer tous les produits de cette marque.');
      }

      throw new Error(result.error || 'Erreur lors de la suppression de la marque');
    }

    return result;
  } catch (error) {
    throw error;
  }
}

export async function fetchBrandProducts(id, params = {}) {
  try {
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append('page', params.page);
    if (params.per_page) queryParams.append('per_page', params.per_page);
    if (params.search) queryParams.append('search', params.search);

    const url = `${API_URL}/marques/${id}/produits${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

    const res = await fetch(url);
    if (!res.ok) {
      throw new Error(`Failed to fetch brand products. Status: ${res.status}`);
    }

    return await res.json();
  } catch (error) {
    throw error;
  }
}
