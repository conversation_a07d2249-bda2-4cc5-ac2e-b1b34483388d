import React, { useState, useEffect } from 'react';
import { Contain<PERSON>, <PERSON>, Col, <PERSON>, But<PERSON>, Form, Table, Al<PERSON>, Spinner, <PERSON><PERSON>, Badge, InputGroup } from 'react-bootstrap';
import { FaPlus, FaPencilAlt, FaTrashAlt, FaTags, FaSearch, FaSort, FaCheck, FaTimes } from 'react-icons/fa';
import { fetchOrderStatuses } from '../../services/orderService';

const OrderStatusManagement = () => {
  // State for order statuses
  const [orderStatuses, setOrderStatuses] = useState([]);
  const [statusForm, setStatusForm] = useState({
    name: '',
    description: '',
    color: '#6c757d',
    is_active: true,
    is_default: false,
    is_final: false
  });
  const [editingStatusId, setEditingStatusId] = useState(null);
  const [statusLoading, setStatusLoading] = useState(false);
  const [statusSubmitting, setStatusSubmitting] = useState(false);

  // UI state
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [statusToDelete, setStatusToDelete] = useState(null);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredStatuses, setFilteredStatuses] = useState([]);

  // Load order statuses
  const loadOrderStatuses = async () => {
    setStatusLoading(true);
    setError('');
    try {
      const data = await fetchOrderStatuses();
      setOrderStatuses(data);
      setFilteredStatuses(data);
    } catch (e) {
      setError(`Error loading order statuses: ${e.message}`);
    }
    setStatusLoading(false);
  };

  // Initial data load
  useEffect(() => {
    loadOrderStatuses();
  }, []);

  // Filter statuses when search term changes
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredStatuses(orderStatuses);
    } else {
      const filtered = orderStatuses.filter(
        (status) =>
          status.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (status.description && status.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredStatuses(filtered);
    }
  }, [searchTerm, orderStatuses]);

  // Handle status form changes
  const handleStatusChange = (e) => {
    const { name, value, type, checked } = e.target;
    setStatusForm({
      ...statusForm,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Submit status form
  const handleStatusSubmit = async () => {
    if (!statusForm.name) {
      setError('Le nom du statut est requis');
      return;
    }

    setStatusSubmitting(true);
    setError('');

    try {
      // Since we're using mock data, we'll simulate the API call
      setTimeout(() => {
        if (editingStatusId) {
          // Update existing status
          const updatedStatuses = orderStatuses.map((status) => (status.id === editingStatusId ? { ...status, ...statusForm } : status));
          setOrderStatuses(updatedStatuses);
          setSuccess('Statut de commande mis à jour avec succès');
        } else {
          // Create new status
          const newStatus = {
            id: Date.now(), // Generate a unique ID
            ...statusForm
          };
          setOrderStatuses([...orderStatuses, newStatus]);
          setSuccess('Statut de commande créé avec succès');
        }

        setShowStatusModal(false);
        resetStatusForm();
        setStatusSubmitting(false);
      }, 500); // Simulate network delay
    } catch (e) {
      setError(`Erreur lors de l'enregistrement du statut: ${e.message}`);
      setStatusSubmitting(false);
    }

    setTimeout(() => setSuccess(''), 3000);
  };

  // Reset status form
  const resetStatusForm = () => {
    setStatusForm({
      name: '',
      description: '',
      color: '#6c757d',
      is_active: true,
      is_default: false,
      is_final: false
    });
    setEditingStatusId(null);
  };

  // Edit status
  const handleEditStatus = (status) => {
    setStatusForm({
      name: status.name,
      description: status.description || '',
      color: status.color || '#6c757d',
      is_active: status.is_active,
      is_default: status.is_default || false,
      is_final: status.is_final || false
    });
    setEditingStatusId(status.id);
    setShowStatusModal(true);
  };

  // Confirm delete
  const confirmDeleteStatus = (status) => {
    setStatusToDelete(status);
    setShowDeleteModal(true);
  };

  // Handle delete
  const handleDeleteStatus = async () => {
    setError('');

    try {
      // Since we're using mock data, we'll simulate the API call
      setTimeout(() => {
        const updatedStatuses = orderStatuses.filter((status) => status.id !== statusToDelete.id);
        setOrderStatuses(updatedStatuses);
        setFilteredStatuses(updatedStatuses);
        setSuccess('Statut de commande supprimé avec succès');
        setShowDeleteModal(false);
      }, 500); // Simulate network delay
    } catch (e) {
      setError(`Erreur lors de la suppression du statut: ${e.message}`);
      setShowDeleteModal(false);
    }

    setTimeout(() => setSuccess(''), 3000);
  };

  return (
    <Container fluid className="py-4">
      {/* Header */}
      <div className="mb-4">
        <h2 className="fw-bold text-primary mb-2">
          <FaTags className="me-2" />
          Gestion des Statuts de Commande
        </h2>
        <p className="text-muted">Gérez les statuts de commande et leurs paramètres.</p>
      </div>

      {error && <Alert variant="danger">{error}</Alert>}
      {success && <Alert variant="success">{success}</Alert>}

      {/* Search and Actions */}
      <Card className="shadow-sm mb-4 border-0">
        <Card.Body>
          <Row className="align-items-center">
            <Col md={6}>
              <InputGroup>
                <InputGroup.Text>
                  <FaSearch />
                </InputGroup.Text>
                <Form.Control placeholder="Rechercher des statuts..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
              </InputGroup>
            </Col>
            <Col md={6} className="text-md-end mt-3 mt-md-0">
              <Button
                variant="primary"
                onClick={() => {
                  resetStatusForm();
                  setShowStatusModal(true);
                }}
              >
                <FaPlus className="me-2" />
                Ajouter un statut
              </Button>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Order Statuses List */}
      <Card className="shadow-sm border-0">
        <Card.Body className="p-0">
          {statusLoading ? (
            <div className="text-center py-5">
              <Spinner animation="border" variant="primary" />
              <p className="mt-3 text-muted">Loading order statuses...</p>
            </div>
          ) : filteredStatuses.length === 0 ? (
            <div className="text-center py-5">
              <div className="mb-3">
                <FaTags style={{ fontSize: '3rem' }} className="text-muted" />
              </div>
              <p className="text-muted">No order statuses found.</p>
              <Button
                variant="primary"
                onClick={() => {
                  resetStatusForm();
                  setShowStatusModal(true);
                }}
              >
                <FaPlus className="me-2" />
                Create First Status
              </Button>
            </div>
          ) : (
            <Table hover responsive className="align-middle mb-0">
              <thead className="bg-light">
                <tr>
                  <th>Name</th>
                  <th>Description</th>
                  <th>Color</th>
                  <th>Status</th>
                  <th>Default</th>
                  <th>Final</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredStatuses.map((status) => (
                  <tr key={status.id}>
                    <td>
                      <span className="fw-medium">{status.name}</span>
                    </td>
                    <td>
                      <div className="text-truncate" style={{ maxWidth: '200px' }}>
                        {status.description || <span className="text-muted fst-italic">No description</span>}
                      </div>
                    </td>
                    <td>
                      <div
                        className="color-swatch"
                        style={{
                          backgroundColor: status.color || '#6c757d',
                          width: '24px',
                          height: '24px',
                          borderRadius: '4px'
                        }}
                      ></div>
                    </td>
                    <td>{status.is_active ? <Badge bg="success">Active</Badge> : <Badge bg="secondary">Inactive</Badge>}</td>
                    <td className="text-center">
                      {status.is_default ? <FaCheck className="text-success" /> : <FaTimes className="text-muted" />}
                    </td>
                    <td className="text-center">
                      {status.is_final ? <FaCheck className="text-success" /> : <FaTimes className="text-muted" />}
                    </td>
                    <td>
                      <Button size="sm" variant="outline-primary" className="me-1" onClick={() => handleEditStatus(status)}>
                        <FaPencilAlt className="me-1" /> Edit
                      </Button>
                      <Button size="sm" variant="outline-danger" onClick={() => confirmDeleteStatus(status)} disabled={status.is_default}>
                        <FaTrashAlt className="me-1" /> Delete
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}
        </Card.Body>
      </Card>

      {/* Status Modal */}
      <Modal show={showStatusModal} onHide={() => setShowStatusModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>{editingStatusId ? 'Edit Order Status' : 'Add New Order Status'}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Status Name</Form.Label>
              <Form.Control
                type="text"
                name="name"
                value={statusForm.name}
                onChange={handleStatusChange}
                placeholder="Enter status name"
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                name="description"
                value={statusForm.description}
                onChange={handleStatusChange}
                placeholder="Enter status description"
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Color</Form.Label>
              <Form.Control type="color" name="color" value={statusForm.color} onChange={handleStatusChange} title="Choose status color" />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Check
                type="checkbox"
                id="is_active"
                name="is_active"
                label="Active"
                checked={statusForm.is_active}
                onChange={handleStatusChange}
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Check
                type="checkbox"
                id="is_default"
                name="is_default"
                label="Default status for new orders"
                checked={statusForm.is_default}
                onChange={handleStatusChange}
              />
              <Form.Text className="text-muted">
                Only one status can be set as default. Setting this as default will remove default from other statuses.
              </Form.Text>
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Check
                type="checkbox"
                id="is_final"
                name="is_final"
                label="Final status (order completed)"
                checked={statusForm.is_final}
                onChange={handleStatusChange}
              />
              <Form.Text className="text-muted">
                Final statuses indicate that the order process is complete (e.g., Completed, Cancelled, Refunded).
              </Form.Text>
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowStatusModal(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleStatusSubmit} disabled={statusSubmitting}>
            {statusSubmitting ? (
              <>
                <Spinner size="sm" animation="border" className="me-2" />
                Processing...
              </>
            ) : (
              'Save Status'
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Confirm Delete</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          Are you sure you want to delete the order status "{statusToDelete?.name}"?
          {statusToDelete?.is_default && (
            <Alert variant="warning" className="mt-3">
              <strong>Warning:</strong> You cannot delete the default status.
            </Alert>
          )}
          {!statusToDelete?.is_default && (
            <Alert variant="warning" className="mt-3">
              <strong>Warning:</strong> Deleting this status may affect existing orders with this status.
            </Alert>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </Button>
          <Button variant="danger" onClick={handleDeleteStatus} disabled={statusToDelete?.is_default}>
            Delete
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default OrderStatusManagement;
