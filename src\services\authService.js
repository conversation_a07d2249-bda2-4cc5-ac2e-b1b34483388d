const API_URL = import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';

// Authentication Service
export class AuthService {
  constructor() {
    this.tokenKey = 'access_token';
    this.refreshTokenKey = 'refresh_token';
    this.idTokenKey = 'id_token';
    this.userKey = 'user_data';
  }

  // Verify Keycloak token with the backend and get user info
  async verifyToken(tokens) {
    try {
      console.log('🔐 Verifying token with backend and fetching user info...');
      console.log('🔐 Tokens received:', {
        hasAccessToken: !!tokens.access_token,
        accessTokenLength: tokens.access_token?.length,
        hasRefreshToken: !!tokens.refresh_token,
        hasIdToken: !!tokens.id_token,
        accessTokenStart: tokens.access_token?.substring(0, 50) + '...'
      });

      // First, verify the token
      console.log('🔐 Making request to:', `${API_URL}/auth/verify`);
      console.log('🔐 Request payload:', {
        access_token: tokens.access_token ? `${tokens.access_token.substring(0, 50)}...` : 'missing'
      });

      // Try to decode the token to see what's inside
      if (tokens.access_token) {
        try {
          const decodedToken = this.decodeJWTToken(tokens.access_token);
          console.log('🔐 Decoded token info:', {
            issuer: decodedToken.iss,
            subject: decodedToken.sub,
            audience: decodedToken.aud,
            expiration: new Date(decodedToken.exp * 1000),
            issuedAt: new Date(decodedToken.iat * 1000),
            email: decodedToken.email,
            name: decodedToken.name,
            roles: decodedToken.realm_access?.roles
          });
        } catch (error) {
          console.error('🔐 Failed to decode token:', error);
        }
      }

      const verifyResponse = await fetch(`${API_URL}/auth/verify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          access_token: tokens.access_token,
          refresh_token: tokens.refresh_token,
          id_token: tokens.id_token
        }),
        credentials: 'include',
        timeout: 10000
      });

      if (!verifyResponse.ok) {
        const errorText = await verifyResponse.text();
        console.error('🔐 Token verification failed:', {
          status: verifyResponse.status,
          statusText: verifyResponse.statusText,
          error: errorText
        });
        throw new Error(`Token verification failed: ${verifyResponse.status} - ${errorText}`);
      }

      console.log('🔐 Token verified, fetching user info...');

      // Then, get user info using the access token
      const userResponse = await fetch(`${API_URL}/auth/user`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${tokens.access_token}`
        },
        credentials: 'include'
      });

      if (!userResponse.ok) {
        const errorText = await userResponse.text();
        console.error('🔐 Failed to get user info:', {
          status: userResponse.status,
          statusText: userResponse.statusText,
          error: errorText
        });
        throw new Error(`Failed to get user info: ${userResponse.status} - ${errorText}`);
      }

      const responseData = await userResponse.json();
      console.log('🔐 User data received:', responseData);

      // Extract user and roles from the response
      const userData = responseData.user || responseData;
      const userRoles = responseData.roles || userData.roles || [];

      console.log('🔐 Extracted user data:', userData);
      console.log('🔐 Extracted roles:', userRoles);

      // Check if user has admin role
      if (!userRoles.includes('admin')) {
        console.error('🔐 Access denied: User does not have admin role');
        throw new Error('Access denied: Only administrators can access this application');
      }

      console.log('🔐 Admin access confirmed');

      // Store tokens and user data (store the actual user object, not the wrapper)
      this.setTokens(tokens);
      this.setUser(userData);

      // Clear logout flag since user is now logged in
      sessionStorage.removeItem('user_logged_out');

      console.log('🔐 Authentication successful');
      return {
        success: true,
        user: userData,
        roles: userRoles,
        message: 'Authentication successful'
      };
    } catch (error) {
      console.error('🔐 Authentication failed:', error);

      // Development mode fallback - REMOVE IN PRODUCTION
      if (process.env.NODE_ENV === 'development' && localStorage.getItem('auth_dev_mode') === 'true') {
        console.warn('🔧 Using development mode authentication fallback');

        // Create a mock user for development
        const mockUser = {
          id: 1,
          name: 'Development User',
          email: '<EMAIL>',
          roles: ['admin', 'client']
        };

        // Store mock tokens and user data
        this.setTokens(tokens);
        this.setUser(mockUser);
        sessionStorage.removeItem('user_logged_out');

        return {
          success: true,
          user: mockUser,
          roles: mockUser.roles,
          message: 'Development mode authentication'
        };
      }

      // Provide more specific error messages
      if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
        throw new Error('Unable to connect to the authentication server. Please check your internet connection and try again.');
      } else if (error.message.includes('401')) {
        throw new Error('Invalid authentication credentials. Please try logging in again.');
      } else if (error.message.includes('403')) {
        throw new Error('Access denied. You do not have permission to access this application.');
      } else if (error.message.includes('500')) {
        throw new Error('Authentication server error. Please try again later or contact support.');
      }

      throw error;
    }
  }

  // Fallback authentication removed - only real backend verification is allowed

  // Decode JWT token
  decodeJWTToken(token) {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
      }).join(''));

      const payload = JSON.parse(jsonPayload);

      // Debug: Log the available user fields
      console.log('🔐 Available user fields in token:', {
        sub: payload.sub,
        name: payload.name,
        given_name: payload.given_name,
        family_name: payload.family_name,
        preferred_username: payload.preferred_username,
        email: payload.email,
        email_verified: payload.email_verified,
        realm_access: payload.realm_access,
        resource_access: payload.resource_access
      });

      return payload;
    } catch (error) {
      console.error('Failed to decode JWT token:', error);
      throw error;
    }
  }

  // Role extraction removed - roles come from backend API

  // Logout user
  async logout() {
    try {
      console.log('🔐 Calling /api/auth/logout endpoint...');

      const accessToken = this.getAccessToken();
      const headers = {
        'Content-Type': 'application/json',
      };

      // Add authorization header if token exists
      if (accessToken) {
        headers['Authorization'] = `Bearer ${accessToken}`;
        console.log('🔐 Including authorization token in logout request');
      }

      const response = await fetch(`${API_URL}/auth/logout`, {
        method: 'POST',
        headers: headers,
        credentials: 'include' // Include cookies for session management
      });

      if (response.ok) {
        console.log('✅ Logout API call successful');
      } else {
        console.warn('⚠️ Logout API call failed with status:', response.status);
        // Try to get error message from response
        try {
          const errorData = await response.json();
          console.warn('⚠️ Logout error details:', errorData);
        } catch (e) {
          console.warn('⚠️ Could not parse logout error response');
        }
      }

      // Clear local storage regardless of API response
      console.log('🔐 Clearing local authentication data...');
      this.clearAuthData();

      console.log('✅ Logout completed successfully');
      return { success: true, shouldRedirectToKeycloak: true };
    } catch (error) {
      console.error('❌ Logout API call failed:', error);
      // Clear local data even if API call fails
      console.log('🔐 Clearing local data despite API failure...');
      this.clearAuthData();
      // Still indicate that we should redirect to Keycloak
      return { success: false, shouldRedirectToKeycloak: true, error: error.message };
    }
  }

  // Refresh authentication token
  async refreshToken() {
    try {
      const refreshToken = this.getRefreshToken();
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await fetch(`${API_URL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          refresh_token: refreshToken
        }),
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Token refresh failed: ${response.status}`);
      }

      const data = await response.json();

      // Update stored tokens
      this.setTokens({
        access_token: data.access_token,
        refresh_token: data.refresh_token || refreshToken,
        id_token: data.id_token
      });

      return data;
    } catch (error) {
      console.error('Token refresh failed:', error);
      this.clearAuthData();
      throw error;
    }
  }

  // Get current user info
  async getCurrentUser() {
    try {
      const response = await fetch(`${API_URL}/auth/user`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAccessToken()}`
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Failed to get user info: ${response.status}`);
      }

      const responseData = await response.json();
      console.log('🔐 getCurrentUser response:', responseData);

      // Extract user data from the response (handle nested structure)
      const userData = responseData.user || responseData;
      const roles = responseData.roles || userData.roles || [];

      // Add roles to user data for consistency
      const userWithRoles = {
        ...userData,
        roles: roles
      };

      console.log('🔐 Processed user data:', userWithRoles);

      this.setUser(userWithRoles);
      return userWithRoles;
    } catch (error) {
      console.error('Failed to get current user:', error);
      throw error;
    }
  }

  // Token management methods
  setTokens(tokens) {
    if (tokens.access_token) {
      localStorage.setItem(this.tokenKey, tokens.access_token);
    }
    if (tokens.refresh_token) {
      localStorage.setItem(this.refreshTokenKey, tokens.refresh_token);
    }
    if (tokens.id_token) {
      localStorage.setItem(this.idTokenKey, tokens.id_token);
    }
  }

  getAccessToken() {
    return localStorage.getItem(this.tokenKey);
  }

  getRefreshToken() {
    return localStorage.getItem(this.refreshTokenKey);
  }

  getIdToken() {
    return localStorage.getItem(this.idTokenKey);
  }

  setUser(userData) {
    localStorage.setItem(this.userKey, JSON.stringify(userData));
  }

  getUser() {
    const userData = localStorage.getItem(this.userKey);
    return userData ? JSON.parse(userData) : null;
  }

  clearAuthData() {
    console.log('🔐 Clearing all authentication data...');
    localStorage.removeItem(this.tokenKey);
    localStorage.removeItem(this.refreshTokenKey);
    localStorage.removeItem(this.idTokenKey);
    localStorage.removeItem(this.userKey);

    // Clear session storage as well
    sessionStorage.removeItem('auth_redirect_url');
    sessionStorage.removeItem('processed_auth_code');

    // Set logout flag to prevent auto-reconnection
    sessionStorage.setItem('user_logged_out', 'true');

    console.log('🔐 All authentication data cleared');
  }

  // Check if user is authenticated
  isAuthenticated() {
    // Check if user explicitly logged out
    const userLoggedOut = sessionStorage.getItem('user_logged_out');
    if (userLoggedOut === 'true') {
      console.log('🔐 User explicitly logged out, not authenticated');
      return false;
    }

    const token = this.getAccessToken();
    const user = this.getUser();
    return !!(token && user);
  }

  // Check if token is expired (basic check)
  isTokenExpired() {
    const token = this.getAccessToken();
    if (!token) return true;

    try {
      // Decode JWT token to check expiration
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp < currentTime;
    } catch (error) {
      console.error('Error checking token expiration:', error);
      return true;
    }
  }

  // Get user roles
  getUserRoles() {
    const user = this.getUser();
    return user?.roles || [];
  }

  // Check if user has specific role
  hasRole(role) {
    const roles = this.getUserRoles();
    return roles.includes(role);
  }

  // Check if user has any of the specified roles
  hasAnyRole(roles) {
    const userRoles = this.getUserRoles();
    return roles.some(role => userRoles.includes(role));
  }

  // Enhanced clear method for debugging
  clearAllAuthData() {
    console.log('🔐 Clearing all authentication data...');
    this.clearAuthData();
    sessionStorage.removeItem('auth_redirect_url');
    sessionStorage.removeItem('processed_auth_code');
    console.log('🔐 All authentication data cleared');
  }

  // Registration is handled directly by Keycloak - no custom registration needed
}

// Create singleton instance
const authService = new AuthService();
export default authService;
