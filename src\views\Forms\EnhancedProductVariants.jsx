import React, { useState, useEffect } from 'react';
import { But<PERSON>, Table, Form, Modal, Row, Col, Card, Badge, Spinner, Alert, ListGroup, Tabs, Tab } from 'react-bootstrap';
import { FaPlus, FaPencilAlt, FaTrashAlt, FaImage, FaCheck, FaTimes, FaRedo, FaLayerGroup } from 'react-icons/fa';
import { fetchAttributes, fetchAttributeValues } from '../../services/attributeService';
import ImageManager from '../GestionCommerciale/ImageManager';

const EnhancedProductVariants = ({
  productId,
  variants,
  setVariants,
  productAttributes,
  onVariantAdded,
  onVariantUpdated,
  onVariantDeleted
}) => {
  // State for variants
  const [editingVariant, setEditingVariant] = useState(null);
  const [showVariantModal, setShowVariantModal] = useState(false);
  const [variantForm, setVariantForm] = useState({
    sku: '',
    price_adjustment: 0,
    stock_quantity: 0,
    is_default: false,
    attribute_values: []
  });
  const [variantLoading, setVariantLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // State for attributes
  const [attributes, setAttributes] = useState([]);
  const [attributeValues, setAttributeValues] = useState({});
  const [attributesLoading, setAttributesLoading] = useState(false);
  const [selectedVariantId, setSelectedVariantId] = useState(null);
  const [activeTab, setActiveTab] = useState('list');

  // Load attributes
  const loadAttributes = async () => {
    setAttributesLoading(true);
    setError('');
    try {
      const data = await fetchAttributes();
      // Filter attributes to only include those that are used for variants
      const variantAttributes = data.filter(
        (attr) => attr.is_used_for_variants || productAttributes.some((pa) => pa.attribute_id === attr.id && pa.use_for_variants)
      );
      setAttributes(variantAttributes);

      // Load values for each attribute
      const valuesPromises = variantAttributes.map(async (attr) => {
        try {
          const values = await fetchAttributeValues(attr.id);
          return { attributeId: attr.id, values };
        } catch (error) {
          console.warn(`Could not load values for attribute ${attr.id}:`, error.message);
          // Return empty array if values can't be loaded (expected for this API)
          return { attributeId: attr.id, values: [] };
        }
      });

      const valuesResults = await Promise.all(valuesPromises);
      const valuesMap = {};
      valuesResults.forEach((result) => {
        valuesMap[result.attributeId] = result.values;
      });

      setAttributeValues(valuesMap);
    } catch (e) {
      setError(`Error loading attributes: ${e.message}`);
    }
    setAttributesLoading(false);
  };

  // Load data on component mount
  useEffect(() => {
    loadAttributes();
  }, [productAttributes]);

  // Handle form changes
  const handleVariantChange = (e) => {
    const { name, value, type, checked } = e.target;
    setVariantForm({
      ...variantForm,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle attribute value selection
  const handleAttributeValueChange = (attributeId, valueId) => {
    const currentValues = [...variantForm.attribute_values];

    // Remove any existing value for this attribute
    const filteredValues = currentValues.filter((av) => av.attribute_id !== attributeId);

    // Add the new value
    filteredValues.push({
      attribute_id: attributeId,
      value_id: valueId
    });

    setVariantForm({
      ...variantForm,
      attribute_values: filteredValues
    });
  };

  // Generate SKU based on selected attribute values
  const generateSku = () => {
    if (!variantForm.attribute_values.length) return '';

    const baseSku = productId ? `PROD-${productId}` : 'NEW-PROD';
    const attributeParts = variantForm.attribute_values.map((av) => {
      const attribute = attributes.find((a) => a.id === av.attribute_id);
      const value = attributeValues[av.attribute_id]?.find((v) => v.id === av.value_id);

      if (attribute && value) {
        return `${attribute.name.substring(0, 2).toUpperCase()}-${value.value.substring(0, 3).toUpperCase()}`;
      }
      return '';
    });

    return `${baseSku}-${attributeParts.join('-')}`;
  };

  // Reset form
  const resetForm = () => {
    setVariantForm({
      sku: '',
      price_adjustment: 0,
      stock_quantity: 0,
      is_default: false,
      attribute_values: []
    });
    setEditingVariant(null);
  };

  // Open modal to add new variant
  const openAddVariantModal = () => {
    resetForm();
    setShowVariantModal(true);
  };

  // Open modal to edit variant
  const openEditVariantModal = (variant) => {
    setVariantForm({
      sku: variant.sku || '',
      price_adjustment: variant.price_adjustment || 0,
      stock_quantity: variant.stock_quantity || 0,
      is_default: variant.is_default || false,
      attribute_values: variant.attribute_values || []
    });
    setEditingVariant(variant.id);
    setShowVariantModal(true);
  };

  // Handle variant submission
  const handleVariantSubmit = () => {
    // Validate form
    if (!variantForm.sku) {
      setError('SKU is required');
      return;
    }

    if (variantForm.attribute_values.length === 0) {
      setError('At least one attribute value must be selected');
      return;
    }

    setVariantLoading(true);
    setError('');

    try {
      // Generate a unique ID for new variants
      const variantId = editingVariant || Date.now();

      const updatedVariant = {
        id: variantId,
        sku: variantForm.sku,
        price_adjustment: parseFloat(variantForm.price_adjustment),
        stock_quantity: parseInt(variantForm.stock_quantity),
        is_default: variantForm.is_default,
        attribute_values: variantForm.attribute_values
      };

      if (editingVariant) {
        // Update existing variant
        const updatedVariants = variants.map((v) => (v.id === editingVariant ? updatedVariant : v));
        setVariants(updatedVariants);

        if (onVariantUpdated) {
          onVariantUpdated(updatedVariant);
        }

        setSuccess('Variant updated successfully');
      } else {
        // Add new variant
        setVariants([...variants, updatedVariant]);

        if (onVariantAdded) {
          onVariantAdded(updatedVariant);
        }

        setSuccess('Variant added successfully');
      }

      // Close modal and reset form
      setShowVariantModal(false);
      resetForm();
    } catch (e) {
      setError(`Error saving variant: ${e.message}`);
    }

    setVariantLoading(false);
    setTimeout(() => setSuccess(''), 3000);
  };

  // Handle variant deletion
  const handleVariantDelete = (variantId) => {
    if (!window.confirm('Are you sure you want to delete this variant?')) return;

    try {
      const updatedVariants = variants.filter((v) => v.id !== variantId);
      setVariants(updatedVariants);

      if (onVariantDeleted) {
        onVariantDeleted(variantId);
      }

      setSuccess('Variant deleted successfully');
      setTimeout(() => setSuccess(''), 3000);
    } catch (e) {
      setError(`Error deleting variant: ${e.message}`);
    }
  };

  // Handle variant image management
  const openVariantImageManager = (variantId) => {
    setSelectedVariantId(variantId);
    setActiveTab('images');
  };

  return (
    <div className="product-variants-manager">
      {error && <Alert variant="danger">{error}</Alert>}
      {success && <Alert variant="success">{success}</Alert>}

      <Tabs activeKey={activeTab} onSelect={setActiveTab} className="mb-3">
        <Tab eventKey="list" title="Variants List">
          <div className="d-flex justify-content-between align-items-center mb-3">
            <h5 className="mb-0">Product Variants</h5>
            <Button variant="primary" onClick={openAddVariantModal} disabled={attributes.length === 0}>
              <FaPlus className="me-2" />
              Add Variant
            </Button>
          </div>

          {attributesLoading ? (
            <div className="text-center py-4">
              <Spinner animation="border" variant="primary" />
              <p className="mt-2">Loading attributes...</p>
            </div>
          ) : attributes.length === 0 ? (
            <Alert variant="info">
              <FaLayerGroup className="me-2" />
              No attributes available for creating variants. Please add attributes to the product first.
            </Alert>
          ) : variants.length === 0 ? (
            <div className="text-center py-4 border rounded">
              <p className="text-muted mb-3">No variants added yet</p>
              <Button variant="outline-primary" onClick={openAddVariantModal}>
                <FaPlus className="me-2" />
                Create First Variant
              </Button>
            </div>
          ) : (
            <Table responsive hover className="border">
              <thead className="bg-light">
                <tr>
                  <th>SKU</th>
                  <th>Attributes</th>
                  <th>Price Adjustment</th>
                  <th>Stock</th>
                  <th>Default</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {variants.map((variant) => (
                  <tr key={variant.id}>
                    <td>{variant.sku}</td>
                    <td>
                      <div className="d-flex flex-wrap gap-1">
                        {variant.attribute_values.map((av) => {
                          const attribute = attributes.find((a) => a.id === av.attribute_id);
                          const value = attributeValues[av.attribute_id]?.find((v) => v.id === av.value_id);

                          return attribute && value ? (
                            <Badge key={`${av.attribute_id}-${av.value_id}`} bg="info" className="me-1">
                              {attribute.name}: {value.display_name || value.value}
                            </Badge>
                          ) : null;
                        })}
                      </div>
                    </td>
                    <td>
                      {variant.price_adjustment > 0 ? (
                        <span className="text-success">+{variant.price_adjustment}</span>
                      ) : variant.price_adjustment < 0 ? (
                        <span className="text-danger">{variant.price_adjustment}</span>
                      ) : (
                        <span className="text-muted">0</span>
                      )}
                    </td>
                    <td>{variant.stock_quantity}</td>
                    <td>{variant.is_default ? <FaCheck className="text-success" /> : <FaTimes className="text-muted" />}</td>
                    <td>
                      <Button size="sm" variant="outline-primary" className="me-1" onClick={() => openEditVariantModal(variant)}>
                        <FaPencilAlt />
                      </Button>
                      <Button size="sm" variant="outline-info" className="me-1" onClick={() => openVariantImageManager(variant.id)}>
                        <FaImage />
                      </Button>
                      <Button size="sm" variant="outline-danger" onClick={() => handleVariantDelete(variant.id)}>
                        <FaTrashAlt />
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}
        </Tab>
        <Tab eventKey="images" title="Variant Images">
          {selectedVariantId ? (
            <div>
              <div className="d-flex justify-content-between align-items-center mb-3">
                <h5 className="mb-0">Images for Variant: {variants.find((v) => v.id === selectedVariantId)?.sku}</h5>
                <Button variant="outline-secondary" size="sm" onClick={() => setActiveTab('list')}>
                  Back to Variants List
                </Button>
              </div>
              <ImageManager modelType="produit_variante" modelId={selectedVariantId} />
            </div>
          ) : (
            <Alert variant="info">Please select a variant to manage its images.</Alert>
          )}
        </Tab>
      </Tabs>

      {/* Variant Form Modal */}
      <Modal show={showVariantModal} onHide={() => setShowVariantModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>{editingVariant ? 'Edit Variant' : 'Add New Variant'}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Row className="mb-3">
              <Col md={6}>
                <Form.Group controlId="variantSku">
                  <Form.Label>SKU</Form.Label>
                  <div className="d-flex">
                    <Form.Control
                      name="sku"
                      value={variantForm.sku}
                      onChange={handleVariantChange}
                      placeholder="Enter SKU or generate automatically"
                      required
                    />
                    <Button
                      variant="outline-secondary"
                      onClick={() => setVariantForm({ ...variantForm, sku: generateSku() })}
                      className="ms-2"
                    >
                      <FaRedo className="me-1" />
                      Generate
                    </Button>
                  </div>
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group controlId="variantPriceAdjustment">
                  <Form.Label>Price Adjustment</Form.Label>
                  <Form.Control
                    type="number"
                    name="price_adjustment"
                    value={variantForm.price_adjustment}
                    onChange={handleVariantChange}
                    placeholder="0.00"
                  />
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group controlId="variantStockQuantity">
                  <Form.Label>Stock Quantity</Form.Label>
                  <Form.Control
                    type="number"
                    name="stock_quantity"
                    value={variantForm.stock_quantity}
                    onChange={handleVariantChange}
                    placeholder="0"
                  />
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Check
                type="checkbox"
                id="variantIsDefault"
                name="is_default"
                label="Set as default variant"
                checked={variantForm.is_default}
                onChange={handleVariantChange}
              />
            </Form.Group>

            <h6 className="mb-3">Variant Attributes</h6>
            {attributes.length === 0 ? (
              <Alert variant="warning">No attributes available for creating variants. Please add attributes to the product first.</Alert>
            ) : (
              <Row>
                {attributes.map((attribute) => (
                  <Col md={6} key={attribute.id} className="mb-3">
                    <Card>
                      <Card.Header className="py-2 bg-light">
                        <strong>{attribute.name}</strong>
                      </Card.Header>
                      <Card.Body>
                        <Form.Group>
                          <Form.Label>Select {attribute.name} Value</Form.Label>
                          <Form.Select
                            value={variantForm.attribute_values.find((av) => av.attribute_id === attribute.id)?.value_id || ''}
                            onChange={(e) => handleAttributeValueChange(attribute.id, e.target.value)}
                            required
                          >
                            <option value="">Select a value</option>
                            {attributeValues[attribute.id]?.map((value) => (
                              <option key={value.id} value={value.id}>
                                {value.display_name || value.value}
                              </option>
                            ))}
                          </Form.Select>
                        </Form.Group>
                      </Card.Body>
                    </Card>
                  </Col>
                ))}
              </Row>
            )}
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowVariantModal(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleVariantSubmit} disabled={variantLoading}>
            {variantLoading ? (
              <>
                <Spinner size="sm" animation="border" className="me-2" />
                Processing...
              </>
            ) : editingVariant ? (
              'Update Variant'
            ) : (
              'Add Variant'
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default EnhancedProductVariants;
