import { lazy } from 'react';

// project imports
import Loadable from 'ui-component/Loadable';
import MinimalLayout from 'layout/MinimalLayout';

// maintenance routing
const LoginPage = Loadable(lazy(() => import('views/pages/authentication/Login')));
const RegisterPage = Loadable(lazy(() => import('views/pages/authentication/Register')));
const AuthCallback = Loadable(lazy(() => import('views/pages/authentication/AuthCallback')));
const KeycloakTest = Loadable(lazy(() => import('views/pages/KeycloakTest')));
const AuthDebug = Loadable(lazy(() => import('views/debug/AuthDebug')));

// ==============================|| AUTHENTICATION ROUTING ||============================== //

const AuthenticationRoutes = {
  path: '/',
  element: <MinimalLayout />,
  children: [
    {
      path: '/pages/login',
      element: <LoginPage />
    },
    {
      path: '/pages/register',
      element: <RegisterPage />
    },
    {
      path: '/auth/callback',
      element: <AuthCallback />
    },
    {
      path: '/keycloak-test',
      element: <KeycloakTest />
    },
    {
      path: '/debug/auth',
      element: <AuthDebug />
    }
  ]
};

export default AuthenticationRoutes;
