import React from 'react';
import PropTypes from 'prop-types';
import { Card, CardContent, CardHeader, CardActions, Box } from '@mui/material';
import { COLORS, TYPOGRAPHY } from 'themes/designSystem';

const StandardCard = ({
  children,
  title,
  subtitle,
  actions,
  elevation = 1,
  sx = {},
  contentSx = {},
  headerSx = {},
  actionsSx = {},
  ...props
}) => {
  const cardStyles = {
    borderRadius: 3,
    border: `1px solid ${COLORS.primary.light}20`,
    boxShadow: `0 2px 8px ${COLORS.primary.main}10`,
    transition: 'all 0.2s ease-in-out',
    '&:hover': {
      boxShadow: `0 4px 16px ${COLORS.primary.main}15`,
      transform: 'translateY(-2px)'
    },
    ...sx
  };

  const headerStyles = {
    pb: title || subtitle ? 1 : 0,
    '& .MuiCardHeader-title': {
      fontFamily: TYPOGRAPHY.fontFamily.primary,
      fontWeight: TYPOGRAPHY.fontWeight.semibold,
      color: COLORS.text.dark,
      fontSize: TYPOGRAPHY.fontSize.lg
    },
    '& .MuiCardHeader-subheader': {
      fontFamily: TYPOGRAPHY.fontFamily.primary,
      color: COLORS.text.secondary,
      fontSize: TYPOGRAPHY.fontSize.sm
    },
    ...headerSx
  };

  const contentStyles = {
    pt: title || subtitle ? 1 : 2,
    '&:last-child': {
      pb: actions ? 1 : 2
    },
    ...contentSx
  };

  const actionsStyles = {
    pt: 0,
    px: 2,
    pb: 2,
    justifyContent: 'flex-end',
    ...actionsSx
  };

  return (
    <Card elevation={elevation} sx={cardStyles} {...props}>
      {(title || subtitle) && (
        <CardHeader
          title={title}
          subheader={subtitle}
          sx={headerStyles}
        />
      )}
      
      <CardContent sx={contentStyles}>
        {children}
      </CardContent>
      
      {actions && (
        <CardActions sx={actionsStyles}>
          {actions}
        </CardActions>
      )}
    </Card>
  );
};

StandardCard.propTypes = {
  children: PropTypes.node.isRequired,
  title: PropTypes.string,
  subtitle: PropTypes.string,
  actions: PropTypes.node,
  elevation: PropTypes.number,
  sx: PropTypes.object,
  contentSx: PropTypes.object,
  headerSx: PropTypes.object,
  actionsSx: PropTypes.object
};

export default StandardCard;
