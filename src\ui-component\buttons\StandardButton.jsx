import React from 'react';
import PropTypes from 'prop-types';
import { Button, CircularProgress } from '@mui/material';
import { COLORS, TYPOGRAPHY } from 'themes/designSystem';

const StandardButton = ({
  children,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  startIcon,
  endIcon,
  onClick,
  type = 'button',
  fullWidth = false,
  sx = {},
  ...props
}) => {
  const getVariantStyles = () => {
    const baseStyles = {
      fontFamily: TYPOGRAPHY.fontFamily.primary,
      fontWeight: TYPOGRAPHY.fontWeight.medium,
      textTransform: 'none',
      borderRadius: 2,
      transition: 'all 0.2s ease-in-out',
      '&:hover': {
        transform: 'translateY(-1px)',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
      },
      '&:active': {
        transform: 'translateY(0)'
      }
    };

    switch (variant) {
      case 'primary':
        return {
          ...baseStyles,
          backgroundColor: COLORS.primary.main,
          color: 'white',
          border: `1px solid ${COLORS.primary.main}`,
          '&:hover': {
            ...baseStyles['&:hover'],
            backgroundColor: COLORS.primary.dark,
            borderColor: COLORS.primary.dark
          },
          '&:disabled': {
            backgroundColor: COLORS.text.secondary + '40',
            borderColor: COLORS.text.secondary + '40',
            color: 'white'
          }
        };

      case 'secondary':
        return {
          ...baseStyles,
          backgroundColor: COLORS.text.secondary,
          color: 'white',
          border: `1px solid ${COLORS.text.secondary}`,
          '&:hover': {
            ...baseStyles['&:hover'],
            backgroundColor: COLORS.text.dark,
            borderColor: COLORS.text.dark
          },
          '&:disabled': {
            backgroundColor: COLORS.text.secondary + '40',
            borderColor: COLORS.text.secondary + '40',
            color: 'white'
          }
        };

      case 'outline':
        return {
          ...baseStyles,
          backgroundColor: 'transparent',
          color: COLORS.primary.main,
          border: `1px solid ${COLORS.primary.main}`,
          '&:hover': {
            ...baseStyles['&:hover'],
            backgroundColor: COLORS.primary.light + '20',
            borderColor: COLORS.primary.dark
          },
          '&:disabled': {
            backgroundColor: 'transparent',
            borderColor: COLORS.text.secondary + '40',
            color: COLORS.text.secondary + '60'
          }
        };

      case 'success':
        return {
          ...baseStyles,
          backgroundColor: COLORS.success.main,
          color: 'white',
          border: `1px solid ${COLORS.success.main}`,
          '&:hover': {
            ...baseStyles['&:hover'],
            backgroundColor: COLORS.success.dark,
            borderColor: COLORS.success.dark
          },
          '&:disabled': {
            backgroundColor: COLORS.text.secondary + '40',
            borderColor: COLORS.text.secondary + '40',
            color: 'white'
          }
        };

      case 'error':
        return {
          ...baseStyles,
          backgroundColor: COLORS.error.main,
          color: 'white',
          border: `1px solid ${COLORS.error.main}`,
          '&:hover': {
            ...baseStyles['&:hover'],
            backgroundColor: COLORS.error.dark,
            borderColor: COLORS.error.dark
          },
          '&:disabled': {
            backgroundColor: COLORS.text.secondary + '40',
            borderColor: COLORS.text.secondary + '40',
            color: 'white'
          }
        };

      case 'warning':
        return {
          ...baseStyles,
          backgroundColor: COLORS.warning.main,
          color: 'white',
          border: `1px solid ${COLORS.warning.main}`,
          '&:hover': {
            ...baseStyles['&:hover'],
            backgroundColor: COLORS.warning.dark,
            borderColor: COLORS.warning.dark
          },
          '&:disabled': {
            backgroundColor: COLORS.text.secondary + '40',
            borderColor: COLORS.text.secondary + '40',
            color: 'white'
          }
        };

      case 'info':
        return {
          ...baseStyles,
          backgroundColor: COLORS.info.main,
          color: 'white',
          border: `1px solid ${COLORS.info.main}`,
          '&:hover': {
            ...baseStyles['&:hover'],
            backgroundColor: COLORS.info.dark,
            borderColor: COLORS.info.dark
          },
          '&:disabled': {
            backgroundColor: COLORS.text.secondary + '40',
            borderColor: COLORS.text.secondary + '40',
            color: 'white'
          }
        };

      default:
        return baseStyles;
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          fontSize: TYPOGRAPHY.fontSize.sm,
          padding: '6px 12px',
          minHeight: '32px'
        };
      case 'large':
        return {
          fontSize: TYPOGRAPHY.fontSize.lg,
          padding: '12px 24px',
          minHeight: '48px'
        };
      case 'medium':
      default:
        return {
          fontSize: TYPOGRAPHY.fontSize.md,
          padding: '8px 16px',
          minHeight: '40px'
        };
    }
  };

  const combinedStyles = {
    ...getVariantStyles(),
    ...getSizeStyles(),
    ...sx
  };

  return (
    <Button
      variant="contained"
      size={size}
      disabled={disabled || loading}
      startIcon={loading ? <CircularProgress size={16} color="inherit" /> : startIcon}
      endIcon={endIcon}
      onClick={onClick}
      type={type}
      fullWidth={fullWidth}
      sx={combinedStyles}
      {...props}
    >
      {children}
    </Button>
  );
};

StandardButton.propTypes = {
  children: PropTypes.node.isRequired,
  variant: PropTypes.oneOf(['primary', 'secondary', 'outline', 'success', 'error', 'warning', 'info']),
  size: PropTypes.oneOf(['small', 'medium', 'large']),
  disabled: PropTypes.bool,
  loading: PropTypes.bool,
  startIcon: PropTypes.node,
  endIcon: PropTypes.node,
  onClick: PropTypes.func,
  type: PropTypes.oneOf(['button', 'submit', 'reset']),
  fullWidth: PropTypes.bool,
  sx: PropTypes.object
};

export default StandardButton;
