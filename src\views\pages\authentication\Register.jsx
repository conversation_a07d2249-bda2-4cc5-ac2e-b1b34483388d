import { useEffect } from 'react';

// ================================|| KEYCLOAK REGISTRATION REDIRECT ||================================ //

export default function Register() {
  // Immediately redirect to Keycloak registration
  useEffect(() => {
    console.log('🔐 Register page accessed - redirecting to Keycloak registration');

    const keycloakUrl = process.env.REACT_APP_KEYCLOAK_URL || 'https://keycloak-prod.1squalq6nmfj.eu-de.codeengine.appdomain.cloud';
    const realm = process.env.REACT_APP_KEYCLOAK_REALM || 'jiheneline';
    const clientId = process.env.REACT_APP_KEYCLOAK_CLIENT_ID || 'backoffice-client';

    const registrationUrl = `${keycloakUrl}/realms/${realm}/protocol/openid-connect/registrations`;
    const params = new URLSearchParams({
      client_id: clientId,
      redirect_uri: window.location.origin + '/auth/callback',
      response_type: 'code',
      scope: 'openid profile email'
    });

    window.location.href = `${registrationUrl}?${params.toString()}`;
  }, []);

  // This component should never render since we redirect immediately
  return null;
}
