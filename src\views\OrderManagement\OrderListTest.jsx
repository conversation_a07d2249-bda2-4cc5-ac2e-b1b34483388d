import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  Tooltip,
  Button,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

// Services
import { fetchOrdersFromLiveAPI } from '../../services/orderService';

const OrderListTest = () => {
  const navigate = useNavigate();

  // State
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalOrders, setTotalOrders] = useState(0);

  // Load orders from API
  const loadOrders = async (page = 1, search = '') => {
    try {
      setLoading(true);
      setError('');

      const params = {
        page: page,
        per_page: 15
      };

      if (search) params.search = search;

      console.log('🔄 Loading orders with params:', params);

      const result = await fetchOrdersFromLiveAPI(params);
      console.log('✅ Orders loaded successfully:', result);

      setOrders(result.data || []);
      setTotalOrders(result.pagination.total || 0);
    } catch (error) {
      console.error('❌ Error loading orders:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Effects
  useEffect(() => {
    loadOrders(currentPage, searchTerm);
  }, [currentPage]);

  // Handlers
  const handleSearch = (event) => {
    const value = event.target.value;
    setSearchTerm(value);
    setCurrentPage(1);
    
    // Debounce search
    const timeoutId = setTimeout(() => {
      loadOrders(1, value);
    }, 500);

    return () => clearTimeout(timeoutId);
  };

  const handleViewOrder = (order) => {
    console.log('🔍 Viewing order:', order.id);
    navigate(`/app/orders/${order.id}`);
  };

  const handleEditOrder = (order) => {
    console.log('✏️ Editing order:', order.id);
    // Pour l'instant, juste un log
    alert(`Édition de la commande ${order.numero_commande || order.id}`);
  };

  const handleDeleteOrder = (order) => {
    console.log('🗑️ Deleting order:', order.id);
    // Pour l'instant, juste un log
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer la commande ${order.numero_commande || order.id} ?`)) {
      alert(`Suppression de la commande ${order.numero_commande || order.id}`);
    }
  };

  const handleRefresh = () => {
    loadOrders(currentPage, searchTerm);
  };

  // Utility functions
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount) => {
    if (!amount) return '0.00 DT';
    return `${parseFloat(amount).toFixed(2)} DT`;
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'confirmee':
      case 'confirmed':
        return 'success';
      case 'en_attente':
      case 'pending':
        return 'warning';
      case 'annulee':
      case 'cancelled':
        return 'error';
      case 'livree':
      case 'delivered':
        return 'info';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Chargement des commandes...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button variant="contained" onClick={handleRefresh}>
          Réessayer
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Typography variant="h4" gutterBottom>
        🧪 Test - Liste des Commandes avec Actions
      </Typography>
      
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Version de test pour vérifier les actions - {totalOrders} commandes trouvées
      </Typography>

      {/* Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <TextField
              placeholder="Rechercher par numéro, client, email..."
              value={searchTerm}
              onChange={handleSearch}
              sx={{ flex: 1 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                )
              }}
            />
            <Button
              variant="outlined"
              onClick={handleRefresh}
              disabled={loading}
              startIcon={<RefreshIcon />}
            >
              Actualiser
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>N° Commande</TableCell>
              <TableCell>Client</TableCell>
              <TableCell>Date</TableCell>
              <TableCell>Total</TableCell>
              <TableCell>Statut</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {orders.map((order) => (
              <TableRow key={order.id} hover>
                <TableCell>
                  <Typography variant="body2" fontWeight="bold" color="primary">
                    {order.numero_commande || `CMD-${order.id}`}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    ID: {order.id}
                  </Typography>
                </TableCell>
                
                <TableCell>
                  <Typography variant="body2" fontWeight="medium">
                    {order.user?.name || 'Client'}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {order.user?.email || order.email_commande || 'N/A'}
                  </Typography>
                </TableCell>
                
                <TableCell>
                  <Typography variant="body2">
                    {formatDate(order.created_at)}
                  </Typography>
                </TableCell>
                
                <TableCell>
                  <Typography variant="body2" fontWeight="bold" color="success.main">
                    {formatCurrency(order.total_commande)}
                  </Typography>
                </TableCell>
                
                <TableCell>
                  <Chip
                    label={order.status_label || order.status}
                    size="small"
                    color={getStatusColor(order.status)}
                  />
                </TableCell>
                
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Tooltip title="Voir les détails">
                      <IconButton
                        size="small"
                        onClick={() => handleViewOrder(order)}
                        color="primary"
                      >
                        <VisibilityIcon />
                      </IconButton>
                    </Tooltip>
                    
                    <Tooltip title="Modifier">
                      <IconButton
                        size="small"
                        onClick={() => handleEditOrder(order)}
                        color="secondary"
                      >
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                    
                    <Tooltip title="Supprimer">
                      <IconButton
                        size="small"
                        onClick={() => handleDeleteOrder(order)}
                        color="error"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Empty State */}
      {orders.length === 0 && !loading && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="text.secondary">
            Aucune commande trouvée
          </Typography>
        </Box>
      )}

      {/* Debug Info */}
      <Box sx={{ mt: 3, p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="h6" gutterBottom>
          🔧 Debug Info:
        </Typography>
        <Typography variant="body2">
          • Total commandes: {totalOrders}
        </Typography>
        <Typography variant="body2">
          • Commandes affichées: {orders.length}
        </Typography>
        <Typography variant="body2">
          • Actions disponibles: Voir, Modifier, Supprimer
        </Typography>
      </Box>
    </Box>
  );
};

export default OrderListTest;
