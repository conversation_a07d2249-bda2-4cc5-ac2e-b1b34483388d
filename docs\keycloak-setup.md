# Keycloak Setup Guide

## Overview

This guide explains how to configure Keycloak for the Jihene-Line backoffice application using the production Keycloak instance.

## Keycloak Instance Details

- **URL**: `https://keycloak-prod.1squalq6nmfj.eu-de.codeengine.appdomain.cloud`
- **Realm**: `jiheneline`
- **Client ID**: `backoffice-client`

## Configuration Steps

### 1. Access Keycloak Admin Console

1. Navigate to: `https://keycloak-prod.1squalq6nmfj.eu-de.codeengine.appdomain.cloud/admin`
2. Login with administrator credentials
3. Select the `jiheneline` realm

### 2. Configure Client Settings

#### Basic Settings
- **Client ID**: `backoffice-client`
- **Client Protocol**: `openid-connect`
- **Access Type**: `public`
- **Standard Flow Enabled**: `ON`
- **Direct Access Grants Enabled**: `ON`

#### URLs Configuration
- **Root URL**: `http://localhost:3000` (for development)
- **Valid Redirect URIs**:
  - `http://localhost:3000/auth/callback`
  - `https://your-production-domain.com/auth/callback`
- **Web Origins**:
  - `http://localhost:3000`
  - `https://your-production-domain.com`
- **Admin URL**: `http://localhost:3000`

#### Advanced Settings
- **Access Token Lifespan**: `15 minutes`
- **Client Session Idle**: `30 minutes`
- **Client Session Max**: `12 hours`

### 3. Configure Realm Settings

#### Login Settings
- **User Registration**: `OFF` (backoffice requires admin approval)
- **Forgot Password**: `ON`
- **Remember Me**: `ON`
- **Verify Email**: `ON`

#### Token Settings
- **Default Signature Algorithm**: `RS256`
- **Access Token Lifespan**: `15 minutes`
- **Refresh Token Max Reuse**: `0`

### 4. Configure User Roles

Create the following roles in the realm:

#### Realm Roles
- `admin` - Full administrative access
- `client` - Basic client access
- `partenaire` - Partner-specific access

#### Role Mappings
Assign appropriate roles to users based on their access requirements.

### 5. Configure Client Scopes

Ensure the following scopes are available:
- `openid` - Required for OpenID Connect
- `profile` - User profile information
- `email` - User email address
- `roles` - User roles information

### 6. User Management

#### Creating Users
1. Go to Users → Add User
2. Fill in required information:
   - Username
   - Email
   - First Name
   - Last Name
3. Set Email Verified to `ON`
4. Assign appropriate roles

#### User Attributes
Add custom attributes if needed:
- `remise_personnelle` - Personal discount rate
- `type_client` - Client type classification

## Environment Configuration

### Development Environment

```env
REACT_APP_KEYCLOAK_URL=https://keycloak-prod.1squalq6nmfj.eu-de.codeengine.appdomain.cloud
REACT_APP_KEYCLOAK_REALM=jiheneline
REACT_APP_KEYCLOAK_CLIENT_ID=backoffice-client
```

### Production Environment

Update the redirect URIs and web origins to match your production domain:

```env
REACT_APP_KEYCLOAK_URL=https://keycloak-prod.1squalq6nmfj.eu-de.codeengine.appdomain.cloud
REACT_APP_KEYCLOAK_REALM=jiheneline
REACT_APP_KEYCLOAK_CLIENT_ID=backoffice-client
```

## Testing the Configuration

### 1. Test Login Flow
1. Start the application
2. Navigate to login page
3. Click "Login with Keycloak"
4. Verify redirect to Keycloak
5. Login with test credentials
6. Verify successful redirect back to application

### 2. Test Token Validation
1. Check browser network tab for API calls
2. Verify tokens are included in requests
3. Check that protected routes work correctly

### 3. Test Logout
1. Click logout in application
2. Verify session is terminated
3. Verify redirect to login page

## Troubleshooting

### Common Issues

#### 1. Invalid Redirect URI
**Error**: `Invalid redirect_uri`
**Solution**: Ensure redirect URIs are correctly configured in Keycloak client settings

#### 2. CORS Issues
**Error**: CORS policy blocks requests
**Solution**: Add your domain to Web Origins in Keycloak client settings

#### 3. Token Validation Fails
**Error**: Token verification fails
**Solution**: Check that the realm and client configuration match your environment variables

#### 4. User Not Found
**Error**: User doesn't exist in Laravel database
**Solution**: Ensure user synchronization is working between Keycloak and Laravel

### Debug Mode

Enable debug logging by setting:
```javascript
localStorage.setItem('auth_debug', 'true');
```

This will log authentication flow details to the browser console.

## Security Considerations

1. **HTTPS Only**: Always use HTTPS in production
2. **Token Expiration**: Configure appropriate token lifespans
3. **Role-Based Access**: Implement proper role-based access control
4. **Session Management**: Configure session timeouts appropriately
5. **CORS Configuration**: Restrict CORS to necessary domains only

## Maintenance

### Regular Tasks
1. **Monitor User Sessions**: Check for unusual activity
2. **Update Tokens**: Rotate signing keys periodically
3. **Review Roles**: Audit user roles and permissions
4. **Backup Configuration**: Export realm configuration regularly

### Updates
When updating Keycloak:
1. Test in development environment first
2. Backup current configuration
3. Update production during maintenance window
4. Verify all functionality works correctly
