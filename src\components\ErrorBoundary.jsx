import React, { Component } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON>,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Refresh as RefreshIcon,
  BugReport as BugReportIcon
} from '@mui/icons-material';

class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('🚨 ErrorBoundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo
    });
  }

  handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: prevState.retryCount + 1
    }));
  };

  render() {
    if (this.state.hasError) {
      return (
        <Box sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <BugReportIcon sx={{ color: 'error.main', mr: 1 }} />
                <Typography variant="h5" color="error">
                  Une erreur s'est produite
                </Typography>
              </Box>

              <Alert severity="error" sx={{ mb: 2 }}>
                <Typography variant="body1" gutterBottom>
                  L'application a rencontré une erreur inattendue.
                </Typography>
                <Typography variant="body2">
                  {this.state.error?.message || 'Erreur inconnue'}
                </Typography>
              </Alert>

              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <Button
                  variant="contained"
                  startIcon={<RefreshIcon />}
                  onClick={this.handleRetry}
                >
                  Réessayer
                </Button>
                <Button
                  variant="outlined"
                  onClick={() => window.location.reload()}
                >
                  Recharger la page
                </Button>
              </Box>

              {this.state.retryCount > 0 && (
                <Chip 
                  label={`Tentatives: ${this.state.retryCount}`} 
                  size="small" 
                  color="warning"
                  sx={{ mb: 2 }}
                />
              )}

              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="subtitle2">
                    Détails techniques (pour le développeur)
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Box component="pre" sx={{ 
                    fontSize: '0.75rem', 
                    backgroundColor: 'grey.100', 
                    p: 2, 
                    borderRadius: 1,
                    overflow: 'auto',
                    maxHeight: 300
                  }}>
                    {this.state.error?.stack || 'Aucun détail disponible'}
                  </Box>
                </AccordionDetails>
              </Accordion>
            </CardContent>
          </Card>
        </Box>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
