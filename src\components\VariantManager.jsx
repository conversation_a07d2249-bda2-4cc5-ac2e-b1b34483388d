import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, But<PERSON>, Form, Row, Col, Table, Badge, Card, Alert, Spinner, Image, InputGroup, Tabs, Tab } from 'react-bootstrap';
import { FaPlus, FaEdit, FaTrash, FaImage, FaEye, FaSave, FaTimes, FaRedo } from 'react-icons/fa';
import { fetchProductVariants, createProductVariant, updateVariant, deleteVariant } from '../services/productService';

const VariantManager = ({ productId, productAttributes = [], onVariantChange, show, onHide }) => {
  // State management
  const [variants, setVariants] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Modal states
  const [showVariantModal, setShowVariantModal] = useState(false);
  const [editingVariant, setEditingVariant] = useState(null);
  const [showImageModal, setShowImageModal] = useState(false);
  const [selectedVariantForImage, setSelectedVariantForImage] = useState(null);

  // Form state
  const [variantForm, setVariantForm] = useState({
    sku: '',
    prix_supplement: 0,
    quantite: 0,
    actif: true,
    attributs: []
  });

  // Image state
  const [variantImages, setVariantImages] = useState({});
  const [uploadingImage, setUploadingImage] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);

  // Load variants on component mount
  useEffect(() => {
    if (productId && show) {
      loadVariants();
    }
  }, [productId, show]);

  // Load variants from API
  const loadVariants = async () => {
    try {
      setLoading(true);
      setError('');

      const response = await fetchProductVariants(productId);
      const variantData = response.data || response || [];
      setVariants(variantData);

      // Load images for each variant
      await loadVariantImages(variantData);

      console.log('✅ Variants loaded:', variantData.length);
    } catch (err) {
      console.error('Error loading variants:', err);
      setError('Erreur lors du chargement des variantes: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  // Load images for variants
  const loadVariantImages = async (variantList) => {
    const imagePromises = variantList.map(async (variant) => {
      try {
        const response = await fetch(
          `${import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api'}/images/get?model_type=produit_variante&model_id=${variant.id}`
        );
        if (response.ok) {
          const imageData = await response.json();
          return { variantId: variant.id, images: imageData.images || [] };
        }
      } catch (error) {
        console.warn(`Failed to load images for variant ${variant.id}:`, error);
      }
      return { variantId: variant.id, images: [] };
    });

    const imageResults = await Promise.all(imagePromises);
    const imageMap = {};
    imageResults.forEach(({ variantId, images }) => {
      imageMap[variantId] = images;
    });
    setVariantImages(imageMap);
  };

  // Handle form changes
  const handleFormChange = (e) => {
    const { name, value, type, checked } = e.target;
    setVariantForm((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Handle attribute changes
  const handleAttributeChange = (attributeId, value) => {
    setVariantForm((prev) => ({
      ...prev,
      attributs: prev.attributs
        .map((attr) => (attr.attribut_id === attributeId ? { ...attr, valeur: value } : attr))
        .concat(prev.attributs.find((attr) => attr.attribut_id === attributeId) ? [] : [{ attribut_id: attributeId, valeur: value }])
        .filter((attr) => attr.valeur !== '')
    }));
  };

  // Generate SKU automatically
  const generateSKU = () => {
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 1000)
      .toString()
      .padStart(3, '0');
    return `VAR-${timestamp}-${random}`;
  };

  // Open add variant modal
  const openAddVariantModal = () => {
    setEditingVariant(null);
    setVariantForm({
      sku: generateSKU(),
      prix_supplement: 0,
      quantite: 0,
      actif: true,
      attributs: []
    });
    setSelectedImage(null);
    setImagePreview(null);
    setShowVariantModal(true);
  };

  // Open edit variant modal
  const openEditVariantModal = (variant) => {
    setEditingVariant(variant.id);
    setVariantForm({
      sku: variant.sku || '',
      prix_supplement: variant.prix_supplement || 0,
      quantite: variant.quantite || 0,
      actif: variant.actif !== false,
      attributs: variant.attributs || []
    });
    setSelectedImage(null);
    setImagePreview(null);
    setShowVariantModal(true);
  };

  // Handle image selection
  const handleImageSelect = (e) => {
    const file = e.target.files[0];
    if (file) {
      setSelectedImage(file);
      const reader = new FileReader();
      reader.onload = (e) => setImagePreview(e.target.result);
      reader.readAsDataURL(file);
    }
  };

  // Save variant
  const saveVariant = async () => {
    try {
      setLoading(true);
      setError('');

      // Validate form
      if (!variantForm.sku) {
        setError('Le SKU est obligatoire');
        return;
      }

      const variantData = {
        sku: variantForm.sku,
        prix_supplement: parseFloat(variantForm.prix_supplement) || 0,
        quantite: parseInt(variantForm.quantite) || 0,
        actif: variantForm.actif,
        attributs: variantForm.attributs
      };

      let savedVariant;
      if (editingVariant) {
        // Update existing variant
        savedVariant = await updateVariant(editingVariant, variantData);
      } else {
        // Create new variant
        savedVariant = await createProductVariant(productId, variantData);
      }

      // Upload image if selected
      if (selectedImage && savedVariant.data?.id) {
        await uploadVariantImage(savedVariant.data.id, selectedImage);
      }

      setSuccess(editingVariant ? 'Variante mise à jour avec succès' : 'Variante créée avec succès');
      setShowVariantModal(false);
      await loadVariants(); // Reload variants

      if (onVariantChange) {
        onVariantChange();
      }
    } catch (err) {
      console.error('Error saving variant:', err);
      setError('Erreur lors de la sauvegarde: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  // Upload variant image
  const uploadVariantImage = async (variantId, imageFile) => {
    try {
      setUploadingImage(true);

      const formData = new FormData();
      formData.append('image', imageFile);
      formData.append('model_type', 'produit_variante');
      formData.append('model_id', variantId);
      formData.append('is_primary', 'true');

      const token = localStorage.getItem('access_token');
      const headers = {
        Accept: 'application/json'
      };
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`${import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api'}/images/upload`, {
        method: 'POST',
        headers,
        body: formData
      });

      if (!response.ok) {
        throw new Error("Erreur lors de l'upload de l'image");
      }

      console.log('✅ Variant image uploaded successfully');
    } catch (error) {
      console.error('Error uploading variant image:', error);
      throw error;
    } finally {
      setUploadingImage(false);
    }
  };

  // Delete variant
  const deleteVariantHandler = async (variantId) => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer cette variante ?')) {
      return;
    }

    try {
      setLoading(true);
      await deleteVariant(variantId);
      setSuccess('Variante supprimée avec succès');
      await loadVariants();

      if (onVariantChange) {
        onVariantChange();
      }
    } catch (err) {
      console.error('Error deleting variant:', err);
      setError('Erreur lors de la suppression: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  // Get primary image for variant
  const getVariantPrimaryImage = (variantId) => {
    const images = variantImages[variantId] || [];
    const primaryImage = images.find((img) => img.is_primary) || images[0];
    return primaryImage?.url || null;
  };

  // Format price
  const formatPrice = (price) => {
    return parseFloat(price || 0).toFixed(2) + ' DT';
  };

  return (
    <>
      {/* Main Variant Manager Modal */}
      <Modal show={show} onHide={onHide} size="xl" backdrop="static">
        <Modal.Header closeButton>
          <Modal.Title>
            <FaImage className="me-2" />
            Gestion des Variantes
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {/* Alerts */}
          {error && (
            <Alert variant="danger" dismissible onClose={() => setError('')}>
              {error}
            </Alert>
          )}
          {success && (
            <Alert variant="success" dismissible onClose={() => setSuccess('')}>
              {success}
            </Alert>
          )}

          {/* Action Bar */}
          <div className="d-flex justify-content-between align-items-center mb-3">
            <h6 className="mb-0">Variantes du produit ({variants.length})</h6>
            <Button variant="primary" onClick={openAddVariantModal} disabled={loading}>
              <FaPlus className="me-2" />
              Ajouter une variante
            </Button>
          </div>

          {/* Loading State */}
          {loading && (
            <div className="text-center py-4">
              <Spinner animation="border" variant="primary" />
              <div className="mt-2">Chargement...</div>
            </div>
          )}

          {/* Variants Table */}
          {!loading && (
            <Table striped bordered hover responsive>
              <thead className="table-dark">
                <tr>
                  <th>Image</th>
                  <th>SKU</th>
                  <th>Prix supplément</th>
                  <th>Stock</th>
                  <th>Attributs</th>
                  <th>Statut</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {variants.length === 0 ? (
                  <tr>
                    <td colSpan="7" className="text-center py-4 text-muted">
                      Aucune variante trouvée. Cliquez sur "Ajouter une variante" pour commencer.
                    </td>
                  </tr>
                ) : (
                  variants.map((variant) => (
                    <tr key={variant.id}>
                      <td>
                        <div style={{ width: '60px', height: '60px' }}>
                          {getVariantPrimaryImage(variant.id) ? (
                            <Image
                              src={getVariantPrimaryImage(variant.id)}
                              alt={variant.sku}
                              style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                              rounded
                            />
                          ) : (
                            <div
                              className="d-flex align-items-center justify-content-center bg-light rounded"
                              style={{ width: '100%', height: '100%' }}
                            >
                              <FaImage className="text-muted" />
                            </div>
                          )}
                        </div>
                      </td>
                      <td>
                        <strong>{variant.sku}</strong>
                      </td>
                      <td>{formatPrice(variant.prix_supplement)}</td>
                      <td>
                        <Badge bg={variant.quantite > 0 ? 'success' : 'danger'}>{variant.quantite}</Badge>
                      </td>
                      <td>
                        <div className="d-flex flex-wrap gap-1">
                          {(variant.attributs || []).map((attr, idx) => (
                            <Badge key={idx} bg="info" className="small">
                              {attr.valeur}
                            </Badge>
                          ))}
                        </div>
                      </td>
                      <td>
                        <Badge bg={variant.actif ? 'success' : 'secondary'}>{variant.actif ? 'Actif' : 'Inactif'}</Badge>
                      </td>
                      <td>
                        <div className="d-flex gap-1">
                          <Button variant="outline-primary" size="sm" onClick={() => openEditVariantModal(variant)} title="Modifier">
                            <FaEdit />
                          </Button>
                          <Button variant="outline-danger" size="sm" onClick={() => deleteVariantHandler(variant.id)} title="Supprimer">
                            <FaTrash />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </Table>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={onHide}>
            Fermer
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Variant Form Modal */}
      <Modal show={showVariantModal} onHide={() => setShowVariantModal(false)} size="lg" backdrop="static">
        <Modal.Header closeButton>
          <Modal.Title>{editingVariant ? 'Modifier la variante' : 'Ajouter une variante'}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Tabs defaultActiveKey="basic" className="mb-3">
              <Tab eventKey="basic" title="Informations de base">
                <Row className="g-3">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>
                        SKU <span className="text-danger">*</span>
                      </Form.Label>
                      <InputGroup>
                        <Form.Control
                          name="sku"
                          value={variantForm.sku}
                          onChange={handleFormChange}
                          placeholder="Ex: PROD-001-RED-L"
                          required
                        />
                        <Button
                          variant="outline-secondary"
                          onClick={() => setVariantForm((prev) => ({ ...prev, sku: generateSKU() }))}
                          title="Générer un SKU automatique"
                        >
                          <FaRedo />
                        </Button>
                      </InputGroup>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>Prix supplément (DT)</Form.Label>
                      <Form.Control
                        name="prix_supplement"
                        type="number"
                        step="0.01"
                        value={variantForm.prix_supplement}
                        onChange={handleFormChange}
                        placeholder="0.00"
                      />
                      <Form.Text className="text-muted">Montant à ajouter au prix de base du produit</Form.Text>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>Quantité en stock</Form.Label>
                      <Form.Control
                        name="quantite"
                        type="number"
                        min="0"
                        value={variantForm.quantite}
                        onChange={handleFormChange}
                        placeholder="0"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Check
                        name="actif"
                        type="checkbox"
                        label="Variante active"
                        checked={variantForm.actif}
                        onChange={handleFormChange}
                      />
                      <Form.Text className="text-muted">Les variantes inactives ne sont pas visibles sur le site</Form.Text>
                    </Form.Group>
                  </Col>
                </Row>
              </Tab>

              <Tab eventKey="attributes" title="Attributs">
                <div className="mb-3">
                  <h6>Attributs de la variante</h6>
                  <p className="text-muted small">
                    Définissez les valeurs des attributs qui différencient cette variante (couleur, taille, etc.)
                  </p>
                </div>

                {productAttributes.length === 0 ? (
                  <Alert variant="warning">Aucun attribut disponible. Veuillez d'abord ajouter des attributs au produit.</Alert>
                ) : (
                  <Row className="g-3">
                    {productAttributes.map((attribute) => (
                      <Col md={6} key={attribute.id}>
                        <Card className="h-100">
                          <Card.Header className="py-2 bg-light">
                            <strong>{attribute.nom}</strong>
                            {attribute.obligatoire && <span className="text-danger ms-1">*</span>}
                          </Card.Header>
                          <Card.Body>
                            <Form.Group>
                              <Form.Label className="small">Valeur</Form.Label>
                              {attribute.type_valeur === 'nombre' ? (
                                <Form.Control
                                  type="number"
                                  step="0.01"
                                  value={variantForm.attributs.find((a) => a.attribut_id === attribute.id)?.valeur || ''}
                                  onChange={(e) => handleAttributeChange(attribute.id, e.target.value)}
                                  placeholder={`Entrez ${attribute.nom.toLowerCase()}`}
                                  required={attribute.obligatoire}
                                />
                              ) : attribute.type_valeur === 'booleen' ? (
                                <Form.Check
                                  type="checkbox"
                                  label={`Activer ${attribute.nom.toLowerCase()}`}
                                  checked={variantForm.attributs.find((a) => a.attribut_id === attribute.id)?.valeur === 'true'}
                                  onChange={(e) => handleAttributeChange(attribute.id, e.target.checked ? 'true' : 'false')}
                                />
                              ) : attribute.type_valeur === 'date' ? (
                                <Form.Control
                                  type="date"
                                  value={variantForm.attributs.find((a) => a.attribut_id === attribute.id)?.valeur || ''}
                                  onChange={(e) => handleAttributeChange(attribute.id, e.target.value)}
                                  required={attribute.obligatoire}
                                />
                              ) : (
                                <Form.Control
                                  type="text"
                                  value={variantForm.attributs.find((a) => a.attribut_id === attribute.id)?.valeur || ''}
                                  onChange={(e) => handleAttributeChange(attribute.id, e.target.value)}
                                  placeholder={`Entrez ${attribute.nom.toLowerCase()}`}
                                  required={attribute.obligatoire}
                                />
                              )}
                              {attribute.description && <Form.Text className="text-muted small">{attribute.description}</Form.Text>}
                            </Form.Group>
                          </Card.Body>
                        </Card>
                      </Col>
                    ))}
                  </Row>
                )}
              </Tab>

              <Tab eventKey="image" title="Image">
                <div className="mb-3">
                  <h6>Image de la variante</h6>
                  <p className="text-muted small">Ajoutez une image spécifique à cette variante (optionnel)</p>
                </div>

                <Row>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>Sélectionner une image</Form.Label>
                      <Form.Control type="file" accept="image/*" onChange={handleImageSelect} />
                      <Form.Text className="text-muted">Formats acceptés: JPG, PNG, GIF (max 10MB)</Form.Text>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    {imagePreview && (
                      <div>
                        <Form.Label>Aperçu</Form.Label>
                        <div>
                          <Image src={imagePreview} alt="Aperçu" style={{ maxWidth: '200px', maxHeight: '200px' }} thumbnail />
                        </div>
                      </div>
                    )}
                  </Col>
                </Row>
              </Tab>
            </Tabs>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowVariantModal(false)}>
            <FaTimes className="me-2" />
            Annuler
          </Button>
          <Button variant="primary" onClick={saveVariant} disabled={loading || uploadingImage}>
            {loading || uploadingImage ? (
              <>
                <Spinner as="span" animation="border" size="sm" className="me-2" />
                {uploadingImage ? 'Upload en cours...' : 'Sauvegarde...'}
              </>
            ) : (
              <>
                <FaSave className="me-2" />
                {editingVariant ? 'Mettre à jour' : 'Créer la variante'}
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default VariantManager;
