# Système de Panier

> **Note importante**: Ce système a été mis à jour pour ne plus utiliser les session IDs. Veuillez consulter le document [cart_system_update.md](cart_system_update.md) pour plus d'informations sur les changements récents.

Ce document décrit le système de panier de l'application.

## Table des matières

1. [Introduction](#introduction)
2. [Modèle de données](#modèle-de-données)
3. [API pour le panier](#api-pour-le-panier)
4. [Exemples d'utilisation](#exemples-dutilisation)
5. [Middleware et cookies](#middleware-et-cookies)
6. [Bonnes pratiques](#bonnes-pratiques)

## Introduction

Le système de panier permet aux utilisateurs de sauvegarder des produits qu'ils souhaitent acheter. Il prend en charge les utilisateurs connectés et non connectés, en utilisant des cookies pour stocker l'ID de session du panier.

### Principales fonctionnalités

- Panier persistant pour les utilisateurs connectés et non connectés
- Ajout, mise à jour et suppression d'articles
- Support pour les variantes de produits
- Calcul automatique des totaux
- Fusion de paniers lors de la connexion d'un utilisateur

## Modèle de données

### Panier

Le modèle `Panier` représente un panier d'achat.

```mermaid
classDiagram
    class Panier {
        +id: int
        +guest_id: uuid
        +client_id: int
        +created_at: timestamp
        +updated_at: timestamp
        +getTotal()
        +addItem(produit_id, quantite, variante_id)
        +updateItem(item_id, quantite)
        +removeItem(item_id)
        +clear()
    }
```

### Article de panier

Le modèle `PanierItem` représente un article dans un panier.

```mermaid
classDiagram
    class PanierItem {
        +id: int
        +panier_id: int
        +produit_id: int
        +variante_id: int
        +quantite: int
        +prix_unitaire: decimal
        +created_at: timestamp
        +updated_at: timestamp
        +getSubtotal()
    }
```

### Relations

```mermaid
classDiagram
    class Panier {
        +id: int
        +guest_id: uuid
        +client_id: int
    }

    class PanierItem {
        +id: int
        +panier_id: int
        +produit_id: int
        +variante_id: int
        +quantite: int
        +prix_unitaire: decimal
    }

    class Produit {
        +id: int
        +nom_produit: string
        +prix_produit: decimal
        +quantite_produit: int
    }

    class ProduitVariante {
        +id: int
        +produit_parent_id: int
        +sku: string
        +prix: decimal
        +stock: int
        +actif: boolean
    }

    class Client {
        +id: int
        +user_id: string
        +nom: string
        +prenom: string
        +email: string
    }

    Panier "1" -- "0..*" PanierItem : contient
    PanierItem "0..*" -- "1" Produit : référence
    PanierItem "0..*" -- "0..1" ProduitVariante : peut avoir
    Panier "0..*" -- "0..1" Client : appartient à
```

## API pour le panier

### Obtenir le contenu du panier

**Endpoint**: `GET /api/panier`

**Description**: Récupère le contenu du panier de l'utilisateur actuel.

**Réponse**:
```json
{
  "panier": {
    "id": 1,
    "guest_id": "550e8400-e29b-41d4-a716-************",
    "client_id": null,
    "created_at": "2023-01-01T00:00:00.000000Z",
    "updated_at": "2023-01-01T00:00:00.000000Z",
    "items": [
      {
        "id": 1,
        "panier_id": 1,
        "produit_id": 1,
        "variante_id": 2,
        "quantite": 2,
        "prix_unitaire": 19.99,
        "created_at": "2023-01-01T00:00:00.000000Z",
        "updated_at": "2023-01-01T00:00:00.000000Z",
        "produit": {
          "id": 1,
          "nom_produit": "Produit 1",
          "prix_produit": 19.99,
          "image_produit": "produit1.jpg"
        },
        "variante": {
          "id": 2,
          "produit_parent_id": 1,
          "sku": "PROD1-VAR2",
          "prix": 19.99,
          "stock": 10
        }
      }
    ]
  },
  "total": 39.98
}
```

### Ajouter un article au panier

**Endpoint**: `POST /api/panier/ajouter`

**Description**: Ajoute un article au panier.

**Paramètres**:
- `produit_id` (obligatoire): ID du produit
- `quantite` (obligatoire): Quantité
- `variante_id` (optionnel): ID de la variante

**Réponse**:
```json
{
  "message": "Article ajouté au panier",
  "panier": {
    "id": 1,
    "guest_id": "550e8400-e29b-41d4-a716-************",
    "client_id": null,
    "created_at": "2023-01-01T00:00:00.000000Z",
    "updated_at": "2023-01-01T00:00:00.000000Z",
    "items": [
      {
        "id": 1,
        "panier_id": 1,
        "produit_id": 1,
        "variante_id": 2,
        "quantite": 2,
        "prix_unitaire": 19.99,
        "created_at": "2023-01-01T00:00:00.000000Z",
        "updated_at": "2023-01-01T00:00:00.000000Z"
      }
    ]
  },
  "total": 39.98
}
```

### Mettre à jour un article du panier

**Endpoint**: `PUT /api/panier/items/{itemId}`

**Description**: Met à jour la quantité d'un article dans le panier.

**Paramètres**:
- `quantite` (obligatoire): Nouvelle quantité

**Réponse**:
```json
{
  "message": "Article mis à jour",
  "panier": {
    "id": 1,
    "guest_id": "550e8400-e29b-41d4-a716-************",
    "client_id": null,
    "created_at": "2023-01-01T00:00:00.000000Z",
    "updated_at": "2023-01-01T00:00:00.000000Z",
    "items": [
      {
        "id": 1,
        "panier_id": 1,
        "produit_id": 1,
        "variante_id": 2,
        "quantite": 3,
        "prix_unitaire": 19.99,
        "created_at": "2023-01-01T00:00:00.000000Z",
        "updated_at": "2023-01-01T00:00:00.000000Z"
      }
    ]
  },
  "total": 59.97
}
```

### Supprimer un article du panier

**Endpoint**: `DELETE /api/panier/items/{itemId}`

**Description**: Supprime un article du panier.

**Réponse**:
```json
{
  "message": "Article supprimé",
  "panier": {
    "id": 1,
    "guest_id": "550e8400-e29b-41d4-a716-************",
    "client_id": null,
    "created_at": "2023-01-01T00:00:00.000000Z",
    "updated_at": "2023-01-01T00:00:00.000000Z",
    "items": []
  },
  "total": 0
}
```

### Vider le panier

**Endpoint**: `DELETE /api/panier/vider`

**Description**: Supprime tous les articles du panier.

**Réponse**:
```json
{
  "message": "Panier vidé",
  "panier": {
    "id": 1,
    "guest_id": "550e8400-e29b-41d4-a716-************",
    "client_id": null,
    "created_at": "2023-01-01T00:00:00.000000Z",
    "updated_at": "2023-01-01T00:00:00.000000Z",
    "items": []
  },
  "total": 0
}
```

## Exemples d'utilisation

### Obtenir le contenu du panier

```http
GET /api/panier
Cookie: cart_guest_id=550e8400-e29b-41d4-a716-************
```

### Ajouter un produit au panier

```http
POST /api/panier/ajouter
Content-Type: application/json
Cookie: cart_guest_id=550e8400-e29b-41d4-a716-************

{
  "produit_id": 1,
  "quantite": 2
}
```

### Ajouter une variante de produit au panier

```http
POST /api/panier/ajouter
Content-Type: application/json
Cookie: cart_guest_id=550e8400-e29b-41d4-a716-************

{
  "produit_id": 1,
  "variante_id": 2,
  "quantite": 2
}
```

### Mettre à jour la quantité d'un article

```http
PUT /api/panier/items/1
Content-Type: application/json
Cookie: cart_guest_id=550e8400-e29b-41d4-a716-************

{
  "quantite": 3
}
```

### Supprimer un article du panier

```http
DELETE /api/panier/items/1
Cookie: cart_guest_id=550e8400-e29b-41d4-a716-************
```

### Vider le panier

```http
DELETE /api/panier/vider
Cookie: cart_guest_id=550e8400-e29b-41d4-a716-************
```

## Middleware et cookies

Le système de panier utilise un middleware `CookieCartMiddleware` pour gérer les cookies d'identification du panier.

### Fonctionnement du middleware

1. Le middleware vérifie si un cookie `cart_guest_id` existe dans la requête
2. Si le cookie n'existe pas, il génère un nouvel ID invité (UUID) et l'ajoute à la requête
3. Si le cookie existe, il l'ajoute à la requête
4. Le middleware ajoute également un cookie `cart_guest_id` à la réponse

### Configuration des cookies

Les cookies sont configurés avec les paramètres suivants:

- `httpOnly`: true (le cookie n'est pas accessible via JavaScript)
- `secure`: true en production (le cookie n'est envoyé que via HTTPS)
- `sameSite`: 'lax' (le cookie est envoyé lors de la navigation depuis un site externe)
- `expires`: 30 jours (durée de vie du cookie)

### Fusion de paniers

Lorsqu'un utilisateur se connecte, le système fusionne automatiquement le panier invité avec le panier de l'utilisateur:

1. Le système recherche un panier associé à l'ID invité (guest_id)
2. Le système recherche un panier associé à l'ID de l'utilisateur
3. Si les deux paniers existent, le système fusionne les articles du panier invité dans le panier de l'utilisateur
4. Si seul le panier invité existe, le système l'associe à l'utilisateur
5. Si seul le panier de l'utilisateur existe, le système l'utilise

## Bonnes pratiques

### Gestion des cookies

- Assurez-vous que votre frontend envoie les cookies avec les requêtes
- Utilisez `credentials: 'include'` avec fetch ou `withCredentials: true` avec axios
- Configurez CORS pour accepter les cookies

### Validation des données

- Validez toujours les données côté serveur
- Vérifiez que les produits et variantes existent
- Vérifiez que les quantités sont valides
- Vérifiez que le stock est suffisant

### Performance

- Utilisez des requêtes optimisées pour récupérer les produits et variantes
- Mettez en cache les données fréquemment utilisées
- Utilisez des transactions pour les opérations complexes

### Sécurité

- Validez que l'utilisateur a accès au panier
- Protégez les routes sensibles
- Utilisez HTTPS pour toutes les requêtes
