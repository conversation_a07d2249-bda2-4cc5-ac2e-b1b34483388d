# Gestion du Contenu

Ce document décrit les APIs de gestion du contenu pour l'application, notamment les carousels et les catégories mises en avant.

## Table des matières

1. [Carousels](#carousels)
   - [Structure des données](#structure-des-données-carousel)
   - [Endpoints API](#endpoints-api-carousel)
2. [Slides de Carousel](#slides-de-carousel)
   - [Structure des données](#structure-des-données-slide)
   - [Endpoints API](#endpoints-api-slide)
   - [Gestion des images](#gestion-des-images-de-slide)
3. [Catégories mises en avant](#catégories-mises-en-avant)
   - [Structure des données](#structure-des-données-catégorie)
   - [Endpoints API](#endpoints-api-catégorie)

## Carousels

Les carousels permettent d'afficher des bannières défilantes sur la page d'accueil ou d'autres pages du site.

### Structure des données (Carousel)

| Champ | Type | Description |
|-------|------|-------------|
| id | int | Identifiant unique |
| nom | string | Nom du carousel |
| description | text | Description du carousel (optionnel) |
| actif | boolean | Statut actif du carousel |
| ordre | int | Ordre d'affichage |
| created_at | timestamp | Date de création |
| updated_at | timestamp | Date de dernière modification |
| deleted_at | timestamp | Date de suppression (soft delete) |

### Endpoints API (Carousel)

#### Récupérer tous les carousels

```http
GET /api/carousels
```

Paramètres optionnels:
- `actif` (boolean): Filtrer par statut actif

#### Récupérer un carousel spécifique

```http
GET /api/carousels/{id}
```

#### Créer un nouveau carousel

```http
POST /api/carousels
```

Paramètres:
- `nom` (string, requis): Nom du carousel
- `description` (string, optionnel): Description du carousel
- `actif` (boolean, optionnel, défaut: true): Statut actif
- `ordre` (integer, optionnel, défaut: 0): Ordre d'affichage

#### Mettre à jour un carousel

```http
PUT /api/carousels/{id}
```

Paramètres:
- `nom` (string, optionnel): Nom du carousel
- `description` (string, optionnel): Description du carousel
- `actif` (boolean, optionnel): Statut actif
- `ordre` (integer, optionnel): Ordre d'affichage

#### Supprimer un carousel

```http
DELETE /api/carousels/{id}
```

#### Récupérer les carousels actifs

```http
GET /api/carousels/actifs
```

Récupère uniquement les carousels actifs avec leurs slides actifs, triés par ordre.

## Slides de Carousel

Les slides sont les éléments individuels d'un carousel, contenant une image, un titre, une description et des informations de bouton.

### Structure des données (Slide)

| Champ | Type | Description |
|-------|------|-------------|
| id | int | Identifiant unique |
| carousel_id | int | ID du carousel parent |
| titre | string | Titre du slide |
| description | text | Description du slide (optionnel) |
| bouton_texte | string | Texte du bouton (optionnel) |
| bouton_lien | string | Lien du bouton (optionnel) |
| ordre | int | Ordre d'affichage |
| actif | boolean | Statut actif du slide |
| created_at | timestamp | Date de création |
| updated_at | timestamp | Date de dernière modification |
| deleted_at | timestamp | Date de suppression (soft delete) |

### Endpoints API (Slide)

#### Récupérer les slides d'un carousel

```http
GET /api/carousels/slides
```

Paramètres requis:
- `carousel_id` (integer): ID du carousel

Paramètres optionnels:
- `actif` (boolean): Filtrer par statut actif

#### Récupérer un slide spécifique

```http
GET /api/carousels/slides/{id}
```

#### Créer un nouveau slide

```http
POST /api/carousels/slides
```

Paramètres:
- `carousel_id` (integer, requis): ID du carousel parent
- `titre` (string, requis): Titre du slide
- `description` (string, optionnel): Description du slide
- `bouton_texte` (string, optionnel): Texte du bouton
- `bouton_lien` (string, optionnel): Lien du bouton
- `ordre` (integer, optionnel, défaut: 0): Ordre d'affichage
- `actif` (boolean, optionnel, défaut: true): Statut actif

#### Mettre à jour un slide

```http
PUT /api/carousels/slides/{id}
```

Paramètres:
- `titre` (string, optionnel): Titre du slide
- `description` (string, optionnel): Description du slide
- `bouton_texte` (string, optionnel): Texte du bouton
- `bouton_lien` (string, optionnel): Lien du bouton
- `ordre` (integer, optionnel): Ordre d'affichage
- `actif` (boolean, optionnel): Statut actif

#### Supprimer un slide

```http
DELETE /api/carousels/slides/{id}
```

#### Réorganiser les slides

```http
POST /api/carousels/slides/reorder
```

Paramètres:
- `carousel_id` (integer, requis): ID du carousel
- `slides` (array, requis): Tableau d'objets contenant:
  - `id` (integer, requis): ID du slide
  - `ordre` (integer, requis): Nouvel ordre d'affichage

### Gestion des images de slide

Pour ajouter une image à un slide, utilisez l'API d'images existante:

```http
POST /api/images/upload
```

Paramètres:
- `model_type` (string, requis): Doit être `carousel_slide`
- `model_id` (integer, requis): ID du slide
- `image` (file, requis): Fichier image
- `is_primary` (boolean, optionnel): Définir comme image principale
- `alt_text` (string, optionnel): Texte alternatif pour l'accessibilité
- `title` (string, optionnel): Titre de l'image

## Catégories mises en avant

Les catégories mises en avant permettent de mettre en valeur certaines catégories sur la page d'accueil ou d'autres pages du site.

### Structure des données (Catégorie)

Les catégories utilisent le modèle `Categorie` existant, avec les champs supplémentaires suivants:

| Champ | Type | Description |
|-------|------|-------------|
| featured | boolean | Indique si la catégorie est mise en avant |
| featured_order | int | Ordre d'affichage pour les catégories mises en avant |

### Endpoints API (Catégorie)

#### Récupérer toutes les catégories mises en avant

```http
GET /api/categories/featured
```

Récupère uniquement les catégories marquées comme "featured", triées par featured_order.

#### Définir une catégorie comme mise en avant

```http
PUT /api/categories/{id}/featured
```

Paramètres:
- `featured` (boolean, requis): Statut de mise en avant
- `featured_order` (integer, optionnel): Ordre d'affichage

#### Réorganiser les catégories mises en avant

```http
POST /api/categories/featured/reorder
```

Paramètres:
- `categories` (array, requis): Tableau d'objets contenant:
  - `id` (integer, requis): ID de la catégorie
  - `featured_order` (integer, requis): Nouvel ordre d'affichage
