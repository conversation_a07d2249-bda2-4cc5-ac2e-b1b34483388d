// Service for fetching attributs and variants
const API_URL = import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';

export async function fetchProductAttributs(productId) {
  const res = await fetch(`${API_URL}/produits/${productId}/attributs`);
  if (!res.ok) throw new Error('Erreur lors du chargement des attributs du produit');
  return await res.json();
}

export async function fetchSousSousCategorieAttributs(sousSousCategorieId) {
  const res = await fetch(`${API_URL}/sous_sousCategories/${sousSousCategorieId}/attributs`);
  if (!res.ok) throw new Error('Erreur lors du chargement des attributs de la sous-sous-catégorie');
  return await res.json();
}

export async function fetchProductVariantes(productId) {
  const res = await fetch(`${API_URL}/produits/${productId}/variantes`);
  if (!res.ok) throw new Error('Erreur lors du chargement des variantes du produit');
  return await res.json();
}
