import React from 'react';
import { Button, Table, Form, Modal } from 'react-bootstrap';

const ProductStepVariants = ({
  variantes,
  setVariantes,
  newVariante,
  setNewVariante,
  showVarianteModal,
  setShowVarianteModal,
  handleVarianteImageChange,
  varianteImagePreview,
  setVarianteImagePreview,
  attributs,
  handleVarianteAttributChange,
  addVariante,
  removeVariante,
  error
}) => (
  <>
    <Button variant="secondary" onClick={() => setShowVarianteModal(true)}>
      Ajouter une variante
    </Button>
    <Table striped bordered hover className="mt-3">
      <thead>
        <tr>
          <th>SKU</th>
          <th>Prix supplément</th>
          <th>Quantité</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        {variantes.map((v, idx) => (
          <tr key={idx}>
            <td>{v.sku}</td>
            <td>{v.prix_supplement}</td>
            <td>{v.quantite}</td>
            <td>
              <Button variant="danger" size="sm" onClick={() => removeVariante(idx)}>
                Supprimer
              </Button>
            </td>
          </tr>
        ))}
      </tbody>
    </Table>
    <Modal show={showVarianteModal} onHide={() => setShowVarianteModal(false)} size="md" centered>
      <Modal.Header closeButton>
        <Modal.Title>Ajouter une variante</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form>
          <Form.Group controlId="sku">
            <Form.Label>SKU</Form.Label>
            <Form.Control
              type="text"
              value={newVariante.sku || ''}
              onChange={(e) => setNewVariante((prev) => ({ ...prev, sku: e.target.value }))}
            />
          </Form.Group>
          <Form.Group controlId="prix_supplement">
            <Form.Label>Prix supplément</Form.Label>
            <Form.Control
              type="number"
              value={newVariante.prix_supplement || 0}
              onChange={(e) => setNewVariante((prev) => ({ ...prev, prix_supplement: e.target.value }))}
            />
          </Form.Group>
          <Form.Group controlId="quantite">
            <Form.Label>Quantité</Form.Label>
            <Form.Control
              type="number"
              value={newVariante.quantite || 0}
              onChange={(e) => setNewVariante((prev) => ({ ...prev, quantite: e.target.value }))}
            />
          </Form.Group>
          <Form.Group controlId="image">
            <Form.Label>Image</Form.Label>
            <Form.Control type="file" accept="image/*" onChange={handleVarianteImageChange} />
            {varianteImagePreview && <img src={varianteImagePreview} alt="Aperçu" style={{ maxWidth: 100, marginTop: 10 }} />}
          </Form.Group>
          {/* Add dynamic attributs fields here if needed */}
        </Form>
        {error && <div className="text-danger mt-2">{error}</div>}
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={() => setShowVarianteModal(false)}>
          Annuler
        </Button>
        <Button variant="primary" onClick={addVariante}>
          Ajouter la variante
        </Button>
      </Modal.Footer>
    </Modal>
  </>
);

export default ProductStepVariants;
