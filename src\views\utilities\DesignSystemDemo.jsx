import React, { useState } from 'react';
import { <PERSON>, Grid, Typo<PERSON>, <PERSON>, Badge } from '@mui/material';
import { IconPlus, IconEdit, IconTrash, IconEye } from '@tabler/icons-react';

// Standardized components
import StandardTable from '../../components/StandardTable';
import StandardButton from '../../components/StandardButton';
import StandardCard from '../../components/StandardCard';
import MainCard from '../../ui-component/cards/MainCard';

// Design system
import { COLORS, TYPOGRAPHY } from '../../themes/designSystem';

/**
 * Design System Demo Page
 * Showcases all standardized components and styling patterns
 */
const DesignSystemDemo = () => {
  const [loading, setLoading] = useState(false);

  // Sample data for table demo
  const sampleData = [
    { id: 1, name: '<PERSON>', email: '<EMAIL>', status: 'active', role: 'Admin' },
    { id: 2, name: '<PERSON>', email: '<EMAIL>', status: 'inactive', role: 'User' },
    { id: 3, name: '<PERSON>', email: '<EMAIL>', status: 'active', role: 'Manager' },
    { id: 4, name: '<PERSON>', email: '<EMAIL>', status: 'pending', role: 'User' }
  ];

  // Table columns configuration
  const columns = [
    { id: 'id', label: 'ID', minWidth: 70 },
    { id: 'name', label: 'Name', minWidth: 150 },
    { id: 'email', label: 'Email', minWidth: 200 },
    { id: 'status', label: 'Status', minWidth: 120 },
    { id: 'role', label: 'Role', minWidth: 100 },
    { id: 'actions', label: 'Actions', minWidth: 150 }
  ];

  // Custom cell renderer for table
  const renderCell = (column, row, value) => {
    switch (column.id) {
      case 'status':
        return (
          <Chip
            label={value}
            color={value === 'active' ? 'success' : value === 'inactive' ? 'error' : 'warning'}
            size="small"
            sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary }}
          />
        );
      case 'actions':
        return (
          <Box sx={{ display: 'flex', gap: 1 }}>
            <StandardButton variant="outline" size="small" startIcon={<IconEye size={16} />}>
              View
            </StandardButton>
            <StandardButton variant="primary" size="small" startIcon={<IconEdit size={16} />}>
              Edit
            </StandardButton>
            <StandardButton variant="error" size="small" startIcon={<IconTrash size={16} />}>
              Delete
            </StandardButton>
          </Box>
        );
      default:
        return value;
    }
  };

  const handleButtonClick = (variant) => {
    setLoading(true);
    setTimeout(() => setLoading(false), 2000);
  };

  return (
    <MainCard title="Design System Demo">
      <Box sx={{ width: '100%' }}>
        {/* Typography Section */}
        <StandardCard title="Typography" subtitle="Standardized text styles" sx={{ mb: 3 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="h3" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary, mb: 1 }}>
                Heading 3 - Primary Font
              </Typography>
              <Typography variant="h4" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary, mb: 1 }}>
                Heading 4 - Primary Font
              </Typography>
              <Typography variant="h5" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary, mb: 1 }}>
                Heading 5 - Primary Font
              </Typography>
              <Typography variant="body1" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary, mb: 1 }}>
                Body text - Primary Font (Inter)
              </Typography>
              <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary, color: COLORS.text.secondary }}>
                Secondary text - Primary Font
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h4" sx={{ fontFamily: TYPOGRAPHY.fontFamily.secondary, mb: 1 }}>
                Heading - Secondary Font (Poppins)
              </Typography>
              <Typography variant="body1" sx={{ fontFamily: TYPOGRAPHY.fontFamily.secondary, mb: 1 }}>
                Body text - Secondary Font
              </Typography>
              <Typography variant="body1" sx={{ fontFamily: TYPOGRAPHY.fontFamily.monospace, mb: 1 }}>
                Monospace text - Code font
              </Typography>
            </Grid>
          </Grid>
        </StandardCard>

        {/* Colors Section */}
        <StandardCard title="Colors" subtitle="Standardized color palette" sx={{ mb: 3 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" sx={{ mb: 2, fontFamily: TYPOGRAPHY.fontFamily.primary }}>
                Primary Colors
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
                <Box sx={{ width: 60, height: 60, backgroundColor: COLORS.primary.main, borderRadius: 1 }} />
                <Box sx={{ width: 60, height: 60, backgroundColor: COLORS.primary.light, borderRadius: 1 }} />
                <Box sx={{ width: 60, height: 60, backgroundColor: COLORS.primary.dark, borderRadius: 1 }} />
              </Box>

              <Typography variant="h6" sx={{ mb: 2, fontFamily: TYPOGRAPHY.fontFamily.primary }}>
                Status Colors
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
                <Box sx={{ width: 60, height: 60, backgroundColor: COLORS.success.main, borderRadius: 1 }} />
                <Box sx={{ width: 60, height: 60, backgroundColor: COLORS.warning.main, borderRadius: 1 }} />
                <Box sx={{ width: 60, height: 60, backgroundColor: COLORS.error.main, borderRadius: 1 }} />
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" sx={{ mb: 2, fontFamily: TYPOGRAPHY.fontFamily.primary }}>
                Grey Scale
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
                <Box sx={{ width: 40, height: 40, backgroundColor: COLORS.grey[50], border: '1px solid #ccc', borderRadius: 1 }} />
                <Box sx={{ width: 40, height: 40, backgroundColor: COLORS.grey[100], borderRadius: 1 }} />
                <Box sx={{ width: 40, height: 40, backgroundColor: COLORS.grey[200], borderRadius: 1 }} />
                <Box sx={{ width: 40, height: 40, backgroundColor: COLORS.grey[300], borderRadius: 1 }} />
                <Box sx={{ width: 40, height: 40, backgroundColor: COLORS.grey[500], borderRadius: 1 }} />
                <Box sx={{ width: 40, height: 40, backgroundColor: COLORS.grey[700], borderRadius: 1 }} />
                <Box sx={{ width: 40, height: 40, backgroundColor: COLORS.grey[900], borderRadius: 1 }} />
              </Box>
            </Grid>
          </Grid>
        </StandardCard>

        {/* Buttons Section */}
        <StandardCard title="Buttons" subtitle="Standardized button components" sx={{ mb: 3 }}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ mb: 2, fontFamily: TYPOGRAPHY.fontFamily.primary }}>
                Modern Button Variants with Gradients & Animations
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
                <StandardButton variant="primary" onClick={() => handleButtonClick('primary')}>
                  Primary Gradient
                </StandardButton>
                <StandardButton variant="secondary" onClick={() => handleButtonClick('secondary')}>
                  Secondary Gradient
                </StandardButton>
                <StandardButton variant="success" onClick={() => handleButtonClick('success')}>
                  Success Gradient
                </StandardButton>
                <StandardButton variant="error" onClick={() => handleButtonClick('error')}>
                  Error Gradient
                </StandardButton>
                <StandardButton variant="warning" onClick={() => handleButtonClick('warning')}>
                  Warning Gradient
                </StandardButton>
                <StandardButton variant="outline" onClick={() => handleButtonClick('outline')}>
                  Modern Outline
                </StandardButton>
                <StandardButton variant="ghost" onClick={() => handleButtonClick('ghost')}>
                  Ghost Style
                </StandardButton>
              </Box>

              <Typography variant="h6" sx={{ mb: 2, fontFamily: TYPOGRAPHY.fontFamily.primary }}>
                Button Sizes & States
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap', alignItems: 'center' }}>
                <StandardButton variant="primary" size="small">
                  Small
                </StandardButton>
                <StandardButton variant="primary" size="medium">
                  Medium
                </StandardButton>
                <StandardButton variant="primary" size="large">
                  Large
                </StandardButton>
                <StandardButton variant="primary" loading={loading} onClick={() => handleButtonClick('loading')}>
                  {loading ? 'Loading...' : 'Click to Load'}
                </StandardButton>
                <StandardButton variant="primary" disabled>
                  Disabled
                </StandardButton>
              </Box>

              <Typography variant="h6" sx={{ mb: 2, fontFamily: TYPOGRAPHY.fontFamily.primary }}>
                Buttons with Icons
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <StandardButton variant="primary" startIcon={<IconPlus size={16} />}>
                  Add New
                </StandardButton>
                <StandardButton variant="outline" startIcon={<IconEdit size={16} />}>
                  Edit
                </StandardButton>
                <StandardButton variant="error" startIcon={<IconTrash size={16} />}>
                  Delete
                </StandardButton>
              </Box>
            </Grid>
          </Grid>
        </StandardCard>

        {/* Modern Effects Showcase */}
        <StandardCard title="Modern Visual Effects" subtitle="Enhanced animations, gradients, and shadows" sx={{ mb: 3 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Box
                sx={{
                  p: 3,
                  borderRadius: '16px',
                  background: `linear-gradient(135deg, ${COLORS.primary.main} 0%, ${COLORS.primary[600]} 100%)`,
                  color: 'white',
                  textAlign: 'center',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  cursor: 'pointer',
                  '&:hover': {
                    transform: 'translateY(-8px) scale(1.02)',
                    boxShadow: '0 20px 40px rgba(59, 130, 246, 0.3)'
                  }
                }}
              >
                <Typography variant="h6" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary, mb: 1 }}>
                  Gradient Card
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Hover for animation
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box
                sx={{
                  p: 3,
                  borderRadius: '16px',
                  background: `linear-gradient(135deg, ${COLORS.success.main} 0%, ${COLORS.success[600]} 100%)`,
                  color: 'white',
                  textAlign: 'center',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  cursor: 'pointer',
                  '&:hover': {
                    transform: 'translateY(-8px) scale(1.02)',
                    boxShadow: '0 20px 40px rgba(16, 185, 129, 0.3)'
                  }
                }}
              >
                <Typography variant="h6" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary, mb: 1 }}>
                  Success Gradient
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Modern shadows
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box
                sx={{
                  p: 3,
                  borderRadius: '16px',
                  background: `linear-gradient(135deg, ${COLORS.secondary.main} 0%, ${COLORS.secondary[600]} 100%)`,
                  color: 'white',
                  textAlign: 'center',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  cursor: 'pointer',
                  '&:hover': {
                    transform: 'translateY(-8px) scale(1.02)',
                    boxShadow: '0 20px 40px rgba(139, 92, 246, 0.3)'
                  }
                }}
              >
                <Typography variant="h6" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary, mb: 1 }}>
                  Purple Gradient
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Smooth transitions
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </StandardCard>

        {/* Enhanced Table Section */}
        <StandardCard title="Enhanced Table" subtitle="Modern table with gradients, animations, and improved UX" sx={{ mb: 3 }}>
          <StandardTable
            columns={columns}
            data={sampleData}
            renderCell={renderCell}
            hover={true}
            pagination={{
              page: 1,
              totalPages: 3,
              onPageChange: (page) => console.log('Page changed to:', page)
            }}
          />
        </StandardCard>

        {/* Usage Instructions */}
        <StandardCard title="Modern Design System Usage" subtitle="How to use the enhanced standardized components">
          <Typography variant="body1" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary, mb: 2 }}>
            Our enhanced design system features modern gradients, smooth animations, and professional styling:
          </Typography>
          <Box component="ul" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary, color: COLORS.text.primary, mb: 3 }}>
            <li>
              <strong>StandardTable</strong> - Enhanced with gradient headers, hover animations, and modern shadows
            </li>
            <li>
              <strong>StandardButton</strong> - Features gradient backgrounds, hover effects, and shimmer animations
            </li>
            <li>
              <strong>StandardCard</strong> - Includes gradient borders, hover lift effects, and modern styling
            </li>
            <li>
              <strong>Design System</strong> - Extended color palette with 50-900 shades and enhanced shadows
            </li>
            <li>
              <strong>Modern Effects</strong> - Cubic-bezier transitions, transform animations, and colored shadows
            </li>
          </Box>

          <Typography variant="h6" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary, mb: 2, color: COLORS.primary.main }}>
            ✨ New Features:
          </Typography>
          <Box component="ul" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary, color: COLORS.text.primary }}>
            <li>
              🎨 <strong>Gradient Backgrounds</strong> - Modern linear gradients for buttons and cards
            </li>
            <li>
              🎭 <strong>Hover Animations</strong> - Smooth transform effects and shadow changes
            </li>
            <li>
              🌈 <strong>Enhanced Colors</strong> - Extended palette with 50-900 shades
            </li>
            <li>
              💫 <strong>Shimmer Effects</strong> - Subtle light animations on buttons
            </li>
            <li>
              🎯 <strong>Better UX</strong> - Improved visual feedback and interactions
            </li>
            <li>
              📱 <strong>Modern Design</strong> - Contemporary styling following latest trends
            </li>
          </Box>
        </StandardCard>
      </Box>
    </MainCard>
  );
};

export default DesignSystemDemo;
