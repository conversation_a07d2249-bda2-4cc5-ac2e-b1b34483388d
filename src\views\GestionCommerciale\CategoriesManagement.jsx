import React, { useEffect, useState } from 'react';
import {
  fetchCategories,
  createCategory,
  updateCategory,
  deleteCategory,
  fetchAllSousCategories,
  createSousCategorie,
  updateSousCategorie,
  deleteSousCategorie,
  fetchModelImages
} from '../../services/categoryService';
import { Table, Button, Form, Alert, Spinner, Container, Row, Col, Card, Tabs, Tab, Badge, Breadcrumb } from 'react-bootstrap';
import { Box, Typography } from '@mui/material';
import SousSousCategories from './SousSousCategories';
import FeaturedCategories from './FeaturedCategories';
import ImageManager from './ImageManager';
import { FaPlus, FaPencilAlt, FaTrashAlt, FaHome, FaLayerGroup, FaStar } from 'react-icons/fa';
import ProfessionalModal from '../../ui-component/extended/ProfessionalModal';
import { FormModal, ConfirmationModal } from '../../ui-component/extended/ModalVariants';

// Design system
import { COLORS, TYPOGRAPHY } from '../../themes/designSystem';
import MainCard from '../../ui-component/cards/MainCard';
import StandardButton from '../../components/StandardButton';
import StandardTable from '../../components/StandardTable';
import StandardCard from '../../components/StandardCard';

const initialCategoryForm = { nom: '', description: '', image: '' };
const initialSousCategoryForm = { nom: '', description: '', categorie_id: '' };

export default function CategoriesManagement() {
  // Columns for categories table
  const categoriesColumns = [
    { id: 'id', label: 'ID', minWidth: 60 },
    { id: 'nom', label: 'Nom', minWidth: 200 },
    { id: 'description', label: 'Description', minWidth: 250 },
    { id: 'image', label: 'Image', minWidth: 150 },
    { id: 'actions', label: 'Actions', minWidth: 200, align: 'center' }
  ];

  // Columns for sous-categories table
  const sousCategoriesColumns = [
    { id: 'id', label: 'ID', minWidth: 60 },
    { id: 'nom', label: 'Nom', minWidth: 150 },
    { id: 'description', label: 'Description', minWidth: 200 },
    { id: 'image', label: 'Image', minWidth: 120 },
    { id: 'parent', label: 'Catégorie parente', minWidth: 150 },
    { id: 'actions', label: 'Actions', minWidth: 200, align: 'center' }
  ];

  // Categories
  const [categories, setCategories] = useState([]);
  const [catLoading, setCatLoading] = useState(true);
  const [catError, setCatError] = useState('');
  const [catForm, setCatForm] = useState(initialCategoryForm);
  const [catEditingId, setCatEditingId] = useState(null);
  const [catSubmitting, setCatSubmitting] = useState(false);

  // Sous-categories
  const [sousCategories, setSousCategories] = useState([]);
  const [sousCatLoading, setSousCatLoading] = useState(true);
  const [sousCatError, setSousCatError] = useState('');
  const [sousCatForm, setSousCatForm] = useState(initialSousCategoryForm);
  const [sousCatImageFile, setSousCatImageFile] = useState(null);
  const [sousCatEditingId, setSousCatEditingId] = useState(null);
  const [sousCatSubmitting, setSousCatSubmitting] = useState(false);

  // Modal states
  const [showCatModal, setShowCatModal] = useState(false);
  const [catModalAction, setCatModalAction] = useState('create'); // 'create' or 'edit'
  const [showSousCatModal, setShowSousCatModal] = useState(false);
  const [sousCatModalAction, setSousCatModalAction] = useState('create'); // 'create' or 'edit'

  // Delete confirmation modal states
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [itemToDelete, setItemToDelete] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const [tab, setTab] = useState('categories');

  // Load data
  const loadCategories = async () => {
    setCatLoading(true);
    setCatError('');
    try {
      const data = await fetchCategories();
      setCategories(data);
    } catch (e) {
      setCatError(e.message);
    }
    setCatLoading(false);
  };
  const loadSousCategories = async () => {
    setSousCatLoading(true);
    setSousCatError('');
    try {
      const data = await fetchAllSousCategories();

      // Fetch images for each sous category
      const categoriesWithImages = await Promise.all(
        data.map(async (sc) => {
          try {
            const images = await fetchModelImages('sous_categorie', sc.id);
            const primaryImage = images.find((img) => img.is_primary) || images[0];
            return {
              ...sc,
              primary_image_url: primaryImage ? `/api/images/serve/${primaryImage.id}` : null,
              images: images
            };
          } catch (error) {
            console.warn(`Failed to fetch images for sous category ${sc.id}:`, error);
            return { ...sc, primary_image_url: null, images: [] };
          }
        })
      );

      setSousCategories(categoriesWithImages);
    } catch (e) {
      setSousCatError(e.message);
    }
    setSousCatLoading(false);
  };

  useEffect(() => {
    loadCategories();
    loadSousCategories();
  }, []);

  // Category handlers
  const handleCatChange = (e) => {
    const newForm = { ...catForm, [e.target.name]: e.target.value };
    console.log(`📝 Category form field changed: ${e.target.name} = ${e.target.value}`);
    console.log('📋 Updated category form:', newForm);
    setCatForm(newForm);
  };
  const handleCatSubmit = async (e) => {
    e.preventDefault();
    setCatSubmitting(true);
    setCatError('');

    try {
      console.log(`📝 ${catEditingId ? 'Updating' : 'Creating'} category:`, catForm);

      if (catEditingId) {
        console.log(`🔄 Updating category with ID: ${catEditingId}`);
        await updateCategory(catEditingId, catForm);
        console.log('✅ Category updated successfully');
      } else {
        console.log('➕ Creating new category');
        await createCategory(catForm);
        console.log('✅ Category created successfully');
      }

      setCatForm(initialCategoryForm);
      setCatEditingId(null);
      setShowCatModal(false);
      loadCategories();
    } catch (e) {
      console.error('❌ Error submitting category:', e);
      setCatError(e.message);
    }
    setCatSubmitting(false);
  };

  // Open category modal for creating
  const handleCatCreate = () => {
    setCatModalAction('create');
    setCatEditingId(null);
    setCatForm(initialCategoryForm);
    setShowCatModal(true);
  };

  // Open category modal for editing
  const handleCatEdit = (cat) => {
    console.log('✏️ Opening category edit modal for:', cat);
    setCatModalAction('edit');
    const formData = {
      nom: cat.nom || cat.nom_categorie,
      description: cat.description || cat.description_categorie,
      image: cat.image || cat.image_categorie
    };
    console.log('📝 Setting category form data:', formData);
    setCatForm(formData);
    setCatEditingId(cat.id);
    setShowCatModal(true);
  };

  // Close category modal
  const handleCatModalClose = () => {
    setShowCatModal(false);
    setCatEditingId(null);
    setCatForm(initialCategoryForm);
    setCatError('');
  };
  // Confirm delete
  const confirmDelete = (type, id, name) => {
    setItemToDelete({ type, id, name });
    setShowDeleteModal(true);
  };

  // Handle delete
  const handleDelete = async () => {
    if (!itemToDelete) return;

    setDeleteLoading(true);
    setCatError('');
    setSousCatError('');

    try {
      const { type, id } = itemToDelete;
      console.log(`🗑️ Starting delete process for ${type} with ID: ${id}`);

      if (type === 'category') {
        console.log('📂 Deleting category...');
        await deleteCategory(id);
        console.log('✅ Category deleted, reloading data...');
        loadCategories();
        loadSousCategories(); // update sous-categories if parent deleted
      } else if (type === 'sous-category') {
        console.log('📁 Deleting sous-category...');
        await deleteSousCategorie(id);
        console.log('✅ Sous-category deleted, reloading data...');
        loadSousCategories();
      }

      setShowDeleteModal(false);
      setItemToDelete(null);
    } catch (e) {
      console.error('❌ Delete operation failed:', e);
      if (itemToDelete.type === 'category') {
        setCatError(e.message);
      } else {
        setSousCatError(e.message);
      }
    }

    setDeleteLoading(false);
  };

  // Close delete modal
  const handleDeleteModalClose = () => {
    if (!deleteLoading) {
      setShowDeleteModal(false);
      setItemToDelete(null);
    }
  };

  // Sous-categorie handlers
  const handleSousCatChange = (e) => {
    const newForm = { ...sousCatForm, [e.target.name]: e.target.value };
    console.log(`📝 Sous-category form field changed: ${e.target.name} = ${e.target.value}`);
    console.log('📋 Updated sous-category form:', newForm);
    setSousCatForm(newForm);
  };
  const handleSousCatSubmit = async (e) => {
    e.preventDefault();
    setSousCatSubmitting(true);
    setSousCatError('');

    try {
      console.log(`📝 ${sousCatEditingId ? 'Updating' : 'Creating'} sous-category:`, sousCatForm);

      // Prepare data with image file
      const formDataWithImage = {
        ...sousCatForm,
        imageFile: sousCatImageFile
      };

      if (sousCatEditingId) {
        console.log(`🔄 Updating sous-category with ID: ${sousCatEditingId}`);
        await updateSousCategorie(sousCatEditingId, formDataWithImage);
        console.log('✅ Sous-category updated successfully');
      } else {
        console.log('➕ Creating new sous-category');
        await createSousCategorie(formDataWithImage);
        console.log('✅ Sous-category created successfully');
      }

      setSousCatForm(initialSousCategoryForm);
      setSousCatImageFile(null);
      setSousCatEditingId(null);
      setShowSousCatModal(false);
      loadSousCategories();
    } catch (e) {
      console.error('❌ Error submitting sous-category:', e);
      setSousCatError(e.message);
    }
    setSousCatSubmitting(false);
  };

  // Open subcategory modal for creating
  const handleSousCatCreate = () => {
    setSousCatModalAction('create');
    setSousCatEditingId(null);
    setSousCatForm(initialSousCategoryForm);
    setSousCatImageFile(null);
    setShowSousCatModal(true);
  };

  // Open subcategory modal for editing
  const handleSousCatEdit = (sc) => {
    console.log('✏️ Opening sous-category edit modal for:', sc);
    setSousCatModalAction('edit');
    const formData = {
      nom: sc.nom || sc.nom_sous_categorie,
      description: sc.description || sc.description_sous_categorie,
      categorie_id: sc.categorie_id
    };
    console.log('📝 Setting sous-category form data:', formData);
    setSousCatForm(formData);
    setSousCatEditingId(sc.id);
    setShowSousCatModal(true);
  };

  // Close subcategory modal
  const handleSousCatModalClose = () => {
    setShowSousCatModal(false);
    setSousCatEditingId(null);
    setSousCatForm(initialSousCategoryForm);
    setSousCatImageFile(null);
    setSousCatError('');
  };

  // Render cell functions for categories table
  const renderCategoriesCell = (column, row) => {
    switch (column.id) {
      case 'id':
        return (
          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
            {row.id}
          </Typography>
        );

      case 'nom':
        return (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box sx={{ width: 10, height: 10, borderRadius: '50%', bgcolor: COLORS.primary.main, mr: 1 }} />
            <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
              {row.nom || row.nom_categorie}
            </Typography>
          </Box>
        );

      case 'description':
        return (
          <Typography
            variant="body2"
            sx={{
              maxWidth: 300,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              color: row.description || row.description_categorie ? COLORS.text.primary : COLORS.text.secondary,
              fontStyle: row.description || row.description_categorie ? 'normal' : 'italic'
            }}
          >
            {row.description || row.description_categorie || 'Aucune description'}
          </Typography>
        );

      case 'image':
        return row.image || row.image_categorie ? (
          <img
            src={row.image || row.image_categorie}
            alt=""
            onError={(e) => {
              e.target.src = 'https://via.placeholder.com/40';
            }}
            style={{ width: 40, height: 40, borderRadius: 4, objectFit: 'cover' }}
          />
        ) : (
          <Typography variant="body2" sx={{ color: COLORS.text.secondary, fontStyle: 'italic' }}>
            Aucune image
          </Typography>
        );

      case 'actions':
        return (
          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
            <StandardButton variant="outline" size="small" onClick={() => handleCatEdit(row)} sx={{ minWidth: 'auto', padding: '4px 8px' }}>
              <FaPencilAlt />
            </StandardButton>
            <StandardButton
              variant="error"
              size="small"
              onClick={() => confirmDelete('category', row.id, row.nom || row.nom_categorie)}
              sx={{ minWidth: 'auto', padding: '4px 8px' }}
            >
              <FaTrashAlt />
            </StandardButton>
          </Box>
        );

      default:
        return row[column.id];
    }
  };

  // Render cell functions for sous-categories table
  const renderSousCategoriesCell = (column, row) => {
    switch (column.id) {
      case 'id':
        return (
          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
            {row.id}
          </Typography>
        );

      case 'nom':
        return (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box sx={{ width: 10, height: 10, borderRadius: '50%', bgcolor: COLORS.success.main, mr: 1 }} />
            <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
              {row.nom || row.nom_sous_categorie}
            </Typography>
          </Box>
        );

      case 'description':
        return (
          <Typography
            variant="body2"
            sx={{
              maxWidth: 250,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              color: row.description || row.description_sous_categorie ? COLORS.text.primary : COLORS.text.secondary,
              fontStyle: row.description || row.description_sous_categorie ? 'normal' : 'italic'
            }}
          >
            {row.description || row.description_sous_categorie || 'Aucune description'}
          </Typography>
        );

      case 'image':
        return row.primary_image_url ? (
          <img
            src={row.primary_image_url}
            alt=""
            onError={(e) => {
              e.target.src = 'https://via.placeholder.com/40';
            }}
            style={{ width: 40, height: 40, borderRadius: 4, objectFit: 'cover' }}
          />
        ) : (
          <Typography variant="body2" sx={{ color: COLORS.text.secondary, fontStyle: 'italic' }}>
            Aucune image
          </Typography>
        );

      case 'parent':
        const parentCat = categories.find((cat) => cat.id === parseInt(row.categorie_id));
        return parentCat ? (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box sx={{ width: 10, height: 10, borderRadius: '50%', bgcolor: COLORS.primary.main, mr: 1 }} />
            <Typography variant="body2">{parentCat.nom || parentCat.nom_categorie}</Typography>
          </Box>
        ) : (
          <Typography variant="body2" sx={{ color: COLORS.text.secondary }}>
            {row.categorie_id}
          </Typography>
        );

      case 'actions':
        return (
          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
            <StandardButton
              variant="outline"
              size="small"
              onClick={() => handleSousCatEdit(row)}
              sx={{ minWidth: 'auto', padding: '4px 8px' }}
            >
              <FaPencilAlt />
            </StandardButton>
            <StandardButton
              variant="error"
              size="small"
              onClick={() => confirmDelete('sous-category', row.id, row.nom || row.nom_sous_categorie)}
              sx={{ minWidth: 'auto', padding: '4px 8px' }}
            >
              <FaTrashAlt />
            </StandardButton>
          </Box>
        );

      default:
        return row[column.id];
    }
  };

  return (
    <MainCard>
      <Box sx={{ width: '100%' }}>
        {/* Breadcrumb - Design System Style */}
        <Box sx={{ mb: 2 }}>
          <Typography
            variant="body2"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              color: COLORS.text.secondary,
              fontSize: TYPOGRAPHY.fontSize.sm
            }}
          >
            Accueil &gt; Gestion des Catégories
          </Typography>
        </Box>

        {/* Header - Design System Style */}
        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h3"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontWeight: TYPOGRAPHY.fontWeight.bold,
              color: COLORS.text.dark,
              mb: 1
            }}
          >
            Gestion des Catégories
          </Typography>
          <Typography
            variant="body1"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              color: COLORS.text.secondary,
              fontSize: TYPOGRAPHY.fontSize.md
            }}
          >
            Organisez et gérez toutes vos catégories de produits
          </Typography>
        </Box>

        {/* Tabs - Design System Style */}
        <StandardCard sx={{ mb: 3 }}>
          <Box sx={{ p: 0 }}>
            <Tabs activeKey={tab} onSelect={setTab} className="mb-0 nav-tabs-custom" fill>
              <Tab
                eventKey="categories"
                title={
                  <span>
                    <Badge bg="primary" pill className="me-2">
                      {categories.length}
                    </Badge>
                    Catégories
                  </span>
                }
              />
              <Tab
                eventKey="sous-categories"
                title={
                  <span>
                    <Badge bg="success" pill className="me-2">
                      {sousCategories.length}
                    </Badge>
                    Sous-catégories
                  </span>
                }
              />
              <Tab eventKey="sous-sous-categories" title="Sous-sous-catégories" />
              <Tab
                eventKey="featured-categories"
                title={
                  <span>
                    <FaStar className="me-2 text-warning" />
                    Catégories Mises en Avant
                  </span>
                }
              />
            </Tabs>
          </Box>
        </StandardCard>

        {/* Custom CSS for tabs */}
        <style jsx="true">{`
          .nav-tabs-custom .nav-link {
            color: #495057;
            font-weight: 500;
            padding: 1rem 1.5rem;
            border-radius: 0;
            border: none;
            border-bottom: 3px solid transparent;
          }
          .nav-tabs-custom .nav-link.active {
            color: #2196f3;
            background: transparent;
            border-bottom: 3px solid #2196f3;
          }
          .nav-tabs-custom .nav-link:hover:not(.active) {
            border-bottom: 3px solid #e9ecef;
          }
        `}</style>
        {tab === 'categories' && (
          <>
            {catError && (
              <Box sx={{ mb: 3 }}>
                <Alert severity="error">{catError}</Alert>
              </Box>
            )}

            {/* Categories Header */}
            <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Box>
                <Typography
                  variant="h5"
                  sx={{
                    fontFamily: TYPOGRAPHY.fontFamily.primary,
                    fontSize: TYPOGRAPHY.fontSize.lg,
                    fontWeight: TYPOGRAPHY.fontWeight.semibold,
                    color: COLORS.text.dark
                  }}
                >
                  Liste des catégories ({categories.length})
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    fontFamily: TYPOGRAPHY.fontFamily.primary,
                    color: COLORS.text.secondary,
                    mt: 0.5
                  }}
                >
                  {categories.length} catégorie{categories.length !== 1 ? 's' : ''} au total
                </Typography>
              </Box>
              <StandardButton variant="primary" onClick={handleCatCreate} startIcon={<FaPlus />} size="medium">
                Ajouter une catégorie
              </StandardButton>
            </Box>

            {/* Categories Table */}
            <StandardTable
              columns={categoriesColumns}
              data={categories}
              loading={catLoading}
              error={catError}
              emptyMessage="Aucune catégorie trouvée. Créez votre première catégorie pour commencer."
              renderCell={renderCategoriesCell}
              hover={true}
            />

            {/* Custom CSS for table styling */}
            <style jsx="true">{`
              .color-dot {
                width: 10px;
                height: 10px;
                border-radius: 50%;
              }
              .image-thumbnail img {
                object-fit: cover;
              }
            `}</style>
          </>
        )}
        {tab === 'sous-categories' && (
          <>
            {sousCatError && (
              <Box sx={{ mb: 3 }}>
                <Alert severity="error">{sousCatError}</Alert>
              </Box>
            )}

            {/* Sous-Categories Header */}
            <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Box>
                <Typography
                  variant="h5"
                  sx={{
                    fontFamily: TYPOGRAPHY.fontFamily.primary,
                    fontSize: TYPOGRAPHY.fontSize.lg,
                    fontWeight: TYPOGRAPHY.fontWeight.semibold,
                    color: COLORS.text.dark
                  }}
                >
                  Liste des sous-catégories ({sousCategories.length})
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    fontFamily: TYPOGRAPHY.fontFamily.primary,
                    color: COLORS.text.secondary,
                    mt: 0.5
                  }}
                >
                  {sousCategories.length} sous-catégorie{sousCategories.length !== 1 ? 's' : ''} au total
                </Typography>
              </Box>
              <StandardButton variant="success" onClick={handleSousCatCreate} startIcon={<FaPlus />} size="medium">
                Ajouter une sous-catégorie
              </StandardButton>
            </Box>

            {/* Sous-Categories Table */}
            <StandardTable
              columns={sousCategoriesColumns}
              data={sousCategories}
              loading={sousCatLoading}
              error={sousCatError}
              emptyMessage="Aucune sous-catégorie trouvée. Créez votre première sous-catégorie pour commencer."
              renderCell={renderSousCategoriesCell}
              hover={true}
            />
          </>
        )}
        {tab === 'sous-sous-categories' && <SousSousCategories ImageManager={ImageManager} />}
        {tab === 'featured-categories' && <FeaturedCategories />}

        {/* Professional Category Modal */}
        <FormModal
          show={showCatModal}
          onHide={handleCatModalClose}
          onSubmit={handleCatSubmit}
          title={catModalAction === 'edit' ? 'Modifier une catégorie' : 'Ajouter une nouvelle catégorie'}
          isEdit={catModalAction === 'edit'}
          loading={catSubmitting}
          size="lg"
          submitText={catModalAction === 'edit' ? 'Modifier' : 'Créer'}
          cancelText="Annuler"
          icon={catModalAction === 'edit' ? <FaPencilAlt /> : <FaPlus />}
        >
          <Form id="catForm" onSubmit={handleCatSubmit}>
            <Row className="g-3">
              <Col xs={12} md={6}>
                <Form.Group controlId="categoryNom">
                  <Form.Label className="fw-medium">Nom de la catégorie</Form.Label>
                  <Form.Control
                    name="nom"
                    value={catForm.nom || ''}
                    onChange={handleCatChange}
                    placeholder="Nom de la catégorie"
                    required
                    disabled={catSubmitting}
                    className="rounded-3 border-2"
                  />
                  <Form.Text className="text-muted">Le nom sera affiché aux clients sur le site.</Form.Text>
                </Form.Group>
              </Col>
              <Col xs={12} md={6}>
                <Form.Group controlId="categoryImage">
                  <Form.Label className="fw-medium">Image (URL)</Form.Label>
                  <Form.Control
                    name="image"
                    value={catForm.image || ''}
                    onChange={handleCatChange}
                    placeholder="ex: https://..."
                    disabled={catSubmitting}
                    className="rounded-3 border-2"
                  />
                  <Form.Text className="text-muted">URL de l'image représentant la catégorie.</Form.Text>
                </Form.Group>
              </Col>
            </Row>
            <Row className="mt-3">
              <Col xs={12}>
                <Form.Group controlId="categoryDescription">
                  <Form.Label className="fw-medium">Description</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    name="description"
                    value={catForm.description || ''}
                    onChange={handleCatChange}
                    placeholder="Description détaillée de la catégorie"
                    disabled={catSubmitting}
                    className="rounded-3 border-2"
                  />
                </Form.Group>
              </Col>
            </Row>
          </Form>
        </FormModal>

        {/* Professional Subcategory Modal */}
        <FormModal
          show={showSousCatModal}
          onHide={handleSousCatModalClose}
          onSubmit={handleSousCatSubmit}
          title={sousCatModalAction === 'edit' ? 'Modifier une sous-catégorie' : 'Ajouter une nouvelle sous-catégorie'}
          isEdit={sousCatModalAction === 'edit'}
          loading={sousCatSubmitting}
          size="lg"
          submitText={sousCatModalAction === 'edit' ? 'Mettre à jour' : 'Ajouter'}
          cancelText="Annuler"
          icon={sousCatModalAction === 'edit' ? <FaPencilAlt /> : <FaPlus />}
        >
          <Form id="sousCatForm" onSubmit={handleSousCatSubmit}>
            <Row className="g-3">
              <Col xs={12} md={6}>
                <Form.Group controlId="sousCategorieNom">
                  <Form.Label className="fw-medium">Nom de la sous-catégorie</Form.Label>
                  <Form.Control
                    name="nom"
                    value={sousCatForm.nom || ''}
                    onChange={handleSousCatChange}
                    placeholder="Nom de la sous-catégorie"
                    required
                    disabled={sousCatSubmitting}
                    className="rounded-3 border-2"
                  />
                  <Form.Text className="text-muted">Le nom sera affiché aux clients sur le site.</Form.Text>
                </Form.Group>
              </Col>
              <Col xs={12} md={6}>
                <Form.Group controlId="sousCategorieImage">
                  <Form.Label className="fw-medium">Image</Form.Label>
                  <Form.Control
                    type="file"
                    accept="image/*"
                    onChange={(e) => setSousCatImageFile(e.target.files[0])}
                    disabled={sousCatSubmitting}
                    className="rounded-3 border-2"
                  />
                  <Form.Text className="text-muted">
                    Sélectionnez une image pour la sous-catégorie (max 10MB).
                    {sousCatImageFile && <span className="text-success ms-2">✓ {sousCatImageFile.name}</span>}
                  </Form.Text>
                </Form.Group>
              </Col>
            </Row>
            <Row className="mt-3">
              <Col xs={12} md={6}>
                <Form.Group controlId="categorieId">
                  <Form.Label className="fw-medium">Catégorie parente</Form.Label>
                  <Form.Select
                    name="categorie_id"
                    value={sousCatForm.categorie_id || ''}
                    onChange={handleSousCatChange}
                    required
                    disabled={sousCatSubmitting || categories.length === 0}
                    className="rounded-3 border-2"
                  >
                    <option value="">Sélectionnez une catégorie</option>
                    {categories.map((cat) => (
                      <option key={cat.id} value={cat.id}>
                        {cat.nom || cat.nom_categorie}
                      </option>
                    ))}
                  </Form.Select>
                  <Form.Text className="text-muted">Sélectionnez la catégorie à laquelle cette sous-catégorie appartient.</Form.Text>
                </Form.Group>
              </Col>
              <Col xs={12} md={6}>
                {/* Empty column for layout balance */}
              </Col>
            </Row>
            <Row className="mt-3">
              <Col xs={12}>
                <Form.Group controlId="sousCategorieDescription">
                  <Form.Label className="fw-medium">Description</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    name="description"
                    value={sousCatForm.description || ''}
                    onChange={handleSousCatChange}
                    placeholder="Description détaillée de la sous-catégorie"
                    disabled={sousCatSubmitting}
                    className="rounded-3 border-2"
                  />
                </Form.Group>
              </Col>
            </Row>
          </Form>
        </FormModal>

        {/* Professional Delete Confirmation Modal */}
        <ConfirmationModal
          show={showDeleteModal}
          onHide={handleDeleteModalClose}
          onConfirm={handleDelete}
          title="Confirmer la suppression"
          message={
            itemToDelete && (
              <>
                Êtes-vous sûr de vouloir supprimer {itemToDelete.type === 'category' ? 'la catégorie' : 'la sous-catégorie'}{' '}
                <strong>"{itemToDelete.name}"</strong> ?<br />
                <small className="text-muted">
                  {itemToDelete.type === 'category'
                    ? 'Cette action supprimera également toutes les sous-catégories et produits associés.'
                    : 'Cette action supprimera également tous les produits associés à cette sous-catégorie.'}
                </small>
              </>
            )
          }
          confirmText="Supprimer définitivement"
          cancelText="Annuler"
          variant="danger"
          loading={deleteLoading}
          icon={<FaTrashAlt />}
        />
      </Box>
    </MainCard>
  );
}
