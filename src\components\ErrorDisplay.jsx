import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  <PERSON>ert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  CircularProgress
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Refresh as RefreshIcon,
  BugReport as BugReportIcon,
  Warning as WarningIcon,
  Error as <PERSON>rrorIcon,
  Wifi as WifiIcon
} from '@mui/icons-material';

const ErrorDisplay = ({ 
  error, 
  onRetry, 
  isRetrying = false, 
  retryCount = 0,
  showDetails = true,
  compact = false 
}) => {
  const [showTechnicalDetails, setShowTechnicalDetails] = useState(false);

  if (!error) return null;

  const getErrorIcon = () => {
    switch (error.type) {
      case 'NETWORK_ERROR':
        return <WifiIcon sx={{ color: 'warning.main' }} />;
      case 'SERVER_ERROR':
        return <ErrorIcon sx={{ color: 'error.main' }} />;
      default:
        return <WarningIcon sx={{ color: 'warning.main' }} />;
    }
  };

  const getErrorSeverity = () => {
    switch (error.type) {
      case 'SERVER_ERROR':
        return 'error';
      case 'NETWORK_ERROR':
        return 'warning';
      case 'UNAUTHORIZED':
      case 'FORBIDDEN':
        return 'info';
      default:
        return 'warning';
    }
  };

  const getErrorTitle = () => {
    switch (error.type) {
      case 'SERVER_ERROR':
        return 'Erreur du serveur';
      case 'NETWORK_ERROR':
        return 'Problème de connexion';
      case 'NOT_FOUND':
        return 'Ressource introuvable';
      case 'UNAUTHORIZED':
        return 'Authentification requise';
      case 'FORBIDDEN':
        return 'Accès refusé';
      default:
        return 'Erreur';
    }
  };

  if (compact) {
    return (
      <Alert 
        severity={getErrorSeverity()} 
        sx={{ mb: 2 }}
        action={
          error.canRetry && onRetry && (
            <Button
              size="small"
              onClick={onRetry}
              disabled={isRetrying}
              startIcon={isRetrying ? <CircularProgress size={16} /> : <RefreshIcon />}
            >
              {isRetrying ? 'Retry...' : 'Retry'}
            </Button>
          )
        }
      >
        {error.message}
      </Alert>
    );
  }

  return (
    <Box sx={{ mb: 2 }}>
      <Alert severity={getErrorSeverity()}>
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
          {getErrorIcon()}
          <Box sx={{ flex: 1 }}>
            <Typography variant="h6" gutterBottom>
              {getErrorTitle()}
            </Typography>
            <Typography variant="body2" sx={{ mb: 2 }}>
              {error.message}
            </Typography>

            {/* Actions */}
            <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
              {error.canRetry && onRetry && (
                <Button
                  variant="contained"
                  size="small"
                  onClick={onRetry}
                  disabled={isRetrying}
                  startIcon={isRetrying ? <CircularProgress size={16} /> : <RefreshIcon />}
                >
                  {isRetrying ? 'Nouvelle tentative...' : 'Réessayer'}
                </Button>
              )}
              
              {error.type === 'UNAUTHORIZED' && (
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => window.location.href = '/login'}
                >
                  Se reconnecter
                </Button>
              )}

              {error.type === 'NETWORK_ERROR' && (
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => window.location.reload()}
                >
                  Recharger la page
                </Button>
              )}
            </Box>

            {/* Retry count */}
            {retryCount > 0 && (
              <Chip 
                label={`Tentatives: ${retryCount}`} 
                size="small" 
                color="warning"
                sx={{ mb: 1 }}
              />
            )}

            {/* Technical details */}
            {showDetails && error.originalError && (
              <Accordion>
                <AccordionSummary 
                  expandIcon={<ExpandMoreIcon />}
                  onClick={() => setShowTechnicalDetails(!showTechnicalDetails)}
                >
                  <Typography variant="caption">
                    Détails techniques
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Box>
                    <Typography variant="caption" color="text.secondary" gutterBottom>
                      Timestamp: {new Date(error.timestamp).toLocaleString()}
                    </Typography>
                    
                    {error.originalError.response && (
                      <Box sx={{ mt: 1 }}>
                        <Typography variant="caption" display="block">
                          Status: {error.originalError.response.status}
                        </Typography>
                        <Typography variant="caption" display="block">
                          URL: {error.originalError.config?.url}
                        </Typography>
                      </Box>
                    )}

                    {error.context && Object.keys(error.context).length > 0 && (
                      <Box sx={{ mt: 1 }}>
                        <Typography variant="caption" display="block" gutterBottom>
                          Contexte:
                        </Typography>
                        <Box component="pre" sx={{ 
                          fontSize: '0.7rem', 
                          backgroundColor: 'grey.100', 
                          p: 1, 
                          borderRadius: 1,
                          overflow: 'auto',
                          maxHeight: 150
                        }}>
                          {JSON.stringify(error.context, null, 2)}
                        </Box>
                      </Box>
                    )}
                  </Box>
                </AccordionDetails>
              </Accordion>
            )}
          </Box>
        </Box>
      </Alert>
    </Box>
  );
};

export default ErrorDisplay;
