/* Professional Modal Styles - System Design */

/* CSS Custom Properties for dynamic sidebar width */
:root {
  --sidebar-width: 260px; /* Default: sidebar open */
  --sidebar-width-mini: 72px; /* Sidebar closed/mini */
  --navbar-height: 88px;
  --modal-margin: 1rem;
}

/* Dynamic classes for sidebar state */
.sidebar-open {
  --current-sidebar-width: var(--sidebar-width);
}

.sidebar-closed {
  --current-sidebar-width: var(--sidebar-width-mini);
}

/* Animation for modal appearance */
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive adjustments for Material-UI Dialog */
@media (max-width: 991.98px) {
  /* Reset modal positioning for mobile/tablet - sidebar is typically hidden */
  .MuiDialog-paper {
    margin: var(--modal-margin) !important;
    margin-top: calc(var(--navbar-height) + var(--modal-margin)) !important;
    max-width: 90vw !important;
    width: 90vw !important;
  }
}

@media (max-width: 575.98px) {
  .MuiDialog-paper {
    margin: 0.5rem !important;
    margin-top: calc(var(--navbar-height) + 0.5rem) !important;
    max-width: calc(100vw - 1rem) !important;
    width: calc(100vw - 1rem) !important;
  }
}

/* Focus states for accessibility */
.MuiIconButton-root:focus {
  outline: 2px solid #2196f3;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .MuiDialog-root {
    display: none !important;
  }
}

/* Additional responsive adjustments for Material-UI Dialog on mobile */
@media (max-width: 575.98px) {
  .MuiDialogTitle-root {
    padding: 0.5rem 0.75rem !important;
  }

  .MuiDialogContent-root {
    padding: 0.75rem !important;
  }

  .MuiDialogActions-root {
    flex-direction: column !important;
    padding: 0.5rem 0.75rem !important;
    gap: 0.5rem !important;
  }

  .MuiDialogActions-root button {
    width: 100% !important;
    margin-bottom: 0 !important;
  }
}
