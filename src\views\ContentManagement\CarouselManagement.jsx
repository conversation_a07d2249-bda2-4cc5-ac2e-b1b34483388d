import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Form, Table, Alert, Spinner, Tabs, Tab, Badge, InputGroup } from 'react-bootstrap';
import { Box, Typography } from '@mui/material';
import {
  FaPlus,
  FaPencilAlt,
  FaTrashAlt,
  FaImage,
  FaArrowUp,
  FaArrowDown,
  FaArrowLeft,
  FaLink,
  FaEye,
  FaEyeSlash,
  FaDesktop,
  FaMobile,
  FaExclamationTriangle
} from 'react-icons/fa';

// Design system
import { COLORS, TYPOGRAPHY } from '../../themes/designSystem';
import MainCard from '../../ui-component/cards/MainCard';
import StandardButton from '../../components/StandardButton';
import StandardTable from '../../components/StandardTable';
import StandardCard from '../../components/StandardCard';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import {
  fetchCarousels,
  createCarousel,
  updateCarousel,
  deleteCarousel,
  fetchSlides,
  createSlide,
  updateSlide,
  deleteSlide,
  reorderSlides
} from '../../services/contentService';
import ImageManager from '../GestionCommerciale/ImageManager';
import ProfessionalModal from '../../ui-component/extended/ProfessionalModal';

const CarouselManagement = () => {
  // Columns for carousels table
  const carouselsColumns = [
    { id: 'id', label: 'ID', minWidth: 60 },
    { id: 'name', label: 'Nom', minWidth: 200 },
    { id: 'description', label: 'Description', minWidth: 250 },
    { id: 'status', label: 'Statut', minWidth: 100 },
    { id: 'slides', label: 'Slides', minWidth: 100 },
    { id: 'info', label: 'Informations', minWidth: 200 },
    { id: 'actions', label: 'Actions', minWidth: 250, align: 'center' }
  ];

  // Columns for slides table
  const slidesColumns = [
    { id: 'id', label: 'ID', minWidth: 60 },
    { id: 'title', label: 'Titre', minWidth: 200 },
    { id: 'content', label: 'Contenu', minWidth: 250 },
    { id: 'button', label: 'Bouton', minWidth: 150 },
    { id: 'status', label: 'Statut', minWidth: 100 },
    { id: 'order', label: 'Ordre', minWidth: 80 },
    { id: 'actions', label: 'Actions', minWidth: 200, align: 'center' }
  ];

  // State for carousels
  const [carousels, setCarousels] = useState([]);
  const [carouselForm, setCarouselForm] = useState({
    name: '',
    description: '',
    is_active: true,
    ordre: 0
  });
  const [editingCarouselId, setEditingCarouselId] = useState(null);
  const [carouselLoading, setCarouselLoading] = useState(false);
  const [carouselSubmitting, setCarouselSubmitting] = useState(false);

  // State for slides
  const [selectedCarouselId, setSelectedCarouselId] = useState(null);
  const [slides, setSlides] = useState([]);
  const [slideForm, setSlideForm] = useState({
    title: '',
    content: '',
    button_text: '',
    button_link: '',
    is_active: true,
    ordre: 0
  });
  const [editingSlideId, setEditingSlideId] = useState(null);
  const [slideLoading, setSlideLoading] = useState(false);
  const [slideSubmitting, setSlideSubmitting] = useState(false);

  // UI state
  const [activeTab, setActiveTab] = useState('carousels');
  const [showCarouselModal, setShowCarouselModal] = useState(false);
  const [showSlideModal, setShowSlideModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [itemToDelete, setItemToDelete] = useState({ type: '', id: null });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [selectedSlideId, setSelectedSlideId] = useState(null);
  const [imageType, setImageType] = useState('desktop');

  // Load carousels
  const loadCarousels = async () => {
    setCarouselLoading(true);
    setError('');
    try {
      const data = await fetchCarousels();
      setCarousels(data);
    } catch (e) {
      setError(`Error loading carousels: ${e.message}`);
    }
    setCarouselLoading(false);
  };

  // Load slides for a carousel
  const loadSlides = async (carouselId) => {
    if (!carouselId) return;

    setSlideLoading(true);
    setError('');
    try {
      const data = await fetchSlides(carouselId);
      setSlides(data);
      setSelectedCarouselId(carouselId);
      setActiveTab('slides');
    } catch (e) {
      setError(`Error loading slides: ${e.message}`);
    }
    setSlideLoading(false);
  };

  // Initial data load
  useEffect(() => {
    loadCarousels();
  }, []);

  // Handle carousel form changes
  const handleCarouselChange = (e) => {
    const { name, value, type, checked } = e.target;
    setCarouselForm({
      ...carouselForm,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle slide form changes
  const handleSlideChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSlideForm({
      ...slideForm,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Submit carousel form
  const handleCarouselSubmit = async () => {
    if (!carouselForm.name) {
      setError('Carousel name is required');
      return;
    }

    setCarouselSubmitting(true);
    setError('');

    try {
      if (editingCarouselId) {
        await updateCarousel(editingCarouselId, carouselForm);
        setSuccess('Carousel updated successfully');
      } else {
        await createCarousel(carouselForm);
        setSuccess('Carousel created successfully');
      }

      setShowCarouselModal(false);
      resetCarouselForm();
      loadCarousels();
    } catch (e) {
      setError(`Error saving carousel: ${e.message}`);
    }

    setCarouselSubmitting(false);
    setTimeout(() => setSuccess(''), 3000);
  };

  // Submit slide form
  const handleSlideSubmit = async () => {
    if (!slideForm.title) {
      setError('Slide title is required');
      return;
    }

    setSlideSubmitting(true);
    setError('');

    try {
      if (editingSlideId) {
        await updateSlide(editingSlideId, slideForm);
        setSuccess('Slide updated successfully');
      } else {
        await createSlide(selectedCarouselId, slideForm);
        setSuccess('Slide created successfully');
      }

      setShowSlideModal(false);
      resetSlideForm();
      loadSlides(selectedCarouselId);
    } catch (e) {
      setError(`Error saving slide: ${e.message}`);
    }

    setSlideSubmitting(false);
    setTimeout(() => setSuccess(''), 3000);
  };

  // Reset carousel form
  const resetCarouselForm = () => {
    setCarouselForm({
      name: '',
      description: '',
      is_active: true,
      ordre: 0
    });
    setEditingCarouselId(null);
  };

  // Reset slide form
  const resetSlideForm = () => {
    setSlideForm({
      title: '',
      content: '',
      button_text: '',
      button_link: '',
      is_active: true,
      ordre: 0
    });
    setEditingSlideId(null);
  };

  // Edit carousel
  const handleEditCarousel = (carousel) => {
    setCarouselForm({
      name: carousel.name,
      description: carousel.description || '',
      is_active: carousel.is_active,
      ordre: carousel.ordre || 0
    });
    setEditingCarouselId(carousel.id);
    setShowCarouselModal(true);
  };

  // Edit slide
  const handleEditSlide = (slide) => {
    setSlideForm({
      title: slide.title,
      content: slide.content || '',
      button_text: slide.button_text || '',
      button_link: slide.button_link || '',
      is_active: slide.is_active,
      ordre: slide.ordre || 0
    });
    setEditingSlideId(slide.id);
    setShowSlideModal(true);
  };

  // Confirm delete
  const confirmDelete = (type, id) => {
    setItemToDelete({ type, id });
    setShowDeleteModal(true);
  };

  // Handle delete
  const handleDelete = async () => {
    setError('');

    try {
      const { type, id } = itemToDelete;

      if (type === 'carousel') {
        await deleteCarousel(id);
        loadCarousels();
        setSuccess('Carousel deleted successfully');
      } else if (type === 'slide') {
        await deleteSlide(id);
        loadSlides(selectedCarouselId);
        setSuccess('Slide deleted successfully');
      }
    } catch (e) {
      setError(`Error deleting item: ${e.message}`);
    }

    setShowDeleteModal(false);
    setTimeout(() => setSuccess(''), 3000);
  };

  // Handle slide reordering
  const handleDragEnd = async (result) => {
    if (!result.destination) return;

    const items = Array.from(slides);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update local state immediately for better UX
    setSlides(items);

    try {
      await reorderSlides(selectedCarouselId, items);
      setSuccess('Slides reordered successfully');
    } catch (e) {
      setError(`Error reordering slides: ${e.message}`);
      // Revert to previous state on error
      loadSlides(selectedCarouselId);
    }

    setTimeout(() => setSuccess(''), 3000);
  };

  // Open image manager for a slide
  const openImageManager = (slideId) => {
    setSelectedSlideId(slideId);
    setActiveTab('images');
  };

  // Render cell functions for carousels table
  const renderCarouselsCell = (column, row) => {
    switch (column.id) {
      case 'id':
        return (
          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
            {row.id}
          </Typography>
        );

      case 'name':
        return (
          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
            {row.name}
          </Typography>
        );

      case 'description':
        return (
          <Typography
            variant="body2"
            sx={{
              maxWidth: 250,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              color: row.description ? COLORS.text.primary : COLORS.text.secondary,
              fontStyle: row.description ? 'normal' : 'italic'
            }}
          >
            {row.description || 'Aucune description'}
          </Typography>
        );

      case 'status':
        return (
          <Box
            sx={{
              display: 'inline-flex',
              alignItems: 'center',
              px: 1,
              py: 0.5,
              borderRadius: 1,
              bgcolor: row.is_active ? COLORS.success.light : COLORS.text.secondary + '20',
              color: row.is_active ? COLORS.success.main : COLORS.text.secondary
            }}
          >
            <Typography variant="caption" sx={{ fontWeight: 'medium' }}>
              {row.is_active ? 'Active' : 'Inactive'}
            </Typography>
          </Box>
        );

      case 'slides':
        return (
          <Box
            sx={{
              display: 'inline-flex',
              alignItems: 'center',
              px: 1,
              py: 0.5,
              borderRadius: 1,
              bgcolor: COLORS.info.light,
              color: COLORS.info.main
            }}
          >
            <Typography variant="caption" sx={{ fontWeight: 'medium' }}>
              {row.slides_count || 0} slides
            </Typography>
          </Box>
        );

      case 'info':
        return (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
            <Box
              sx={{
                display: 'inline-flex',
                alignItems: 'center',
                px: 1,
                py: 0.25,
                borderRadius: 0.5,
                bgcolor: COLORS.text.secondary + '10',
                color: COLORS.text.secondary,
                width: 'fit-content'
              }}
            >
              <Typography variant="caption" sx={{ fontSize: '0.7rem' }}>
                Ordre: {row.ordre}
              </Typography>
            </Box>
            <Box
              sx={{
                display: 'inline-flex',
                alignItems: 'center',
                px: 1,
                py: 0.25,
                borderRadius: 0.5,
                bgcolor: COLORS.text.secondary + '10',
                color: COLORS.text.secondary,
                width: 'fit-content'
              }}
            >
              <Typography variant="caption" sx={{ fontSize: '0.7rem' }}>
                Créé: {new Date(row.created_at).toLocaleDateString()}
              </Typography>
            </Box>
          </Box>
        );

      case 'actions':
        return (
          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
            <StandardButton
              variant="primary"
              size="small"
              onClick={() => loadSlides(row.id)}
              title="Gérer les slides"
              sx={{ minWidth: 'auto', padding: '4px 8px' }}
            >
              <FaDesktop />
            </StandardButton>
            <StandardButton
              variant="outline"
              size="small"
              onClick={() => handleEditCarousel(row)}
              title="Modifier"
              sx={{ minWidth: 'auto', padding: '4px 8px' }}
            >
              <FaPencilAlt />
            </StandardButton>
            <StandardButton
              variant="error"
              size="small"
              onClick={() => confirmDelete('carousel', row.id)}
              title="Supprimer"
              sx={{ minWidth: 'auto', padding: '4px 8px' }}
            >
              <FaTrashAlt />
            </StandardButton>
          </Box>
        );

      default:
        return row[column.id];
    }
  };

  // Render cell functions for slides table
  const renderSlidesCell = (column, row, index) => {
    switch (column.id) {
      case 'id':
        return (
          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
            {row.id}
          </Typography>
        );

      case 'title':
        return (
          <Box>
            <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
              {row.title}
            </Typography>
            {row.subtitle && (
              <Typography variant="caption" sx={{ color: COLORS.text.secondary }}>
                {row.subtitle}
              </Typography>
            )}
          </Box>
        );

      case 'content':
        return (
          <Typography
            variant="body2"
            sx={{
              maxWidth: 250,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              color: row.content ? COLORS.text.primary : COLORS.text.secondary,
              fontStyle: row.content ? 'normal' : 'italic'
            }}
          >
            {row.content || 'Aucun contenu'}
          </Typography>
        );

      case 'button':
        return row.button_text ? (
          <Box>
            <Box
              sx={{
                display: 'inline-flex',
                alignItems: 'center',
                px: 1,
                py: 0.5,
                borderRadius: 1,
                bgcolor: COLORS.info.light,
                color: COLORS.info.main,
                mb: 0.5
              }}
            >
              <Typography variant="caption" sx={{ fontWeight: 'medium' }}>
                {row.button_text}
              </Typography>
            </Box>
            {row.button_link && (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <FaLink style={{ marginRight: 4, fontSize: '0.7rem', color: COLORS.text.secondary }} />
                <Typography
                  variant="caption"
                  sx={{
                    color: COLORS.text.secondary,
                    maxWidth: 150,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}
                >
                  {row.button_link}
                </Typography>
              </Box>
            )}
          </Box>
        ) : (
          <Typography variant="body2" sx={{ color: COLORS.text.secondary, fontStyle: 'italic' }}>
            Aucun bouton
          </Typography>
        );

      case 'status':
        return (
          <Box
            sx={{
              display: 'inline-flex',
              alignItems: 'center',
              px: 1,
              py: 0.5,
              borderRadius: 1,
              bgcolor: row.is_active ? COLORS.success.light : COLORS.text.secondary + '20',
              color: row.is_active ? COLORS.success.main : COLORS.text.secondary
            }}
          >
            {row.is_active ? (
              <FaEye style={{ marginRight: 4, fontSize: '0.8rem' }} />
            ) : (
              <FaEyeSlash style={{ marginRight: 4, fontSize: '0.8rem' }} />
            )}
            <Typography variant="caption" sx={{ fontWeight: 'medium' }}>
              {row.is_active ? 'Active' : 'Inactive'}
            </Typography>
          </Box>
        );

      case 'order':
        return (
          <Box
            sx={{
              display: 'inline-flex',
              alignItems: 'center',
              px: 1,
              py: 0.5,
              borderRadius: '50%',
              bgcolor: COLORS.primary.light,
              color: COLORS.primary.main,
              minWidth: 32,
              height: 32,
              justifyContent: 'center'
            }}
          >
            <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
              {index + 1}
            </Typography>
          </Box>
        );

      case 'actions':
        return (
          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
            <StandardButton
              variant="info"
              size="small"
              onClick={() => openImageManager(row.id)}
              title="Gérer les images"
              sx={{ minWidth: 'auto', padding: '4px 8px' }}
            >
              <FaImage />
            </StandardButton>
            <StandardButton
              variant="outline"
              size="small"
              onClick={() => handleEditSlide(row)}
              title="Modifier"
              sx={{ minWidth: 'auto', padding: '4px 8px' }}
            >
              <FaPencilAlt />
            </StandardButton>
            <StandardButton
              variant="error"
              size="small"
              onClick={() => confirmDelete('slide', row.id)}
              title="Supprimer"
              sx={{ minWidth: 'auto', padding: '4px 8px' }}
            >
              <FaTrashAlt />
            </StandardButton>
          </Box>
        );

      default:
        return row[column.id];
    }
  };

  return (
    <MainCard>
      <Box sx={{ width: '100%' }}>
        {/* Breadcrumb - Design System Style */}
        <Box sx={{ mb: 2 }}>
          <Typography
            variant="body2"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              color: COLORS.text.secondary,
              fontSize: TYPOGRAPHY.fontSize.sm
            }}
          >
            Accueil &gt; Gestion des Carrousels
          </Typography>
        </Box>

        {/* Header - Design System Style */}
        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h3"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontWeight: TYPOGRAPHY.fontWeight.bold,
              color: COLORS.text.dark,
              mb: 1
            }}
          >
            Gestion des Carrousels
          </Typography>
          <Typography
            variant="body1"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              color: COLORS.text.secondary,
              fontSize: TYPOGRAPHY.fontSize.md
            }}
          >
            Gérez les carrousels et diapositives de votre site web
          </Typography>
        </Box>

        {/* Error and Success Messages */}
        {error && (
          <Box sx={{ mb: 3 }}>
            <Alert severity="error">{error}</Alert>
          </Box>
        )}
        {success && (
          <Box sx={{ mb: 3 }}>
            <Alert severity="success">{success}</Alert>
          </Box>
        )}

        {/* Tabs - Design System Style */}
        <StandardCard sx={{ mb: 3 }}>
          <Box sx={{ p: 0 }}>
            <Tabs activeKey={activeTab} onSelect={setActiveTab} className="mb-0 nav-tabs-custom" fill>
              <Tab
                eventKey="carousels"
                title={
                  <span>
                    <FaImage className="me-2" />
                    Carrousels
                  </span>
                }
              />
              <Tab
                eventKey="slides"
                title={
                  <span>
                    <FaDesktop className="me-2" />
                    Diapositives
                  </span>
                }
                disabled={!selectedCarouselId}
              />
              <Tab
                eventKey="images"
                title={
                  <span>
                    <FaImage className="me-2" />
                    Images
                  </span>
                }
                disabled={!selectedSlideId}
              />
            </Tabs>
          </Box>
        </StandardCard>

        {/* Tab Content */}
        {activeTab === 'carousels' && (
          <>
            {/* Carousels Header */}
            <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Box>
                <Typography
                  variant="h5"
                  sx={{
                    fontFamily: TYPOGRAPHY.fontFamily.primary,
                    fontSize: TYPOGRAPHY.fontSize.lg,
                    fontWeight: TYPOGRAPHY.fontWeight.semibold,
                    color: COLORS.text.dark
                  }}
                >
                  Liste des carrousels ({carousels.length})
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    fontFamily: TYPOGRAPHY.fontFamily.primary,
                    color: COLORS.text.secondary,
                    mt: 0.5
                  }}
                >
                  Gérez vos carrousels et leurs diapositives
                </Typography>
              </Box>
              <StandardButton
                variant="primary"
                onClick={() => {
                  resetCarouselForm();
                  setShowCarouselModal(true);
                }}
                startIcon={<FaPlus />}
                size="medium"
              >
                Ajouter un Carrousel
              </StandardButton>
            </Box>

            {/* Carousels Table */}
            <StandardTable
              columns={carouselsColumns}
              data={carousels}
              loading={carouselLoading}
              error={error}
              emptyMessage="Aucun carrousel trouvé. Créez votre premier carrousel pour commencer."
              renderCell={renderCarouselsCell}
              hover={true}
            />
          </>
        )}

        {activeTab === 'images' && (
          <>
            {/* Images Header */}
            <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <Box>
                <Typography
                  variant="h5"
                  sx={{
                    fontFamily: TYPOGRAPHY.fontFamily.primary,
                    fontSize: TYPOGRAPHY.fontSize.lg,
                    fontWeight: TYPOGRAPHY.fontWeight.semibold,
                    color: COLORS.text.dark,
                    mb: 1
                  }}
                >
                  <FaImage style={{ marginRight: 8 }} />
                  Gestion des Images
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    fontFamily: TYPOGRAPHY.fontFamily.primary,
                    color: COLORS.text.secondary,
                    mb: 2
                  }}
                >
                  Gérez les images pour la diapositive: <strong>{slides.find((s) => s.id === selectedSlideId)?.title}</strong>
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Box
                    sx={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      px: 1,
                      py: 0.5,
                      borderRadius: 1,
                      bgcolor: COLORS.info.light,
                      color: COLORS.info.main
                    }}
                  >
                    <FaDesktop style={{ marginRight: 4, fontSize: '0.8rem' }} />
                    <Typography variant="caption" sx={{ fontWeight: 'medium' }}>
                      Images Desktop & Mobile
                    </Typography>
                  </Box>
                  <Box
                    sx={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      px: 1,
                      py: 0.5,
                      borderRadius: 1,
                      bgcolor: COLORS.text.secondary + '20',
                      color: COLORS.text.secondary
                    }}
                  >
                    <Typography variant="caption" sx={{ fontWeight: 'medium' }}>
                      Formats: JPG, PNG, GIF, WebP
                    </Typography>
                  </Box>
                </Box>
              </Box>
              <StandardButton variant="secondary" onClick={() => setActiveTab('slides')} startIcon={<FaArrowLeft />} size="medium">
                Retour aux diapositives
              </StandardButton>
            </Box>

            <ImageManager modelType="carousel_slide" modelId={selectedSlideId} />
          </>
        )}

        {activeTab === 'slides' && (
          <>
            {/* Slides Header */}
            <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Box>
                <Typography
                  variant="h5"
                  sx={{
                    fontFamily: TYPOGRAPHY.fontFamily.primary,
                    fontSize: TYPOGRAPHY.fontSize.lg,
                    fontWeight: TYPOGRAPHY.fontWeight.semibold,
                    color: COLORS.text.dark
                  }}
                >
                  Diapositives du carrousel: {carousels.find((c) => c.id === selectedCarouselId)?.name}
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    fontFamily: TYPOGRAPHY.fontFamily.primary,
                    color: COLORS.text.secondary,
                    mt: 0.5
                  }}
                >
                  Glissez-déposez les diapositives pour les réorganiser
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <StandardButton variant="secondary" onClick={() => setActiveTab('carousels')} size="medium">
                  Retour aux Carrousels
                </StandardButton>
                <StandardButton
                  variant="primary"
                  onClick={() => {
                    resetSlideForm();
                    setShowSlideModal(true);
                  }}
                  startIcon={<FaPlus />}
                  size="medium"
                >
                  Ajouter une Diapositive
                </StandardButton>
              </Box>
            </Box>

            {/* Slides Table with Drag & Drop */}
            {slideLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 5 }}>
                <Spinner animation="border" variant="primary" />
                <Typography variant="body2" sx={{ ml: 2, color: COLORS.text.secondary }}>
                  Chargement des diapositives...
                </Typography>
              </Box>
            ) : slides.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 5, border: 1, borderColor: COLORS.text.secondary + '20', borderRadius: 1 }}>
                <FaDesktop style={{ fontSize: '3rem', color: COLORS.text.secondary, marginBottom: 16 }} />
                <Typography variant="body1" sx={{ color: COLORS.text.secondary, mb: 2 }}>
                  Aucune diapositive trouvée pour ce carrousel.
                </Typography>
                <StandardButton
                  variant="primary"
                  onClick={() => {
                    resetSlideForm();
                    setShowSlideModal(true);
                  }}
                  startIcon={<FaPlus />}
                >
                  Créer la première diapositive
                </StandardButton>
              </Box>
            ) : (
              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="slides">
                  {(provided) => (
                    <div {...provided.droppableProps} ref={provided.innerRef}>
                      <StandardTable
                        columns={slidesColumns}
                        data={slides.map((slide, index) => ({ ...slide, index }))}
                        loading={false}
                        error={null}
                        emptyMessage="Aucune diapositive trouvée."
                        renderCell={(column, row) => renderSlidesCell(column, row, row.index)}
                        hover={true}
                        draggable={true}
                      />
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            )}

            {/* Slide Modal */}
            <ProfessionalModal
              show={showSlideModal}
              onHide={() => setShowSlideModal(false)}
              title={editingSlideId ? 'Edit Slide' : 'Add New Slide'}
              subtitle="Configure slide content and display settings"
              icon={<FaDesktop />}
              size="lg"
              primaryAction={handleSlideSubmit}
              secondaryAction={() => setShowSlideModal(false)}
              primaryText="Save Slide"
              secondaryText="Cancel"
              loading={slideSubmitting}
              loadingText="Processing..."
              disabled={slideSubmitting}
              variant="primary"
            >
              <Form>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Title</Form.Label>
                      <Form.Control
                        type="text"
                        name="title"
                        value={slideForm.title}
                        onChange={handleSlideChange}
                        placeholder="Enter slide title"
                        required
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Ordre</Form.Label>
                      <Form.Control
                        type="number"
                        name="ordre"
                        value={slideForm.ordre}
                        onChange={handleSlideChange}
                        placeholder="Ordre d'affichage"
                        min="0"
                      />
                    </Form.Group>
                  </Col>
                </Row>
                <Form.Group className="mb-3">
                  <Form.Label>Content</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    name="content"
                    value={slideForm.content}
                    onChange={handleSlideChange}
                    placeholder="Enter slide content"
                  />
                </Form.Group>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Button Text</Form.Label>
                      <Form.Control
                        type="text"
                        name="button_text"
                        value={slideForm.button_text}
                        onChange={handleSlideChange}
                        placeholder="Enter button text"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Button Link</Form.Label>
                      <Form.Control
                        type="text"
                        name="button_link"
                        value={slideForm.button_link}
                        onChange={handleSlideChange}
                        placeholder="Enter button link"
                      />
                    </Form.Group>
                  </Col>
                </Row>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Check
                        type="checkbox"
                        id="is_active"
                        name="is_active"
                        label="Active"
                        checked={slideForm.is_active}
                        onChange={handleSlideChange}
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Check
                        type="checkbox"
                        id="open_in_new_tab"
                        name="open_in_new_tab"
                        label="Open link in new tab"
                        checked={slideForm.open_in_new_tab}
                        onChange={handleSlideChange}
                      />
                    </Form.Group>
                  </Col>
                </Row>
                <Alert variant="info">
                  <FaImage className="me-2" />
                  After saving the slide, you can add desktop and mobile images from the slides list.
                </Alert>
              </Form>
            </ProfessionalModal>
          </>
        )}

        {/* Carousel Modal */}
        <ProfessionalModal
          show={showCarouselModal}
          onHide={() => setShowCarouselModal(false)}
          title={editingCarouselId ? 'Edit Carousel' : 'Add New Carousel'}
          subtitle="Configure carousel settings and display options"
          icon={<FaImage />}
          size="md"
          primaryAction={handleCarouselSubmit}
          secondaryAction={() => setShowCarouselModal(false)}
          primaryText="Save Carousel"
          secondaryText="Cancel"
          loading={carouselSubmitting}
          loadingText="Processing..."
          disabled={carouselSubmitting}
          variant="primary"
        >
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Name</Form.Label>
              <Form.Control
                type="text"
                name="name"
                value={carouselForm.name}
                onChange={handleCarouselChange}
                placeholder="Enter carousel name"
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                name="description"
                value={carouselForm.description}
                onChange={handleCarouselChange}
                placeholder="Enter carousel description"
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Check
                type="checkbox"
                id="is_active"
                name="is_active"
                label="Active"
                checked={carouselForm.is_active}
                onChange={handleCarouselChange}
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Ordre</Form.Label>
              <Form.Control type="number" name="ordre" value={carouselForm.ordre} onChange={handleCarouselChange} min="0" />
              <Form.Text className="text-muted">Ordre d'affichage du carrousel</Form.Text>
            </Form.Group>
          </Form>
        </ProfessionalModal>

        {/* Delete Confirmation Modal */}
        <ProfessionalModal
          show={showDeleteModal}
          onHide={() => setShowDeleteModal(false)}
          title="Confirm Delete"
          subtitle="This action cannot be undone"
          icon={<FaExclamationTriangle />}
          size="sm"
          primaryAction={handleDelete}
          secondaryAction={() => setShowDeleteModal(false)}
          primaryText="Delete"
          secondaryText="Cancel"
          primaryVariant="danger"
          variant="danger"
        >
          <div className="text-center">
            {itemToDelete.type === 'carousel' && (
              <p className="mb-0">
                Are you sure you want to delete this carousel? This will also delete all slides associated with this carousel.
              </p>
            )}
            {itemToDelete.type === 'slide' && <p className="mb-0">Are you sure you want to delete this slide?</p>}
          </div>
        </ProfessionalModal>

        {/* Custom CSS for professional tabs styling */}
        <style jsx="true">{`
          .nav-tabs-custom .nav-link {
            color: #495057;
            font-weight: 500;
            padding: 1rem 1.5rem;
            border-radius: 0;
            border: none;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
          }
          .nav-tabs-custom .nav-link.active {
            color: #2196f3;
            background: transparent;
            border-bottom: 3px solid #2196f3;
          }
          .nav-tabs-custom .nav-link:hover:not(.active) {
            border-bottom: 3px solid #e9ecef;
            color: #2196f3;
          }
          .nav-tabs-custom .nav-link:disabled {
            color: #6c757d;
            opacity: 0.6;
            cursor: not-allowed;
          }
          .nav-tabs-custom .nav-link:disabled:hover {
            border-bottom: 3px solid transparent;
            color: #6c757d;
          }
        `}</style>
      </Box>
    </MainCard>
  );
};

export default CarouselManagement;
