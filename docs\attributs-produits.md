# Système d'Attributs de Produits

## Introduction

Le système d'attributs de produits est une refonte complète de la gestion des caractéristiques des produits. Il offre une plus grande flexibilité, robustesse et évolutivité pour gérer des catalogues de produits complexes avec des attributs variables.

## Concepts clés

### Groupes d'attributs

Les groupes d'attributs permettent d'organiser les attributs en catégories logiques, facilitant leur gestion dans l'interface d'administration.

### Attributs

Les attributs définissent les propriétés que peuvent avoir les produits. Chaque attribut a un type de valeur spécifique (texte, nombre, date, booléen, liste) et peut être associé à des sous-catégories.

### Valeurs d'attributs

Les valeurs d'attributs sont les valeurs spécifiques assignées à un produit pour un attribut donné. Elles sont typées selon le type de l'attribut.

### Variantes de produits

Les variantes permettent de gérer des déclinaisons d'un produit (tailles, couleurs, etc.) avec leurs propres valeurs d'attributs, stocks et prix.

## Structure de données

```ascii
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│ Produit         │     │ Attribut        │     │ Groupe          │
├─────────────────┤     ├─────────────────┤     ├─────────────────┤
│ id              │     │ id              │     │ id              │
│ nom_produit     │     │ nom             │     │ nom             │
│ description     │     │ description     │     │ description     │
│ prix            │     │ type_valeur     │     └─────────────────┘
│ ...             │     │ groupe_id       │              ▲
└─────────────────┘     │ obligatoire     │              │
        │               │ filtrable       │              │
        │               │ comparable      │     ┌─────────────────┐
        │               └─────────────────┘     │ AttributGroupe  │
        │                       ▲               ├─────────────────┤
        │                       │               │ id              │
        │               ┌───────┴───────┐       │ attribut_id     │
        │               │               │       │ groupe_id       │
        │               │               │       └─────────────────┘
┌───────▼───────┐ ┌─────▼───────────┐
│ ProduitValeur │ │ AttributCategorie│
├───────────────┤ ├─────────────────┤
│ id            │ │ id              │
│ produit_id    │ │ attribut_id     │
│ attribut_id   │ │ categorie_id    │
│ valeur_texte  │ │ obligatoire     │
│ valeur_nombre │ └─────────────────┘
│ valeur_date   │
└───────────────┘
```

## API Endpoints

### Routes pour le frontoffice (storefront)

Ces routes sont conçues pour être utilisées par l'interface client (storefront) et ne nécessitent pas d'authentification.

```http
GET /api/produits/{id}/attributs
```

Retourne les attributs d'un produit spécifique.

```http
GET /api/produits/{id}/variantes
```

Retourne les variantes d'un produit spécifique.

```http
GET /api/attributs/filtrables
```

Retourne la liste des attributs filtrables avec leurs valeurs disponibles, regroupés par groupe d'attributs.

```http
GET /api/produits/filtrer
```

Filtre les produits selon différents critères, y compris les attributs.

#### Paramètres de filtrage (optionnels)

| Paramètre         | Type    | Description                                |
|-------------------|---------|-----------------------------------------|
| marque_id         | integer | Filtrer par marque                       |
| sous_categorie_id | integer | Filtrer par sous-catégorie              |
| sous_sous_categorie_id | integer | Filtrer par sous-sous-catégorie        |
| attributs         | object  | Filtrer par attributs (voir exemple)     |
| prix_min          | decimal | Prix minimum                             |
| prix_max          | decimal | Prix maximum                             |
| en_promotion      | boolean | Produits en promotion                    |
| sort_by           | string  | Champ de tri (par défaut: nom_produit)   |
| sort_direction    | string  | Direction du tri (asc ou desc)           |
| per_page          | integer | Nombre d'éléments par page (par défaut: 12) |

#### Exemple de requête

```http
GET /api/produits/filtrer?marque_id=1&attributs[3][]=Rouge&attributs[3][]=Bleu&attributs[5][min]=10&attributs[5][max]=50&prix_min=20&prix_max=100&en_promotion=true&sort_by=prix_produit&sort_direction=asc&per_page=24
```

Cette requête filtre les produits de la marque 1, avec l'attribut 3 (couleur) égal à "Rouge" ou "Bleu", l'attribut 5 (taille) entre 10 et 50, un prix entre 20 et 100, en promotion, triés par prix croissant, avec 24 produits par page.

### Routes pour le backoffice (admin)

Ces routes sont conçues pour être utilisées par l'interface d'administration (backoffice).

#### Gestion des groupes d'attributs

```http
GET /api/admin/groupes-attributs
```

Retourne la liste de tous les groupes d'attributs.

```http
POST /api/admin/groupes-attributs
```

Crée un nouveau groupe d'attributs.

##### Paramètres de la requête

| Paramètre    | Type   | Description                        |
|--------------|--------|------------------------------------|
| nom          | string | Nom du groupe (obligatoire)        |
| description  | string | Description du groupe (optionnelle)|

```http
GET /api/admin/groupes-attributs/{id}
```

Retourne les détails d'un groupe d'attributs spécifique avec ses attributs.

```http
PUT /api/admin/groupes-attributs/{id}
```

Met à jour un groupe d'attributs existant.

```http
DELETE /api/admin/groupes-attributs/{id}
```

Supprime un groupe d'attributs (uniquement s'il ne contient pas d'attributs).

#### Gestion des attributs

```http
GET /api/admin/attributs
```

Retourne la liste de tous les attributs avec filtrage possible.

##### Paramètres de filtrage (optionnels)

| Paramètre         | Type    | Description                                |
|-------------------|---------|--------------------------------------------|
| groupe_id         | integer | Filtrer par ID de groupe                   |
| type_valeur       | string  | Filtrer par type de valeur                 |
| filtrable         | boolean | Filtrer les attributs filtrables           |
| comparable        | boolean | Filtrer les attributs comparables          |
| sous_categorie_id | integer | Filtrer par sous-catégorie associée        |

```http
POST /api/admin/attributs
```

Crée un nouvel attribut.

##### Paramètres de la requête

| Paramètre       | Type    | Description                                |
|-----------------|---------|--------------------------------------------|
| nom             | string  | Nom de l'attribut (obligatoire)            |
| description     | string  | Description de l'attribut                  |
| type_valeur     | string  | Type de valeur (texte, nombre, date, booleen, liste) |
| groupe_id       | integer | ID du groupe d'attributs                   |
| obligatoire     | boolean | Si l'attribut est obligatoire              |
| filtrable       | boolean | Si l'attribut peut être utilisé pour filtrer |
| comparable      | boolean | Si l'attribut peut être utilisé pour comparer |
| sous_categories | array   | Tableau des sous-catégories associées      |

##### Exemple de requête

```json
{
  "nom": "Couleur",
  "description": "Couleur du produit",
  "type_valeur": "texte",
  "groupe_id": 1,
  "filtrable": true,
  "comparable": true,
  "sous_categories": [
    {
      "id": 3,
      "obligatoire": true
    },
    {
      "id": 4,
      "obligatoire": false
    }
  ]
}
```

```http
GET /api/admin/attributs/{id}
```

Retourne les détails d'un attribut spécifique avec ses relations.

```http
PUT /api/admin/attributs/{id}
```

Met à jour un attribut existant.

##### Paramètres de la requête

| Paramètre    | Type    | Description                                |
|--------------|---------|--------------------------------------------|
| nom          | string  | Nom de l'attribut                          |
| description  | string  | Description de l'attribut (optionnel)      |
| type_valeur  | string  | Type de valeur (texte, nombre, date, booleen, liste) |
| groupe_id    | integer | ID du groupe d'attributs (optionnel)       |
| obligatoire  | boolean | Si l'attribut est obligatoire par défaut    |
| filtrable    | boolean | Si l'attribut peut être utilisé pour filtrer |
| comparable   | boolean | Si l'attribut peut être utilisé pour comparer |
| sous_categories | array | Tableau des sous-catégories associées      |

##### Exemple de requête

```json
{
  "nom": "Taille",
  "description": "Taille du produit",
  "type_valeur": "texte",
  "groupe_id": 1,
  "filtrable": true,
  "comparable": true,
  "sous_categories": [
    {
      "id": 3,
      "obligatoire": true
    },
    {
      "id": 4,
      "obligatoire": false
    }
  ]
}
```

##### Exemple de réponse

```json
{
  "id": 5,
  "nom": "Taille",
  "description": "Taille du produit",
  "type_valeur": "texte",
  "groupe_id": 1,
  "obligatoire": false,
  "filtrable": true,
  "comparable": true,
  "created_at": "2025-04-08T12:36:06.000000Z",
  "updated_at": "2025-04-09T15:45:22.000000Z",
  "deleted_at": null,
  "groupe": {
    "id": 1,
    "nom": "Dimensions",
    "description": "Caractéristiques dimensionnelles",
    "created_at": "2025-04-08T12:36:05.000000Z",
    "updated_at": "2025-04-08T12:36:05.000000Z",
    "deleted_at": null
  },
  "sous_categories": [
    {
      "id": 3,
      "nom_sous_categorie": "Smartphones",
      "pivot": {
        "attribut_id": 5,
        "sous_categorie_id": 3,
        "obligatoire": true
      }
    },
    {
      "id": 4,
      "nom_sous_categorie": "Tablettes",
      "pivot": {
        "attribut_id": 5,
        "sous_categorie_id": 4,
        "obligatoire": false
      }
    }
  ]
}
```

```http
DELETE /api/admin/attributs/{id}
```

Supprime un attribut. Cette opération n'est possible que si l'attribut n'est pas utilisé par des produits ou des variantes.

##### Exemple de réponse en cas de succès

```json
{
  "message": "Attribut supprimé avec succès"
}
```

##### Exemple de réponse en cas d'erreur

```json
{
  "error": "Impossible de supprimer cet attribut",
  "message": "Cet attribut est utilisé par des produits. Veuillez d'abord supprimer ces valeurs."
}
```

```http
POST /api/admin/attributs/{id}/sous-categories/{sousCategorieId}
```

Associe un attribut à une sous-catégorie.

##### Paramètres de la requête

| Paramètre   | Type    | Description                                |
|-------------|---------|--------------------------------------------|
| obligatoire | boolean | Si l'attribut est obligatoire pour cette sous-catégorie |

```http
DELETE /api/admin/attributs/{id}/sous-categories/{sousCategorieId}
```

Détache un attribut d'une sous-catégorie.

#### Gestion des variantes de produits

```http
GET /api/produits/{produit_id}/variantes
```

Retourne les variantes d'un produit spécifique.

##### Paramètres de filtrage (optionnels)

| Paramètre  | Type    | Description                                |
|------------|---------|--------------------------------------------|
| actif      | boolean | Filtrer par statut actif                   |
| disponible | boolean | Filtrer par disponibilité en stock         |

```http
POST /api/produits/{produit_id}/variantes
```

Crée une nouvelle variante pour un produit.

##### Paramètres de la requête

| Paramètre       | Type    | Description                                |
|-----------------|---------|--------------------------------------------|
| sku             | string  | Code SKU unique (obligatoire)              |
| prix_supplement | decimal | Supplément de prix par rapport au produit parent |
| stock           | integer | Quantité en stock                          |
| actif           | boolean | Si la variante est active                  |
| valeurs         | array   | Tableau des valeurs d'attributs            |

##### Exemple de requête

```json
{
  "sku": "PROD-001-RED-L",
  "prix_supplement": 5.00,
  "stock": 10,
  "actif": true,
  "valeurs": [
    {
      "attribut_id": 1,
      "valeur": "Rouge"
    },
    {
      "attribut_id": 2,
      "valeur": "L"
    }
  ]
}
```

```http
GET /api/variantes/{id}
```

Retourne les détails d'une variante spécifique.

```http
PUT /api/variantes/{id}
```

Met à jour une variante existante.

```http
DELETE /api/variantes/{id}
```

Supprime une variante.

```http
PATCH /api/variantes/{id}/stock
```

Met à jour uniquement le stock d'une variante.

##### Paramètres de la requête

| Paramètre | Type    | Description                                |
|-----------|---------|--------------------------------------------|
| stock     | integer | Nouvelle quantité en stock (obligatoire)   |

## Utilisation avec les produits existants

### Obtenir les valeurs d'attributs d'un produit

```php
$produit = Produit::with('valeurs.attribut')->find($id);
$valeurs = $produit->valeurs;

// Obtenir une valeur spécifique
$couleur = $produit->getValeurAttribut($attributId);
```

### Définir une valeur d'attribut pour un produit

```php
$produit->setValeurAttribut($attributId, $valeur);
```

## Migration depuis l'ancien système

La migration depuis l'ancien système de caractéristiques est automatisée via une migration dédiée. Cette migration :

1. Crée un groupe d'attributs par défaut
2. Convertit les caractéristiques existantes en attributs
3. Associe les attributs aux sous-catégories appropriées
4. Migre les valeurs de caractéristiques des produits vers le nouveau format

Pour exécuter la migration manuellement :

```bash
php artisan migrate --path=database/migrations/2025_04_08_130000_migrate_caracteristiques_to_attributs.php
```

## Avantages du nouveau système

1. **Flexibilité** : Ajout facile de nouveaux attributs sans modification de schéma
2. **Robustesse** : Validation des types de données au niveau de la base de données
3. **Performance** : Colonnes typées pour des requêtes efficaces
4. **Évolutivité** : Support pour des attributs multilingues et des variantes de produits
5. **Organisation** : Groupement logique des attributs pour une meilleure gestion
