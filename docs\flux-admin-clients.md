# Flux d'Administration des Clients

Ce document décrit les différentes opérations d'administration des clients disponibles dans le système, notamment la gestion des types de clients et des remises.

## Types de clients

Le système prend en charge quatre types de clients:

1. **Client normal**: Client standard sans remise spécifique
2. **Client partenaire**: Client bénéficiant d'une remise partenaire
3. **Client point de vente**: Client associé à un point de vente physique
4. **Client groupe**: Client appartenant à un groupe avec une remise commune

## Flux d'administration

### 1. Consultation des clients

Un administrateur peut consulter la liste de tous les clients, avec possibilité de filtrage.

```
GET /api/clients
```

**Paramètres de filtrage (optionnels):**
- `type`: Type de client (normal, partenaire, point_de_vente, groupe)
- `point_de_vente_id`: ID du point de vente
- `groupe_client_id`: ID du groupe de clients

**Exemple de réponse:**
```json
[
  {
    "id": 1,
    "name": "<PERSON><PERSON><PERSON>",
    "email": "<EMAIL>",
    "roles": ["client"],
    "remise_personnelle": 12.75,
    "type_client": "normal",
    "remise_effective": 12.75
  },
  {
    "id": 2,
    "name": "Entreprise ABC",
    "email": "<EMAIL>",
    "roles": ["client", "partenaire"],
    "remise_personnelle": 0,
    "type_client": "partenaire",
    "remise_effective": 15.0,
    "partenaire": {
      "id": 1,
      "remise": 15.0,
      "description": "Partenaire premium",
      "statut": "actif"
    }
  }
]
```

### 2. Modification de la remise personnelle

Un administrateur peut modifier la remise personnelle d'un client.

```
PUT /api/clients/{id}/remise
```

**Corps de la requête:**
```json
{
  "remise_personnelle": 12.75
}
```

**Réponse:**
```json
{
  "id": 1,
  "name": "Youssef Mrabet",
  "email": "<EMAIL>",
  "remise_personnelle": 12.75,
  "type_client": "normal",
  "remise_effective": 12.75
}
```

### 3. Changement de type de client

Un administrateur peut changer le type d'un client.

```
PUT /api/clients/{id}/type
```

#### 3.1. Changement en client normal

**Corps de la requête:**
```json
{
  "type_client": "normal"
}
```

#### 3.2. Changement en client partenaire

**Corps de la requête:**
```json
{
  "type_client": "partenaire"
}
```

Cette action créera automatiquement une entrée partenaire avec une remise par défaut de 0%. L'administrateur pourra ensuite mettre à jour cette remise via l'endpoint `PUT /api/partenaires/{id}`.

#### 3.3. Changement en client point de vente

**Corps de la requête:**
```json
{
  "type_client": "point_de_vente",
  "point_de_vente_id": 1
}
```

#### 3.4. Changement en client groupe

**Corps de la requête:**
```json
{
  "type_client": "groupe",
  "groupe_client_id": 1
}
```

### 4. Gestion des partenaires

#### 4.1. Création d'un partenaire

```
POST /api/partenaires
```

**Corps de la requête:**
```json
{
  "user_id": 3,
  "remise": 10.00,
  "description": "Nouveau partenaire",
  "statut": "actif"
}
```

#### 4.2. Modification d'un partenaire

```
PUT /api/partenaires/{id}
```

**Corps de la requête:**
```json
{
  "remise": 12.50,
  "description": "Partenaire mis à jour",
  "statut": "actif"
}
```

#### 4.3. Suppression d'un partenaire

```
DELETE /api/partenaires/{id}
```

### 5. Gestion des points de vente

#### 5.1. Création d'un point de vente

```
POST /api/points-de-vente
```

**Corps de la requête:**
```json
{
  "nom": "Boutique Centre Ville",
  "adresse": "123 Rue Principale",
  "telephone": "71234567",
  "email": "<EMAIL>",
  "remise": 8.50,
  "description": "Point de vente principal",
  "statut": "actif"
}
```

#### 5.2. Ajout d'un client à un point de vente

```
POST /api/points-de-vente/{id}/clients
```

**Corps de la requête:**
```json
{
  "user_id": 4
}
```

#### 5.3. Retrait d'un client d'un point de vente

```
DELETE /api/points-de-vente/{id}/clients/{userId}
```

### 6. Gestion des groupes de clients

#### 6.1. Création d'un groupe de clients

```
POST /api/groupes-clients
```

**Corps de la requête:**
```json
{
  "nom": "Clients Premium",
  "description": "Groupe de clients premium",
  "remise": 10.00,
  "statut": "actif"
}
```

#### 6.2. Ajout d'un client à un groupe

```
POST /api/groupes-clients/{id}/clients
```

**Corps de la requête:**
```json
{
  "user_id": 5
}
```

#### 6.3. Retrait d'un client d'un groupe

```
DELETE /api/groupes-clients/{id}/clients/{userId}
```

## Consultation des commandes d'un client

Un administrateur peut consulter les commandes d'un client spécifique.

### Récupérer toutes les commandes d'un client

```
GET /api/clients/{id}/commandes
```

### Récupérer la dernière commande d'un client

```
GET /api/clients/{id}/derniere-commande
```

Ou via l'endpoint administrateur protégé:

```
GET /api/v1/admin/clients/{id}/derniere-commande
```

## Diagramme de flux pour la gestion des types de clients

```
┌─────────────────┐
│ Consultation    │
│  des clients    │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Sélection d'un  │
│     client      │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Choix du type   │
│    de client    │
└────────┬────────┘
         │
         ├─────────────┬─────────────┬─────────────┐
         │             │             │             │
         ▼             ▼             ▼             ▼
┌─────────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│     Normal      │ │ Partenaire  │ │ Point de    │ │   Groupe    │
│                 │ │             │ │   vente     │ │             │
└─────────────────┘ └──────┬──────┘ └──────┬──────┘ └──────┬──────┘
                           │               │               │
                           ▼               ▼               ▼
                   ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
                   │ Création ou │ │ Sélection   │ │ Sélection   │
                   │ sélection   │ │ d'un point  │ │ d'un groupe │
                   │ partenaire  │ │ de vente    │ │ de clients  │
                   └──────┬──────┘ └──────┬──────┘ └──────┬──────┘
                          │               │               │
                          ▼               ▼               ▼
                   ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
                   │Configuration│ │Configuration│ │Configuration│
                   │ de la remise│ │ de la remise│ │ de la remise│
                   │ partenaire  │ │ point vente │ │   groupe    │
                   └─────────────┘ └─────────────┘ └─────────────┘
```

## Bonnes pratiques pour l'administration des clients

1. **Remises personnelles**: À utiliser pour des cas spécifiques où un client individuel doit bénéficier d'une remise unique, indépendamment de son type.

2. **Partenaires**: À utiliser pour des entreprises ou organisations qui ont un accord commercial spécifique avec vous.

3. **Points de vente**: À utiliser pour représenter des emplacements physiques avec plusieurs clients associés (ex: une boutique avec plusieurs employés comme clients).

4. **Groupes de clients**: À utiliser pour regrouper logiquement des clients qui partagent des caractéristiques communes (ex: clients VIP, clients professionnels) sans nécessairement être liés à un emplacement physique.

5. **Hiérarchie des remises**: Toujours garder à l'esprit que la remise personnelle prend priorité sur les autres types de remises. Utilisez cette fonctionnalité avec parcimonie pour éviter des incohérences dans la politique de remises.
