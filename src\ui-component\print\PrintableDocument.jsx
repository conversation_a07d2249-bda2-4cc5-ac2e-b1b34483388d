import React from 'react';
import { Box, Typography, Divider, Table, TableBody, TableCell, TableHead, TableRow, Paper } from '@mui/material';
import { COLORS, TYPOGRAPHY } from 'themes/designSystem';

const PrintableDocument = ({ type, data, onPrint }) => {
  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatPrice = (price) => {
    if (!price) return '0.000 DT';
    return `${parseFloat(price).toFixed(3)} DT`;
  };

  const handlePrint = () => {
    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    const printContent = document.getElementById('printable-content');
    
    if (printWindow && printContent) {
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>${type === 'order' ? 'Commande' : 'Facture'} - JihenLine</title>
          <meta charset="utf-8">
          <style>
            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
            }
            
            body {
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              font-size: 12px;
              line-height: 1.4;
              color: #333;
              background: white;
            }
            
            .print-container {
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
            }
            
            .header {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 30px;
              border-bottom: 3px solid #1976d2;
              padding-bottom: 20px;
            }
            
            .company-info h1 {
              font-size: 28px;
              color: #1976d2;
              margin-bottom: 5px;
              font-weight: bold;
            }
            
            .company-info p {
              margin: 2px 0;
              color: #666;
            }
            
            .document-info {
              text-align: right;
            }
            
            .document-info h2 {
              font-size: 24px;
              color: #1976d2;
              margin-bottom: 10px;
            }
            
            .document-info p {
              margin: 3px 0;
            }
            
            .section {
              margin: 25px 0;
            }
            
            .section-title {
              font-size: 16px;
              font-weight: bold;
              color: #1976d2;
              margin-bottom: 15px;
              border-bottom: 1px solid #e0e0e0;
              padding-bottom: 5px;
            }
            
            .info-grid {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 20px;
              margin-bottom: 20px;
            }
            
            .info-item {
              margin-bottom: 8px;
            }
            
            .info-label {
              font-weight: bold;
              color: #555;
              display: inline-block;
              min-width: 120px;
            }
            
            .info-value {
              color: #333;
            }
            
            .products-table {
              width: 100%;
              border-collapse: collapse;
              margin: 20px 0;
            }
            
            .products-table th,
            .products-table td {
              border: 1px solid #ddd;
              padding: 12px 8px;
              text-align: left;
            }
            
            .products-table th {
              background-color: #f5f5f5;
              font-weight: bold;
              color: #333;
            }
            
            .products-table .text-right {
              text-align: right;
            }
            
            .products-table .text-center {
              text-align: center;
            }
            
            .total-section {
              margin-top: 20px;
              text-align: right;
            }
            
            .total-row {
              display: flex;
              justify-content: flex-end;
              margin: 5px 0;
            }
            
            .total-label {
              min-width: 150px;
              font-weight: bold;
              padding: 5px 10px;
            }
            
            .total-value {
              min-width: 100px;
              padding: 5px 10px;
              text-align: right;
            }
            
            .grand-total {
              background-color: #1976d2;
              color: white;
              font-size: 16px;
              font-weight: bold;
            }
            
            .footer {
              margin-top: 40px;
              padding-top: 20px;
              border-top: 1px solid #e0e0e0;
              text-align: center;
              color: #666;
              font-size: 11px;
            }
            
            .status-badge {
              display: inline-block;
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 11px;
              font-weight: bold;
              text-transform: uppercase;
            }
            
            .status-completed {
              background-color: #e8f5e8;
              color: #2e7d32;
            }
            
            .status-pending {
              background-color: #fff3e0;
              color: #f57c00;
            }
            
            .status-cancelled {
              background-color: #ffebee;
              color: #d32f2f;
            }
            
            @media print {
              body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
              }
              
              .print-container {
                max-width: none;
                margin: 0;
                padding: 0;
              }
              
              .header {
                page-break-after: avoid;
              }
              
              .products-table {
                page-break-inside: avoid;
              }
              
              .footer {
                page-break-before: avoid;
              }
            }
          </style>
        </head>
        <body>
          ${printContent.innerHTML}
        </body>
        </html>
      `);
      
      printWindow.document.close();
      printWindow.focus();
      
      // Wait for content to load then print
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 250);
    }
    
    if (onPrint) {
      onPrint();
    }
  };

  const renderOrderContent = () => (
    <div className="print-container">
      {/* Header */}
      <div className="header">
        <div className="company-info">
          <h1>JihenLine</h1>
          <p>Plateforme E-commerce</p>
          <p>Tunis, Tunisie</p>
          <p>Email: <EMAIL></p>
          <p>Tél: +216 XX XXX XXX</p>
        </div>
        <div className="document-info">
          <h2>COMMANDE</h2>
          <p><strong>N°:</strong> {data.numero_commande || data.order_number || `CMD-${data.id}`}</p>
          <p><strong>Date:</strong> {formatDate(data.created_at)}</p>
          <p><strong>Statut:</strong> <span className={`status-badge status-${data.status?.toLowerCase()}`}>{data.status}</span></p>
        </div>
      </div>

      {/* Customer Info */}
      <div className="section">
        <div className="section-title">Informations Client</div>
        <div className="info-grid">
          <div>
            <div className="info-item">
              <span className="info-label">Nom:</span>
              <span className="info-value">{data.user?.name || data.customer_name || 'N/A'}</span>
            </div>
            <div className="info-item">
              <span className="info-label">Email:</span>
              <span className="info-value">{data.user?.email || data.customer_email || 'N/A'}</span>
            </div>
            <div className="info-item">
              <span className="info-label">Téléphone:</span>
              <span className="info-value">{data.user?.phone || data.customer_phone || 'N/A'}</span>
            </div>
          </div>
          <div>
            <div className="info-item">
              <span className="info-label">Méthode de paiement:</span>
              <span className="info-value">{data.methode_paiement || data.payment_method || 'N/A'}</span>
            </div>
            <div className="info-item">
              <span className="info-label">Date de commande:</span>
              <span className="info-value">{formatDate(data.created_at)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Shipping Address */}
      {(data.shipping_address || data.shipping_address_line1) && (
        <div className="section">
          <div className="section-title">Adresse de Livraison</div>
          <div className="info-item">
            <div>{data.shipping_address?.street || data.shipping_address_line1}</div>
            <div>{data.shipping_address?.postal_code || data.shipping_postal_code} {data.shipping_address?.city || data.shipping_city}</div>
            <div>{data.shipping_address?.country || data.shipping_country || 'Tunisie'}</div>
          </div>
        </div>
      )}

      {/* Products */}
      <div className="section">
        <div className="section-title">Produits Commandés</div>
        <table className="products-table">
          <thead>
            <tr>
              <th>Produit</th>
              <th className="text-center">Quantité</th>
              <th className="text-right">Prix Unitaire</th>
              <th className="text-right">Total</th>
            </tr>
          </thead>
          <tbody>
            {(data.produits || data.items || []).map((produit, index) => (
              <tr key={produit.id || index}>
                <td>{produit.nom_produit || produit.product_name || 'Produit'}</td>
                <td className="text-center">{produit.pivot?.quantite || produit.quantity || 1}</td>
                <td className="text-right">{formatPrice(produit.pivot?.prix_unitaire || produit.unit_price || 0)}</td>
                <td className="text-right">{formatPrice(produit.pivot?.total_ligne || produit.total_price || 0)}</td>
              </tr>
            ))}
          </tbody>
        </table>

        <div className="total-section">
          <div className="total-row grand-total">
            <div className="total-label">TOTAL</div>
            <div className="total-value">{formatPrice(data.total_commande || data.total || 0)}</div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="footer">
        <p>Merci pour votre confiance - JihenLine</p>
        <p>Document généré le {formatDate(new Date().toISOString())}</p>
      </div>
    </div>
  );

  const renderInvoiceContent = () => (
    <div className="print-container">
      {/* Header */}
      <div className="header">
        <div className="company-info">
          <h1>JihenLine</h1>
          <p>Plateforme E-commerce</p>
          <p>Tunis, Tunisie</p>
          <p>Email: <EMAIL></p>
          <p>Tél: +216 XX XXX XXX</p>
        </div>
        <div className="document-info">
          <h2>FACTURE</h2>
          <p><strong>N°:</strong> {data.invoice_number || `FAC-${data.id}`}</p>
          <p><strong>Date:</strong> {formatDate(data.invoice_date || data.created_at)}</p>
          <p><strong>Statut:</strong> <span className={`status-badge status-${data.status?.toLowerCase()}`}>{data.status}</span></p>
        </div>
      </div>

      {/* Invoice Info */}
      <div className="section">
        <div className="section-title">Informations Facture</div>
        <div className="info-grid">
          <div>
            <div className="info-item">
              <span className="info-label">Commande N°:</span>
              <span className="info-value">{data.order?.numero_commande || data.order?.order_number || `CMD-${data.order?.id}`}</span>
            </div>
            <div className="info-item">
              <span className="info-label">Client:</span>
              <span className="info-value">{data.order?.user?.name || data.order?.customer_name || 'N/A'}</span>
            </div>
            <div className="info-item">
              <span className="info-label">Email:</span>
              <span className="info-value">{data.order?.user?.email || data.order?.customer_email || 'N/A'}</span>
            </div>
          </div>
          <div>
            <div className="info-item">
              <span className="info-label">Montant:</span>
              <span className="info-value">{formatPrice(data.amount || data.montant)}</span>
            </div>
            <div className="info-item">
              <span className="info-label">Méthode de paiement:</span>
              <span className="info-value">{data.methode_paiement || 'N/A'}</span>
            </div>
            <div className="info-item">
              <span className="info-label">Date de paiement:</span>
              <span className="info-value">{formatDate(data.created_at)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Details */}
      <div className="section">
        <div className="section-title">Détails du Paiement</div>
        <div className="total-section">
          <div className="total-row">
            <div className="total-label">Montant HT:</div>
            <div className="total-value">{formatPrice((data.amount || data.montant) * 0.81)}</div>
          </div>
          <div className="total-row">
            <div className="total-label">TVA (19%):</div>
            <div className="total-value">{formatPrice((data.amount || data.montant) * 0.19)}</div>
          </div>
          <div className="total-row grand-total">
            <div className="total-label">TOTAL TTC</div>
            <div className="total-value">{formatPrice(data.amount || data.montant)}</div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="footer">
        <p>Merci pour votre confiance - JihenLine</p>
        <p>Facture générée le {formatDate(new Date().toISOString())}</p>
        <p>TVA: TN123456789 - RC: B123456789</p>
      </div>
    </div>
  );

  return (
    <Box>
      <Box id="printable-content" sx={{ display: 'none' }}>
        {type === 'order' ? renderOrderContent() : renderInvoiceContent()}
      </Box>
      
      {/* Trigger print */}
      <Box sx={{ display: 'none' }}>
        {handlePrint()}
      </Box>
    </Box>
  );
};

export default PrintableDocument;
