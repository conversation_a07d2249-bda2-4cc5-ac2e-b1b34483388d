# Flux de Création d'une Commande

Ce document décrit le flux complet de création d'une commande dans le système, de l'authentification de l'utilisateur jusqu'à la finalisation de la commande.

## Étapes du flux

### 1. Authentification de l'utilisateur

L'utilisateur doit d'abord s'authentifier via Keycloak pour obtenir un token JWT.

```
POST /api/auth/verify
```

**Corps de la requête:**
```json
{
  "token": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJfT3B2Ym1..."
}
```

**Réponse:**
```json
{
  "user": {
    "id": 1,
    "name": "<PERSON><PERSON><PERSON>",
    "email": "<EMAIL>",
    "roles": ["client"],
    "remise_personnelle": 10.50,
    "type_client": "normal"
  },
  "token": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJfT3B2Ym1..."
}
```

### 2. Consultation du catalogue de produits

L'utilisateur peut consulter le catalogue de produits, filtrer par catégorie, sous-catégorie ou marque.

```
GET /api/produits?categorie_id=1
```

**Réponse:**
```json
[
  {
    "id": 1,
    "nom": "Laptop Pro X",
    "description": "Ordinateur portable haute performance",
    "prix": 1299.99,
    "quantite_stock": 15,
    "marque_id": 1,
    "sous_categorie_id": 3,
    "image": "laptop_pro_x.jpg"
  },
  {
    "id": 3,
    "nom": "Desktop Power",
    "description": "Ordinateur de bureau puissant",
    "prix": 899.99,
    "quantite_stock": 10,
    "marque_id": 1,
    "sous_categorie_id": 4,
    "image": "desktop_power.jpg"
  }
]
```

### 3. Consultation des détails d'un produit

L'utilisateur peut consulter les détails d'un produit spécifique.

```
GET /api/produits/1
```

**Réponse:**
```json
{
  "id": 1,
  "nom": "Laptop Pro X",
  "description": "Ordinateur portable haute performance",
  "prix": 1299.99,
  "quantite_stock": 15,
  "marque_id": 1,
  "sous_categorie_id": 3,
  "image": "laptop_pro_x.jpg",
  "marque": {
    "id": 1,
    "nom": "TechPro"
  },
  "sous_categorie": {
    "id": 3,
    "nom": "Ordinateurs portables",
    "categorie": {
      "id": 1,
      "nom": "Informatique"
    }
  },
  "caracteristiques": [
    {
      "id": 1,
      "nom": "Processeur",
      "valeur": "Intel Core i7"
    },
    {
      "id": 2,
      "nom": "Mémoire RAM",
      "valeur": "16 Go"
    },
    {
      "id": 3,
      "nom": "Stockage",
      "valeur": "512 Go SSD"
    }
  ]
}
```

### 4. Création de la commande

L'utilisateur crée une commande en spécifiant les produits, les quantités et les informations de livraison.

```
POST /api/commandes
```

**Corps de la requête:**
```json
{
  "user_id": 1,
  "adresse_commande": "123 Rue Principale",
  "ville_commande": "Tunis",
  "code_postal_commande": "1000",
  "telephone_commande": "71234567",
  "email_commande": "<EMAIL>",
  "produits": [
    {
      "id": 1,
      "quantite": 1,
      "prix_unitaire": 1299.99
    },
    {
      "id": 2,
      "quantite": 2,
      "prix_unitaire": 499.99
    }
  ]
}
```

**Réponse:**
```json
{
  "id": 5,
  "user_id": 1,
  "adresse_commande": "123 Rue Principale",
  "ville_commande": "Tunis",
  "code_postal_commande": "1000",
  "telephone_commande": "71234567",
  "email_commande": "<EMAIL>",
  "total_commande": 2074.97,
  "remise_commande": 0,
  "created_at": "2025-04-04T16:30:00.000000Z",
  "updated_at": "2025-04-04T16:30:00.000000Z",
  "produits": [
    {
      "id": 1,
      "nom": "Laptop Pro X",
      "pivot": {
        "commande_id": 5,
        "produit_id": 1,
        "quantite": 1,
        "prix_unitaire": 1299.99
      }
    },
    {
      "id": 2,
      "nom": "Smartphone XYZ",
      "pivot": {
        "commande_id": 5,
        "produit_id": 2,
        "quantite": 2,
        "prix_unitaire": 499.99
      }
    }
  ],
  "client_remise": 10.5
}
```

### 5. Création du paiement

L'utilisateur procède au paiement de la commande.

```
POST /api/paiements
```

**Corps de la requête:**
```json
{
  "commande_id": 5,
  "montant": 2074.97,
  "methode": "carte",
  "statut": "complete",
  "reference": "PAY-123456789"
}
```

**Réponse:**
```json
{
  "id": 3,
  "commande_id": 5,
  "montant": 2074.97,
  "methode": "carte",
  "statut": "complete",
  "reference": "PAY-123456789",
  "created_at": "2025-04-04T16:35:00.000000Z",
  "updated_at": "2025-04-04T16:35:00.000000Z"
}
```

### 6. Consultation de la commande

L'utilisateur peut consulter les détails de sa commande.

```
GET /api/commandes/5
```

**Réponse:**
```json
{
  "id": 5,
  "user_id": 1,
  "adresse_commande": "123 Rue Principale",
  "ville_commande": "Tunis",
  "code_postal_commande": "1000",
  "telephone_commande": "71234567",
  "email_commande": "<EMAIL>",
  "total_commande": 2074.97,
  "remise_commande": 0,
  "created_at": "2025-04-04T16:30:00.000000Z",
  "updated_at": "2025-04-04T16:30:00.000000Z",
  "user": {
    "id": 1,
    "name": "Youssef Mrabet",
    "email": "<EMAIL>"
  },
  "produits": [
    {
      "id": 1,
      "nom": "Laptop Pro X",
      "description": "Ordinateur portable haute performance",
      "prix": 1299.99,
      "pivot": {
        "commande_id": 5,
        "produit_id": 1,
        "quantite": 1,
        "prix_unitaire": 1299.99
      }
    },
    {
      "id": 2,
      "nom": "Smartphone XYZ",
      "description": "Smartphone dernière génération",
      "prix": 499.99,
      "pivot": {
        "commande_id": 5,
        "produit_id": 2,
        "quantite": 2,
        "prix_unitaire": 499.99
      }
    }
  ],
  "paiement": {
    "id": 3,
    "commande_id": 5,
    "montant": 2074.97,
    "methode": "carte",
    "statut": "complete",
    "reference": "PAY-123456789"
  },
  "client_remise": 10.5
}
```

## Calcul automatique des remises

Lors de la création d'une commande, le système calcule automatiquement le total en appliquant la remise appropriée:

1. Le sous-total est calculé: (1 × 1299.99) + (2 × 499.99) = 2299.97 €
2. La remise effective du client est déterminée: 10.5%
3. Le montant de la remise est calculé: 2299.97 × 10.5% = 241.50 €
4. Le total final est calculé: 2299.97 - 241.50 = 2058.47 €

Si une remise spécifique à la commande avait été spécifiée et était supérieure à la remise du client, elle aurait été utilisée à la place.

## Diagramme de séquence

```
┌─────────┐          ┌────────────┐          ┌──────────┐          ┌────────────┐
│  Client │          │ API Laravel │          │ Keycloak │          │ Base de    │
│         │          │             │          │          │          │ données    │
└────┬────┘          └──────┬─────┘          └────┬─────┘          └─────┬──────┘
     │                      │                      │                      │
     │ Authentification     │                      │                      │
     │─────────────────────>│                      │                      │
     │                      │ Vérification token   │                      │
     │                      │─────────────────────>│                      │
     │                      │                      │                      │
     │                      │<─────────────────────│                      │
     │                      │                      │                      │
     │ Consultation produits│                      │                      │
     │─────────────────────>│                      │                      │
     │                      │ Requête produits     │                      │
     │                      │─────────────────────────────────────────────>
     │                      │                      │                      │
     │                      │<─────────────────────────────────────────────
     │<─────────────────────│                      │                      │
     │                      │                      │                      │
     │ Création commande    │                      │                      │
     │─────────────────────>│                      │                      │
     │                      │ Calcul remise        │                      │
     │                      │─────────────────────────────────────────────>
     │                      │                      │                      │
     │                      │<─────────────────────────────────────────────
     │                      │                      │                      │
     │                      │ Enregistrement       │                      │
     │                      │─────────────────────────────────────────────>
     │                      │                      │                      │
     │                      │<─────────────────────────────────────────────
     │<─────────────────────│                      │                      │
     │                      │                      │                      │
     │ Paiement             │                      │                      │
     │─────────────────────>│                      │                      │
     │                      │ Enregistrement       │                      │
     │                      │─────────────────────────────────────────────>
     │                      │                      │                      │
     │                      │<─────────────────────────────────────────────
     │<─────────────────────│                      │                      │
     │                      │                      │                      │
```
