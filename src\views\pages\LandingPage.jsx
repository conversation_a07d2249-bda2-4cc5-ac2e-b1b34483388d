import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box, Typography, CircularProgress } from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';

const LandingPage = () => {
  const navigate = useNavigate();
  const { isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    console.log('🔐 LandingPage: isLoading:', isLoading, 'isAuthenticated:', isAuthenticated);

    // If user is authenticated, redirect to dashboard
    if (!isLoading && isAuthenticated) {
      console.log('🔐 LandingPage: User authenticated, redirecting to dashboard');
      navigate('/app/dashboard/default', { replace: true });
    } else if (!isLoading && !isAuthenticated) {
      // If user is not authenticated, redirect to login
      console.log('🔐 LandingPage: User not authenticated, redirecting to login');
      navigate('/pages/login', { replace: true });
    }
  }, [isAuthenticated, isLoading, navigate]);

  return (
    <Box display="flex" flexDirection="column" justifyContent="center" alignItems="center" minHeight="100vh" gap={2}>
      <CircularProgress size={60} />
      <Typography variant="h6" color="textSecondary">
        Loading Jihene-Line Backoffice...
      </Typography>
      <Typography variant="body2" color="textSecondary">
        Please wait while we prepare your workspace
      </Typography>
    </Box>
  );
};

export default LandingPage;
