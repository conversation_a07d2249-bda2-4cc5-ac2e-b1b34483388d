import { lazy } from 'react';

// project imports
import MainLayout from 'layout/MainLayout';
import Loadable from 'ui-component/Loadable';
import ProtectedRoute from 'components/ProtectedRoute';
import LandingPage from '../views/pages/LandingPage';
import AjoutProduit from '../views/Forms/AjoutProduit';
import Collection from '../views/Forms/Collection';
import Promotion from '../views/Forms/Promotion';
import Listpromotions from '../views/GestionCommerciale/Listpromotions';
import ListCollection from '../views/GestionCommerciale/ListCollection';
import PromotionEvent from '../views/Forms/PromotionEvent';

import CategoriesManagement from '../views/GestionCommerciale/CategoriesManagement';
import SousSousCategories from '../views/GestionCommerciale/SousSousCategories';
import AttributeManagement from '../views/GestionCommerciale/AttributeManagement';
import BrandManagement from '../views/GestionCommerciale/BrandManagement';
import ProductManagement from '../views/GestionCommerciale/ProductManagement';
import CarouselManagement from '../views/ContentManagement/CarouselManagement';

import OrderDetail from '../views/OrderManagement/OrderDetail';
import OrderStatusManagement from '../views/OrderManagement/OrderStatusManagement';
import Invoices from '../views/OrderManagement/Invoices';
import InvoicesFixed from '../views/OrderManagement/InvoicesFixed';
import OrderListEnhanced from '../views/OrderManagement/OrderListEnhanced';
import OrderListSimple from '../views/OrderManagement/OrderListSimple';
import OrderListTest from '../views/OrderManagement/OrderListTest';
import OrderListDemo from '../views/Demo/OrderListDemo';

// Client Management routing
const ClientList = Loadable(lazy(() => import('views/ClientManagement/ClientList')));
const ClientListStandardized = Loadable(lazy(() => import('views/ClientManagement/ClientListStandardized')));

// dashboard routing
const DashboardDefault = Loadable(lazy(() => import('views/dashboard/Default')));

// utilities routing
const UtilsTypography = Loadable(lazy(() => import('views/utilities/Typography')));
const UtilsColor = Loadable(lazy(() => import('views/utilities/Color')));
const UtilsShadow = Loadable(lazy(() => import('views/utilities/Shadow')));
const DesignSystemDemo = Loadable(lazy(() => import('views/utilities/DesignSystemDemo')));

// sample page routing
const SamplePage = Loadable(lazy(() => import('views/sample-page')));

// profile page routing
const UserProfile = Loadable(lazy(() => import('views/profile/UserProfile')));

// debug page routing
const AuthDebug = Loadable(lazy(() => import('views/debug/AuthDebug')));
const OrdersDebug = Loadable(lazy(() => import('views/debug/OrdersDebug')));

// ==============================|| MAIN ROUTING ||============================== //

const MainRoutes = {
  path: '/app',
  element: (
    <ProtectedRoute>
      <MainLayout />
    </ProtectedRoute>
  ),
  children: [
    {
      path: '/app',
      element: <DashboardDefault />
    },
    {
      path: 'dashboard',
      children: [
        {
          path: 'default',
          element: <DashboardDefault />
        }
      ]
    },
    {
      path: 'typography',
      element: <ProductManagement />
    },
    {
      path: 'color',
      element: <UtilsColor />
    },
    {
      path: 'shadow',
      element: <UtilsShadow />
    },
    {
      path: 'design-system-demo',
      element: <DesignSystemDemo />
    },
    {
      path: 'sample-page',
      element: <SamplePage />
    },
    {
      path: 'profile',
      element: <UserProfile />
    },
    {
      path: 'debug/auth',
      element: <AuthDebug />
    },
    {
      path: 'debug/orders',
      element: <OrdersDebug />
    },
    {
      path: 'AjoutProduit',
      element: <AjoutProduit />
    },
    {
      path: 'Collection',
      element: <Collection />
    },
    {
      path: 'Promotion',
      element: <Promotion />
    },

    {
      path: 'Lpromotions',
      element: <Listpromotions />
    },
    {
      path: 'LCollection',
      element: <ListCollection />
    },
    {
      path: 'PromotionEvent',
      element: <PromotionEvent />
    },

    {
      path: 'categories',
      element: <CategoriesManagement />
    },
    {
      path: 'sous-categories',
      element: <CategoriesManagement />
    },
    {
      path: 'sous-sous-categories',
      element: <SousSousCategories />
    },
    {
      path: 'attributes',
      element: <AttributeManagement />
    },
    {
      path: 'brands',
      element: <BrandManagement />
    },
    {
      path: 'products',
      element: <ProductManagement />
    },
    {
      path: 'carousels',
      element: <CarouselManagement />
    },

    {
      path: 'invoices',
      element: <InvoicesFixed />
    },
    {
      path: 'invoices-old',
      element: <Invoices />
    },

    {
      path: 'orders',
      element: <OrderListSimple />
    },
    {
      path: 'orders-enhanced',
      element: <OrderListEnhanced />
    },
    {
      path: 'orders-test',
      element: <OrderListTest />
    },
    {
      path: 'orders/:id',
      element: <OrderDetail />
    },
    {
      path: 'orders-demo',
      element: <OrderListDemo />
    },
    {
      path: 'order-statuses',
      element: <OrderStatusManagement />
    },

    {
      path: 'clients',
      children: [
        {
          path: 'list',
          element: <ClientList />
        },
        {
          path: 'list-standardized',
          element: <ClientListStandardized />
        }
      ]
    }
  ]
};

export default MainRoutes;
