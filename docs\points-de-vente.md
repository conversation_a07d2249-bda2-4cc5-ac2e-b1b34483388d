# Gestion des Points de Vente

## Introduction

Le système permet de gérer des points de vente physiques. Un point de vente est une entité qui peut avoir plusieurs clients associés et qui bénéficie d'une remise spécifique.

## Endpoints API

### Récupérer tous les points de vente

```
GET /api/points-de-vente
```

Retourne la liste de tous les points de vente avec le nombre de clients associés.

#### Exemple de réponse

```json
[
  {
    "id": 1,
    "nom": "Boutique Centre Ville",
    "adresse": "123 Rue Principale",
    "telephone": "71234567",
    "email": "<EMAIL>",
    "remise": 8.50,
    "description": "Point de vente principal",
    "statut": "actif",
    "created_at": "2025-04-01T09:00:00.000000Z",
    "updated_at": "2025-04-01T09:00:00.000000Z",
    "users_count": 3
  },
  {
    "id": 2,
    "nom": "Boutique Lac",
    "adresse": "45 Avenue du Lac",
    "telephone": "71456789",
    "email": "<EMAIL>",
    "remise": 7.00,
    "description": "Point de vente secondaire",
    "statut": "actif",
    "created_at": "2025-04-01T10:30:00.000000Z",
    "updated_at": "2025-04-01T10:30:00.000000Z",
    "users_count": 2
  }
]
```

### Récupérer un point de vente spécifique

```
GET /api/points-de-vente/{id}
```

Retourne les détails d'un point de vente spécifique avec la liste des clients associés.

#### Exemple de réponse

```json
{
  "id": 1,
  "nom": "Boutique Centre Ville",
  "adresse": "123 Rue Principale",
  "telephone": "71234567",
  "email": "<EMAIL>",
  "remise": 8.50,
  "description": "Point de vente principal",
  "statut": "actif",
  "created_at": "2025-04-01T09:00:00.000000Z",
  "updated_at": "2025-04-01T09:00:00.000000Z",
  "users": [
    {
      "id": 4,
      "name": "Ahmed Ben Ali",
      "email": "<EMAIL>",
      "roles": ["client"],
      "type_client": "point_de_vente"
    },
    {
      "id": 5,
      "name": "Fatima Mansour",
      "email": "<EMAIL>",
      "roles": ["client"],
      "type_client": "point_de_vente"
    },
    {
      "id": 6,
      "name": "Mohamed Karim",
      "email": "<EMAIL>",
      "roles": ["client"],
      "type_client": "point_de_vente"
    }
  ]
}
```

### Créer un nouveau point de vente

```
POST /api/points-de-vente
```

Crée un nouveau point de vente.

#### Paramètres de la requête

| Paramètre   | Type    | Description                                |
|-------------|---------|--------------------------------------------|
| nom         | string  | Nom du point de vente                      |
| adresse     | string  | Adresse physique                           |
| telephone   | string  | Numéro de téléphone                        |
| email       | string  | Adresse email                              |
| remise      | decimal | Pourcentage de remise (0-100)              |
| description | string  | Description du point de vente (optionnel)  |
| statut      | string  | Statut du point de vente (actif/inactif)   |

#### Exemple de requête

```json
{
  "nom": "Boutique Sousse",
  "adresse": "78 Avenue de la Plage",
  "telephone": "73123456",
  "email": "<EMAIL>",
  "remise": 6.50,
  "description": "Point de vente à Sousse",
  "statut": "actif"
}
```

#### Exemple de réponse

```json
{
  "id": 3,
  "nom": "Boutique Sousse",
  "adresse": "78 Avenue de la Plage",
  "telephone": "73123456",
  "email": "<EMAIL>",
  "remise": 6.50,
  "description": "Point de vente à Sousse",
  "statut": "actif",
  "created_at": "2025-04-04T15:30:00.000000Z",
  "updated_at": "2025-04-04T15:30:00.000000Z"
}
```

### Mettre à jour un point de vente

```
PUT /api/points-de-vente/{id}
```

Met à jour les informations d'un point de vente existant.

#### Paramètres de la requête

Mêmes paramètres que pour la création, tous optionnels.

#### Exemple de requête

```json
{
  "remise": 9.00,
  "description": "Point de vente principal mis à jour"
}
```

### Supprimer un point de vente

```
DELETE /api/points-de-vente/{id}
```

Supprime un point de vente existant. Cette action met à jour tous les clients associés en les définissant comme clients normaux.

### Ajouter un client à un point de vente

```
POST /api/points-de-vente/{id}/clients
```

Associe un client existant à un point de vente.

#### Paramètres de la requête

| Paramètre | Type    | Description                                |
|-----------|---------|--------------------------------------------|
| user_id   | integer | ID de l'utilisateur à associer             |

#### Exemple de requête

```json
{
  "user_id": 7
}
```

#### Exemple de réponse

```json
{
  "message": "Utilisateur ajouté au point de vente avec succès",
  "user": {
    "id": 7,
    "name": "Sami Trabelsi",
    "email": "<EMAIL>",
    "roles": ["client"],
    "point_de_vente_id": 1,
    "type_client": "point_de_vente"
  }
}
```

### Retirer un client d'un point de vente

```
DELETE /api/points-de-vente/{id}/clients/{userId}
```

Retire l'association entre un client et un point de vente.

#### Exemple de réponse

```json
{
  "message": "Utilisateur retiré du point de vente avec succès"
}
```

## Logique de remise

Lorsqu'un utilisateur est associé à un point de vente:

1. Son type_client est défini sur "point_de_vente"
2. Son point_de_vente_id est défini sur l'ID du point de vente
3. Le rôle "client" est ajouté à ses rôles s'il ne l'a pas déjà

Lors du calcul de la remise effective pour un client:

1. Si le client a une remise personnelle, celle-ci est utilisée en priorité
2. Sinon, si le client est de type "point_de_vente", la remise définie pour le point de vente est utilisée

Cette remise est ensuite appliquée automatiquement lors de la création de commandes par ce client, sauf si une remise spécifique à la commande est définie et est plus avantageuse.

## Changement de type de client

Pour associer un client à un point de vente, vous pouvez utiliser l'endpoint suivant:

```
PUT /api/clients/{id}/type
```

#### Exemple de requête

```json
{
  "type_client": "point_de_vente",
  "point_de_vente_id": 1
}
```

Cette action associera le client au point de vente spécifié et mettra à jour son type.
