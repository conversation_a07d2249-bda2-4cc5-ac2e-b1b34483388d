<!doctype html>
<html lang="en">
  <head>
    <title>jihenLine - Backoffice</title>
    <link rel="icon" href="./src/assets/images/31e8b10e-8c3b-4de2-b71f-67a1ec1c95f3.jpg" />
    <!-- Meta Tags-->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#2296f3" />
    <meta name="title" content="jihenLine - Backoffice Administration" />
    <meta
      name="description"
      content="jihenLine Backoffice - Administration panel for managing products, orders, and clients."
    />
    <meta
      name="keywords"
      content="react admin template, material-ui react dashboard template, reactjs admin template, reactjs dashboard, react backend template"
    />
    <meta name="author" content="JihenLine" />
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
          This HTML file is a template.
          If you open it directly in the browser, you will see an empty page.

          You can add webfonts, meta tags, or analytics to this file.
          The build step will place the bundled scripts into the <body> tag.

          To begin the development, run `npm start` or `yarn start`.
          To create a production bundle, use `npm run build` or `yarn build`.
        -->
    <script type="module" src="/src/index.jsx"></script>
  </body>
</html>
