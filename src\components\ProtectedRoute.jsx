import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Box, CircularProgress, Typography } from '@mui/material';

// Loading component
const LoadingScreen = () => (
  <Box
    display="flex"
    flexDirection="column"
    justifyContent="center"
    alignItems="center"
    minHeight="100vh"
    gap={2}
  >
    <CircularProgress size={60} />
    <Typography variant="h6" color="textSecondary">
      Loading...
    </Typography>
  </Box>
);

// Protected Route component
const ProtectedRoute = ({ children, requiredRoles = [], requireAuth = true }) => {
  const { isAuthenticated, isLoading, hasAnyRole } = useAuth();
  const location = useLocation();

  // Show loading screen while checking authentication
  if (isLoading) {
    return <LoadingScreen />;
  }

  // If authentication is required but user is not authenticated
  if (requireAuth && !isAuthenticated) {
    // Redirect to login page with return URL
    return (
      <Navigate 
        to="/pages/login" 
        state={{ from: location.pathname }} 
        replace 
      />
    );
  }

  // If specific roles are required, check if user has any of them
  if (requiredRoles.length > 0 && !hasAnyRole(requiredRoles)) {
    // User doesn't have required roles - show unauthorized page or redirect
    return (
      <Box
        display="flex"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
        gap={2}
        p={3}
      >
        <Typography variant="h4" color="error" gutterBottom>
          Access Denied
        </Typography>
        <Typography variant="body1" color="textSecondary" textAlign="center">
          You don't have the required permissions to access this page.
        </Typography>
        <Typography variant="body2" color="textSecondary">
          Required roles: {requiredRoles.join(', ')}
        </Typography>
      </Box>
    );
  }

  // User is authenticated and has required roles
  return children;
};

export default ProtectedRoute;
