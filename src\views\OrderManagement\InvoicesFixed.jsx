import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  Tooltip,
  Button,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  Print as PrintIcon,
  Receipt as ReceiptIcon,
  Person as PersonIcon,
  Payment as PaymentIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

// Standardized components
import StandardTable from '../../components/StandardTable';
import StandardCard from '../../components/StandardCard';
import MainCard from '../../ui-component/cards/MainCard';
import ProfessionalModal from '../../ui-component/extended/ProfessionalModal';

// Design system
import { COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS } from '../../themes/designSystem';

const API_URL = import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';

const InvoicesFixed = () => {
  // States
  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(15);
  const [showInvoiceModal, setShowInvoiceModal] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [totalInvoices, setTotalInvoices] = useState(0);

  // Load invoices on component mount
  useEffect(() => {
    loadInvoices();
  }, []);

  const loadInvoices = async () => {
    try {
      setLoading(true);
      setError('');

      // Fetch payments which represent our invoices
      const paymentsResponse = await fetch(`${API_URL}/paiements`);
      if (!paymentsResponse.ok) {
        throw new Error('Erreur lors du chargement des paiements');
      }

      const paymentsData = await paymentsResponse.json();
      const payments = Array.isArray(paymentsData) ? paymentsData : paymentsData.data || [];

      console.log('💳 Payments API response:', paymentsData);

      // For each payment, fetch the related order to get complete invoice data
      const invoicesWithOrders = await Promise.all(
        payments.map(async (payment) => {
          try {
            const orderResponse = await fetch(`${API_URL}/commandes/${payment.commande_id}?with=user,client,produits`);

            if (orderResponse.ok) {
              const orderResponseData = await orderResponse.json();
              const orderData = orderResponseData.data || orderResponseData;

              const finalPaymentMethod = orderData.methode_paiement || null;

              return {
                ...payment,
                order: orderData,
                invoice_number: `FAC-${payment.id.toString().padStart(4, '0')}`,
                invoice_date: payment.created_at,
                amount: payment.montant,
                status: payment.status || 'completed',
                methode_paiement: finalPaymentMethod
              };
            }

            return {
              ...payment,
              order: null,
              methode_paiement: null,
              invoice_number: `FAC-${payment.id.toString().padStart(4, '0')}`,
              invoice_date: payment.created_at,
              amount: payment.montant,
              status: payment.status || 'completed'
            };
          } catch (err) {
            console.error(`❌ Error fetching order ${payment.commande_id}:`, err);
            return {
              ...payment,
              order: null,
              methode_paiement: null,
              invoice_number: `FAC-${payment.id.toString().padStart(4, '0')}`,
              invoice_date: payment.created_at,
              amount: payment.montant,
              status: payment.status || 'completed'
            };
          }
        })
      );

      setInvoices(invoicesWithOrders);
      setTotalInvoices(invoicesWithOrders.length);
    } catch (err) {
      console.error('Error loading invoices:', err);
      setError('Erreur lors du chargement des factures: ' + err.message);
      setInvoices([]);
      setTotalInvoices(0);
    } finally {
      setLoading(false);
    }
  };

  // Filter invoices based on search term and status
  const filteredInvoices = invoices.filter((invoice) => {
    const matchesSearch =
      invoice.invoice_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.order?.user?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.order?.nom_client?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.order?.email_commande?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.transaction_id?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = !statusFilter || invoice.status?.toLowerCase() === statusFilter.toLowerCase();

    return matchesSearch && matchesStatus;
  });

  // Pagination logic
  const totalPages = Math.ceil(filteredInvoices.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentInvoices = filteredInvoices.slice(startIndex, endIndex);

  // Reset to first page when search term or status filter changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, statusFilter]);

  // Enhanced handlers
  const handleSearch = (event) => {
    setSearchTerm(event.target.value);
  };

  const handleStatusFilter = (event) => {
    setStatusFilter(event.target.value);
  };

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const handleRefresh = () => {
    loadInvoices();
  };

  // Table columns configuration
  const columns = [
    { id: 'invoice_number', label: 'N° Facture', minWidth: 120 },
    { id: 'invoice_date', label: 'Date', minWidth: 150 },
    { id: 'client_name', label: 'Client', minWidth: 180 },
    { id: 'commande_id', label: 'Commande', minWidth: 100 },
    { id: 'amount', label: 'Montant', minWidth: 120 },
    { id: 'status', label: 'Statut', minWidth: 100 },
    { id: 'actions', label: 'Actions', minWidth: 120 }
  ];

  // Custom cell renderer
  const renderCell = (column, row, value) => {
    switch (column.id) {
      case 'invoice_number':
        return (
          <Box>
            <Typography
              variant="body2"
              sx={{
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                fontWeight: TYPOGRAPHY.fontWeight.semibold,
                color: COLORS.primary.main
              }}
            >
              {row.invoice_number}
            </Typography>
            {row.transaction_id && (
              <Typography variant="caption" sx={{ color: COLORS.text.secondary }}>
                ID: {row.transaction_id}
              </Typography>
            )}
          </Box>
        );
      case 'invoice_date':
        return (
          <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary }}>
            {formatDate(row.invoice_date)}
          </Typography>
        );
      case 'client_name':
        return (
          <Box>
            <Typography
              variant="body2"
              sx={{
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                fontWeight: TYPOGRAPHY.fontWeight.medium,
                display: 'flex',
                alignItems: 'center',
                gap: 0.5
              }}
            >
              <PersonIcon size={16} />
              {getClientName(row)}
            </Typography>
            {(row.order?.email_commande || row.order?.user?.email) && (
              <Typography variant="caption" sx={{ color: COLORS.text.secondary }}>
                {row.order.email_commande || row.order.user?.email}
              </Typography>
            )}
          </Box>
        );
      case 'commande_id':
        return (
          <Chip
            label={`CMD-${row.commande_id}`}
            size="small"
            sx={{
              backgroundColor: COLORS.primary.light,
              color: COLORS.primary.main,
              fontFamily: TYPOGRAPHY.fontFamily.primary
            }}
          />
        );
      case 'amount':
        return (
          <Typography
            variant="body2"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontWeight: TYPOGRAPHY.fontWeight.semibold,
              color: COLORS.success.main
            }}
          >
            {formatCurrency(row.amount)}
          </Typography>
        );
      case 'status':
        return (
          <Chip
            label={getStatusText(row.status)}
            size="small"
            sx={{
              backgroundColor: getStatusColorValue(row.status),
              color: 'white',
              fontFamily: TYPOGRAPHY.fontFamily.primary
            }}
          />
        );
      case 'actions':
        return (
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Voir la facture">
              <IconButton
                size="small"
                onClick={() => handleViewInvoice(row)}
                sx={{
                  color: COLORS.primary.main,
                  '&:hover': {
                    backgroundColor: COLORS.primary.light,
                    transform: 'scale(1.1)'
                  }
                }}
              >
                <VisibilityIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Imprimer">
              <IconButton
                size="small"
                onClick={() => handlePrintInvoice(row)}
                sx={{
                  color: COLORS.secondary.main,
                  '&:hover': {
                    backgroundColor: COLORS.secondary.light,
                    transform: 'scale(1.1)'
                  }
                }}
              >
                <PrintIcon />
              </IconButton>
            </Tooltip>
          </Box>
        );
      default:
        return value || 'N/A';
    }
  };

  // Handle view invoice details
  const handleViewInvoice = async (invoice) => {
    setSelectedInvoice(invoice);
    setShowInvoiceModal(true);
  };

  // Handle print invoice
  const handlePrintInvoice = (invoice) => {
    console.log('📄 handlePrintInvoice called with:', invoice);

    // Create professional invoice HTML content
    const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Facture ${invoice.invoice_number || `FAC-${invoice.id}`} - JihenLine</title>
        <meta charset="utf-8">
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: white;
            padding: 20px;
            margin: 0;
          }

          .print-container {
            max-width: 800px;
            margin: 0 auto;
          }

          .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            border-bottom: 3px solid #1976d2;
            padding-bottom: 20px;
          }

          .company-info h1 {
            font-size: 28px;
            color: #1976d2;
            margin-bottom: 5px;
            font-weight: bold;
          }

          .company-info p {
            margin: 2px 0;
            color: #666;
          }

          .document-info {
            text-align: right;
          }

          .document-info h2 {
            font-size: 24px;
            color: #1976d2;
            margin-bottom: 10px;
          }

          .client-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
          }

          .client-info h3 {
            color: #1976d2;
            margin-bottom: 10px;
            font-size: 16px;
          }

          .payment-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
          }

          .payment-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
          }

          .payment-section h4 {
            color: #1976d2;
            margin-bottom: 10px;
            font-size: 14px;
          }

          .amount-summary {
            background-color: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
          }

          .amount-summary .total-amount {
            font-size: 24px;
            font-weight: bold;
            color: #1976d2;
            margin: 10px 0;
          }

          .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
            background-color: #4caf50;
            color: white;
          }

          .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            text-align: center;
            color: #666;
            font-size: 11px;
          }

          @media print {
            body {
              -webkit-print-color-adjust: exact;
              print-color-adjust: exact;
              padding: 0;
            }

            .print-container {
              max-width: none;
              margin: 0;
            }

            .header {
              page-break-after: avoid;
            }

            .footer {
              page-break-before: avoid;
            }
          }
        </style>
      </head>
      <body>
        <div class="print-container">
          <!-- Header -->
          <div class="header">
            <div class="company-info">
              <h1>JihenLine</h1>
              <p>Plateforme E-commerce</p>
              <p>Tunis, Tunisie</p>
              <p>Email: <EMAIL></p>
              <p>Tél: +216 XX XXX XXX</p>
            </div>
            <div class="document-info">
              <h2>FACTURE</h2>
              <p><strong>N°:</strong> ${invoice.invoice_number || `FAC-${invoice.id}`}</p>
              <p><strong>Date:</strong> ${formatDate(invoice.invoice_date || invoice.created_at)}</p>
              <p><strong>Statut:</strong> <span class="status-badge">${getStatusText(invoice.status)}</span></p>
            </div>
          </div>

          <!-- Client Information -->
          <div class="client-info">
            <h3>Informations Client</h3>
            <p><strong>Client:</strong> ${getClientName(invoice)}</p>
            <p><strong>Email:</strong> ${invoice.order?.user?.email || invoice.order?.email_commande || 'Non spécifié'}</p>
            <p><strong>Commande:</strong> CMD-${invoice.commande_id}</p>
            ${invoice.transaction_id ? `<p><strong>Transaction ID:</strong> ${invoice.transaction_id}</p>` : ''}
          </div>

          <!-- Payment Information -->
          <div class="payment-info">
            <div class="payment-section">
              <h4>Détails du Paiement</h4>
              <p><strong>Date de paiement:</strong> ${formatDate(invoice.invoice_date || invoice.created_at)}</p>
              <p><strong>Méthode:</strong> ${invoice.methode_paiement || 'Non spécifiée'}</p>
              <p><strong>Référence:</strong> ${invoice.transaction_id || 'N/A'}</p>
            </div>
            <div class="payment-section">
              <h4>Informations Commande</h4>
              <p><strong>N° Commande:</strong> CMD-${invoice.commande_id}</p>
              <p><strong>Date commande:</strong> ${formatDate(invoice.order?.created_at || invoice.created_at)}</p>
              <p><strong>Statut:</strong> ${getStatusText(invoice.status)}</p>
            </div>
          </div>

          <!-- Amount Summary -->
          <div class="amount-summary">
            <h3>Montant Total</h3>
            <div class="total-amount">${formatCurrency(invoice.amount)}</div>
            <p>Toutes taxes comprises</p>
          </div>

          <!-- Footer -->
          <div class="footer">
            <p>Merci pour votre confiance - JihenLine</p>
            <p>Facture générée le ${formatDate(new Date().toISOString())}</p>
            <p>TVA: TN123456789 - RC: B123456789</p>
          </div>
        </div>
      </body>
      </html>
    `;

    // Open new window and print
    const printWindow = window.open('', '_blank');
    console.log('🖨️ Print window opened:', !!printWindow);

    if (printWindow) {
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.focus();
      console.log('🖨️ Content written to print window, starting print...');

      setTimeout(() => {
        printWindow.print();
        printWindow.close();
        console.log('🖨️ Print completed and window closed');
      }, 500);
    } else {
      console.error('❌ Failed to open print window - popup blocked?');
      alert("Impossible d'ouvrir la fenêtre d'impression. Veuillez autoriser les popups pour ce site.");
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Format currency - Using TND (Tunisian Dinar)
  const formatCurrency = (amount) => {
    if (!amount) return '0.000 DT';
    return `${parseFloat(amount).toFixed(3)} DT`;
  };

  // Get client name with improved fallback logic
  const getClientName = (invoice) => {
    const order = invoice.order;
    if (!order) return 'Client inconnu';

    if (order.user?.name) return order.user.name;
    if (order.client?.nom) return order.client.nom;
    if (order.nom_client) return order.nom_client;
    if (order.prenom_client && order.nom_client) {
      return `${order.prenom_client} ${order.nom_client}`;
    }
    if (order.email_commande) {
      return order.email_commande.split('@')[0];
    }

    return 'Client inconnu';
  };

  // Get status color (for Bootstrap badge classes)
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'complete':
        return 'success';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'danger';
      default:
        return 'secondary';
    }
  };

  // Get status color value (for backgroundColor)
  const getStatusColorValue = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'complete':
        return COLORS.success.main;
      case 'pending':
        return COLORS.warning.main;
      case 'failed':
        return COLORS.error.main;
      default:
        return COLORS.grey[500];
    }
  };

  // Get status text
  const getStatusText = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'complete':
        return 'Payée';
      case 'pending':
        return 'En attente';
      case 'failed':
        return 'Échec';
      default:
        return 'Inconnue';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Chargement des factures...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button variant="contained" onClick={loadInvoices}>
          Réessayer
        </Button>
      </Box>
    );
  }

  return (
    <MainCard title="Gestion des Factures - Enhanced">
      <Box sx={{ width: '100%' }}>
        {/* Responsive Filters and Search */}
        <StandardCard title="Recherche et Filtres" size="small" sx={{ mb: 3 }} className="responsive-card">
          <Box
            sx={{
              display: 'flex',
              gap: 2,
              flexWrap: 'wrap',
              alignItems: 'center',
              flexDirection: { xs: 'column', sm: 'row' }
            }}
            className="responsive-form"
          >
            <TextField
              placeholder="Rechercher par numéro, client, email, transaction..."
              value={searchTerm}
              onChange={handleSearch}
              sx={{
                flex: 1,
                minWidth: { xs: '100%', sm: 250 },
                width: { xs: '100%', sm: 'auto' }
              }}
              size="small"
              slotProps={{
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon size={20} color={COLORS.grey[500]} />
                    </InputAdornment>
                  )
                }
              }}
            />

            <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }} size="small">
              <InputLabel>Statut</InputLabel>
              <Select value={statusFilter} onChange={handleStatusFilter} label="Statut">
                <MenuItem value="">Tous</MenuItem>
                <MenuItem value="completed">Payée</MenuItem>
                <MenuItem value="pending">En attente</MenuItem>
                <MenuItem value="failed">Échec</MenuItem>
              </Select>
            </FormControl>

            <Button
              variant="outlined"
              onClick={handleRefresh}
              disabled={loading}
              startIcon={<RefreshIcon />}
              sx={{
                width: { xs: '100%', sm: 'auto' },
                minWidth: { xs: '100%', sm: 'auto' }
              }}
              size="small"
            >
              Actualiser
            </Button>
          </Box>
        </StandardCard>

        {/* Results Summary */}
        <Box sx={{ mb: 2 }}>
          <Typography
            variant="h5"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontSize: TYPOGRAPHY.fontSize.lg,
              fontWeight: TYPOGRAPHY.fontWeight.semibold,
              color: COLORS.text.dark
            }}
          >
            Toutes les Factures ({totalInvoices})
          </Typography>
          {(searchTerm || statusFilter) && (
            <Typography
              variant="body2"
              sx={{
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                color: COLORS.text.secondary,
                mt: 0.5
              }}
            >
              Résultats filtrés - Page {currentPage} sur {totalPages}
            </Typography>
          )}
        </Box>

        {/* Enhanced Table */}
        <StandardTable
          columns={columns}
          data={currentInvoices}
          loading={loading}
          error={error}
          emptyMessage={searchTerm ? 'Aucune facture ne correspond à votre recherche.' : 'Aucune facture disponible pour le moment.'}
          renderCell={renderCell}
          hover={true}
          pagination={{
            page: currentPage,
            totalPages: totalPages,
            onPageChange: handlePageChange
          }}
        />

        {/* Additional Info */}
        {!loading && !error && filteredInvoices.length > 0 && (
          <Box sx={{ mt: 2 }}>
            <Typography
              variant="body2"
              sx={{
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                color: COLORS.text.secondary,
                textAlign: 'center'
              }}
            >
              Affichage de {(currentPage - 1) * itemsPerPage + 1} à {Math.min(currentPage * itemsPerPage, filteredInvoices.length)} sur{' '}
              {filteredInvoices.length} facture(s)
            </Typography>
          </Box>
        )}
      </Box>

      {/* Professional Invoice Details Modal */}
      <ProfessionalModal
        show={showInvoiceModal}
        onHide={() => setShowInvoiceModal(false)}
        title={selectedInvoice ? `Facture ${selectedInvoice.invoice_number}` : 'Détails de la facture'}
        icon={<ReceiptIcon />}
        size="lg"
        variant="primary"
        primaryAction={() => handlePrintInvoice(selectedInvoice)}
        secondaryAction={() => setShowInvoiceModal(false)}
        primaryText="Imprimer"
        secondaryText="Fermer"
        primaryVariant="primary"
        secondaryVariant="secondary"
      >
        {selectedInvoice && (
          <Box>
            {/* Responsive Client Information Section */}
            <Box
              sx={{
                display: 'grid',
                gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' },
                gap: { xs: SPACING.md, md: SPACING.lg },
                mb: { xs: SPACING.md, md: SPACING.lg }
              }}
              className="responsive-grid-2"
            >
              <StandardCard
                title={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: SPACING.xs, flexWrap: 'wrap' }}>
                    <PersonIcon sx={{ color: COLORS.primary.main, fontSize: { xs: '1.2rem', md: '1.5rem' } }} />
                    <Typography
                      variant="h6"
                      sx={{
                        fontFamily: TYPOGRAPHY.fontFamily.primary,
                        fontWeight: TYPOGRAPHY.fontWeight.semibold,
                        color: COLORS.primary.main,
                        fontSize: { xs: TYPOGRAPHY.fontSize.base, md: TYPOGRAPHY.fontSize.lg }
                      }}
                    >
                      Informations Client
                    </Typography>
                  </Box>
                }
                size="small"
                className="responsive-card"
              >
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: SPACING.xs }}>
                  <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary }}>
                    <strong>Client:</strong> {getClientName(selectedInvoice)}
                  </Typography>
                  <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary }}>
                    <strong>Email:</strong> {selectedInvoice.order?.user?.email || selectedInvoice.order?.email_commande || 'Non spécifié'}
                  </Typography>
                  <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary }}>
                    <strong>Commande:</strong> CMD-{selectedInvoice.commande_id}
                  </Typography>
                  {selectedInvoice.transaction_id && (
                    <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary }}>
                      <strong>Transaction ID:</strong> {selectedInvoice.transaction_id}
                    </Typography>
                  )}
                </Box>
              </StandardCard>

              <StandardCard
                title={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: SPACING.xs }}>
                    <PaymentIcon sx={{ color: COLORS.primary.main }} />
                    <Typography
                      variant="h6"
                      sx={{
                        fontFamily: TYPOGRAPHY.fontFamily.primary,
                        fontWeight: TYPOGRAPHY.fontWeight.semibold,
                        color: COLORS.primary.main
                      }}
                    >
                      Informations Paiement
                    </Typography>
                  </Box>
                }
                size="small"
              >
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: SPACING.xs }}>
                  <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary }}>
                    <strong>Date:</strong> {formatDate(selectedInvoice.invoice_date)}
                  </Typography>
                  <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary }}>
                    <strong>Montant:</strong>{' '}
                    <Typography
                      component="span"
                      sx={{
                        color: COLORS.success.main,
                        fontWeight: TYPOGRAPHY.fontWeight.bold,
                        fontFamily: TYPOGRAPHY.fontFamily.primary
                      }}
                    >
                      {formatCurrency(selectedInvoice.amount)}
                    </Typography>
                  </Typography>
                  <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary }}>
                    <strong>Méthode:</strong> {selectedInvoice.methode_paiement || 'Non spécifiée'}
                  </Typography>
                  <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary }}>
                    <strong>Statut:</strong>{' '}
                    <Chip
                      label={getStatusText(selectedInvoice.status)}
                      size="small"
                      sx={{
                        backgroundColor: getStatusColorValue(selectedInvoice.status),
                        color: '#ffffff',
                        fontFamily: TYPOGRAPHY.fontFamily.primary
                      }}
                    />
                  </Typography>
                </Box>
              </StandardCard>
            </Box>

            {/* Order Information */}
            {selectedInvoice.order && (
              <StandardCard
                title={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: SPACING.xs }}>
                    <ReceiptIcon sx={{ color: COLORS.primary.main }} />
                    <Typography
                      variant="h6"
                      sx={{
                        fontFamily: TYPOGRAPHY.fontFamily.primary,
                        fontWeight: TYPOGRAPHY.fontWeight.semibold,
                        color: COLORS.primary.main
                      }}
                    >
                      Détails de la Commande
                    </Typography>
                  </Box>
                }
                size="small"
                sx={{ mb: SPACING.lg }}
              >
                <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: SPACING.lg }}>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: SPACING.xs }}>
                    <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary }}>
                      <strong>N° Commande:</strong> CMD-{selectedInvoice.commande_id}
                    </Typography>
                    <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary }}>
                      <strong>Date commande:</strong> {formatDate(selectedInvoice.order.created_at)}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: SPACING.xs }}>
                    <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary }}>
                      <strong>Statut commande:</strong> {selectedInvoice.order.status || 'Non spécifié'}
                    </Typography>
                    <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary }}>
                      <strong>Total commande:</strong> {formatCurrency(selectedInvoice.order.total || selectedInvoice.amount)}
                    </Typography>
                  </Box>
                </Box>
              </StandardCard>
            )}

            {/* Summary */}
            <Box
              sx={{
                backgroundColor: COLORS.info.light,
                border: `1px solid ${COLORS.info.main}`,
                borderRadius: BORDER_RADIUS.md,
                padding: SPACING.lg
              }}
            >
              <Typography
                variant="h6"
                sx={{
                  fontFamily: TYPOGRAPHY.fontFamily.primary,
                  fontWeight: TYPOGRAPHY.fontWeight.semibold,
                  color: COLORS.info.main,
                  mb: SPACING.sm
                }}
              >
                Résumé de la facture
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  fontFamily: TYPOGRAPHY.fontFamily.primary,
                  color: COLORS.text.dark,
                  margin: 0
                }}
              >
                Cette facture correspond au paiement de la commande CMD-{selectedInvoice.commande_id}
                d'un montant de{' '}
                <Typography
                  component="span"
                  sx={{
                    fontWeight: TYPOGRAPHY.fontWeight.bold,
                    fontFamily: TYPOGRAPHY.fontFamily.primary
                  }}
                >
                  {formatCurrency(selectedInvoice.amount)}
                </Typography>{' '}
                effectué le {formatDate(selectedInvoice.invoice_date)}.
              </Typography>
            </Box>
          </Box>
        )}
      </ProfessionalModal>
    </MainCard>
  );
};

export default InvoicesFixed;
