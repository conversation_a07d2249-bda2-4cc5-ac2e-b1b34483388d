import PropTypes from 'prop-types';
import React from 'react';

// material-ui
import { useTheme } from '@mui/material/styles';
import Grid from '@mui/material/Grid2';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Divider from '@mui/material/Divider';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';

// project imports
import MainCard from 'ui-component/cards/MainCard';
import SkeletonTotalGrowthBarChart from 'ui-component/cards/Skeleton/TotalGrowthBarChart';
import { gridSpacing } from 'store/constant';

export default function SalesMetricsCard({ isLoading = false, data = [], error = null }) {
  const theme = useTheme();

  // Calculate metrics from data
  const calculateMetrics = () => {
    console.log('🔍 SalesMetricsCard - Calculating metrics from data:', data);

    if (!data || !Array.isArray(data) || data.length === 0) {
      console.log('⚠️ SalesMetricsCard - No data available for metrics calculation');
      return {
        totalSales: 0,
        totalOrders: 0,
        averageOrderValue: 0,
        currentMonth: { sales: 0, orders: 0 },
        previousMonth: { sales: 0, orders: 0 },
        growth: 0
      };
    }

    const totalSales = data.reduce((sum, item) => sum + (Number(item.sales) || 0), 0);
    const totalOrders = data.reduce((sum, item) => sum + (Number(item.orderCount) || 0), 0);
    const averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0;

    console.log('📊 SalesMetricsCard - Calculated totals:', {
      totalSales,
      totalOrders,
      averageOrderValue,
      monthsCount: data.length
    });

    // Find the most recent month with actual sales data for better growth calculation
    const monthsWithSales = data.filter((month) => Number(month.sales) > 0);

    let currentMonth, previousMonth;
    let currentSales = 0,
      previousSales = 0;
    let growth = 0;
    let growthStatus = 'no-data';

    console.log(
      '📈 SalesMetricsCard - Months with sales:',
      monthsWithSales.map((m) => ({
        month: m.month,
        sales: m.sales,
        orders: m.orderCount
      }))
    );

    if (monthsWithSales.length >= 2) {
      // Compare the two most recent months with sales
      currentMonth = monthsWithSales[monthsWithSales.length - 1];
      previousMonth = monthsWithSales[monthsWithSales.length - 2];
      currentSales = Number(currentMonth.sales) || 0;
      previousSales = Number(previousMonth.sales) || 0;

      growth = ((currentSales - previousSales) / previousSales) * 100;
      growthStatus = 'calculated';
    } else if (monthsWithSales.length === 1) {
      // Only one month has sales - show as "new sales"
      currentMonth = monthsWithSales[0];
      previousMonth = { month: 'Aucun', sales: 0, orderCount: 0 };
      currentSales = Number(currentMonth.sales) || 0;
      previousSales = 0;

      growth = 100; // Show as positive growth since it's new
      growthStatus = 'new-sales';
    } else {
      // No months with sales - use the last two months from the array
      currentMonth = data[data.length - 1] || { month: 'Actuel', sales: 0, orderCount: 0 };
      previousMonth = data[data.length - 2] || { month: 'Précédent', sales: 0, orderCount: 0 };
      currentSales = 0;
      previousSales = 0;

      growth = 0;
      growthStatus = 'no-sales';
    }

    console.log('📈 SalesMetricsCard - Growth calculation:', {
      currentMonth: currentMonth.month,
      currentSales,
      previousMonth: previousMonth.month,
      previousSales,
      growth: growth.toFixed(1) + '%',
      growthStatus,
      monthsWithSalesCount: monthsWithSales.length
    });

    return {
      totalSales,
      totalOrders,
      averageOrderValue,
      currentMonth: { sales: currentSales, orders: Number(currentMonth.orderCount) || 0 },
      previousMonth: { sales: previousSales, orders: Number(previousMonth.orderCount) || 0 },
      growth,
      growthStatus
    };
  };

  const metrics = calculateMetrics();

  if (error) {
    return (
      <MainCard>
        <Grid container spacing={gridSpacing}>
          <Grid size={12}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: '300px',
                backgroundColor: 'error.lighter',
                borderRadius: 2,
                border: 2,
                borderColor: 'error.main'
              }}
            >
              <Typography variant="h6" sx={{ color: 'error.main', mb: 1 }}>
                ❌ Erreur de chargement
              </Typography>
              <Typography variant="body2" sx={{ color: 'error.dark', textAlign: 'center' }}>
                Impossible de charger les données de ventes: {error}
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </MainCard>
    );
  }

  if (isLoading) {
    return <SkeletonTotalGrowthBarChart />;
  }

  return (
    <MainCard>
      <Grid container spacing={gridSpacing}>
        <Grid size={12}>
          <Typography variant="subtitle2" sx={{ mb: 2 }}>
            Métriques des Ventes
          </Typography>
        </Grid>

        {/* Main metrics row */}
        <Grid size={12}>
          <Grid container spacing={2}>
            {/* Total Sales */}
            <Grid size={{ xs: 12, sm: 4 }}>
              <Card sx={{ bgcolor: 'primary.lighter', border: 1, borderColor: 'primary.light' }}>
                <CardContent sx={{ p: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <AttachMoneyIcon sx={{ color: 'primary.main', mr: 1 }} />
                    <Typography variant="body2" color="primary.main">
                      Ventes Totales
                    </Typography>
                  </Box>
                  <Typography variant="h4" sx={{ color: 'primary.dark' }}>
                    {metrics.totalSales.toFixed(0)} DT
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Total Orders */}
            <Grid size={{ xs: 12, sm: 4 }}>
              <Card sx={{ bgcolor: 'secondary.lighter', border: 1, borderColor: 'secondary.light' }}>
                <CardContent sx={{ p: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <ShoppingCartIcon sx={{ color: 'secondary.main', mr: 1 }} />
                    <Typography variant="body2" color="secondary.main">
                      Commandes Totales
                    </Typography>
                  </Box>
                  <Typography variant="h4" sx={{ color: 'secondary.dark' }}>
                    {metrics.totalOrders}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Growth */}
            <Grid size={{ xs: 12, sm: 4 }}>
              <Card
                sx={{
                  bgcolor:
                    metrics.growthStatus === 'no-sales' || metrics.growthStatus === 'no-data'
                      ? 'grey.100'
                      : metrics.growth >= 0
                        ? 'success.lighter'
                        : 'error.lighter',
                  border: 1,
                  borderColor:
                    metrics.growthStatus === 'no-sales' || metrics.growthStatus === 'no-data'
                      ? 'grey.300'
                      : metrics.growth >= 0
                        ? 'success.light'
                        : 'error.light'
                }}
              >
                <CardContent sx={{ p: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <TrendingUpIcon
                      sx={{
                        color:
                          metrics.growthStatus === 'no-sales' || metrics.growthStatus === 'no-data'
                            ? 'grey.500'
                            : metrics.growth >= 0
                              ? 'success.main'
                              : 'error.main',
                        mr: 1,
                        transform: metrics.growth < 0 ? 'rotate(180deg)' : 'none'
                      }}
                    />
                    <Typography
                      variant="body2"
                      color={
                        metrics.growthStatus === 'no-sales' || metrics.growthStatus === 'no-data'
                          ? 'grey.600'
                          : metrics.growth >= 0
                            ? 'success.main'
                            : 'error.main'
                      }
                    >
                      Croissance
                    </Typography>
                  </Box>
                  <Typography
                    variant="h4"
                    sx={{
                      color:
                        metrics.growthStatus === 'no-sales' || metrics.growthStatus === 'no-data'
                          ? 'grey.700'
                          : metrics.growth >= 0
                            ? 'success.dark'
                            : 'error.dark'
                    }}
                  >
                    {metrics.growthStatus === 'no-sales'
                      ? 'N/A'
                      : metrics.growthStatus === 'no-data'
                        ? 'N/A'
                        : metrics.growthStatus === 'new-sales'
                          ? 'Nouveau'
                          : `${metrics.growth >= 0 ? '+' : ''}${metrics.growth.toFixed(1)}%`}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Grid>

        <Grid size={12}>
          <Divider sx={{ my: 2 }} />
        </Grid>

        {/* Additional metrics */}
        <Grid size={12}>
          <Grid container spacing={2}>
            <Grid size={{ xs: 12, sm: 6 }}>
              <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Valeur Moyenne par Commande
                </Typography>
                <Typography variant="h5" color="text.primary">
                  {metrics.averageOrderValue.toFixed(2)} DT
                </Typography>
              </Box>
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Période Analysée
                </Typography>
                <Typography variant="h6" color="text.primary">
                  {data.length} mois
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Grid>

        {/* Monthly breakdown */}
        {data.length > 0 && (
          <>
            <Grid size={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="subtitle2" sx={{ mb: 2 }}>
                Évolution Mensuelle
              </Typography>
            </Grid>
            <Grid size={12}>
              <Box sx={{ maxHeight: 200, overflow: 'auto' }}>
                {data.map((month, index) => (
                  <Box
                    key={index}
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      p: 1,
                      borderBottom: index < data.length - 1 ? 1 : 0,
                      borderColor: 'divider'
                    }}
                  >
                    <Typography variant="body2" sx={{ minWidth: 80 }}>
                      {month.month}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 2 }}>
                      <Typography variant="body2" color="primary.main">
                        {Number(month.sales || 0).toFixed(0)} DT
                      </Typography>
                      <Typography variant="body2" color="secondary.main">
                        {Number(month.orderCount || 0)} cmd
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </Box>
            </Grid>
          </>
        )}
      </Grid>
    </MainCard>
  );
}

SalesMetricsCard.propTypes = {
  isLoading: PropTypes.bool,
  data: PropTypes.array,
  error: PropTypes.string
};
