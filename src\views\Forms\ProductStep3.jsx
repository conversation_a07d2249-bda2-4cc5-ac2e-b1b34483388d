import React from 'react';
import { Form, Row, Col } from 'react-bootstrap';

const ProductStep3 = ({ formData, setFormData, attributFields, attributValues, setAttributValues, error }) => (
  <>
    <Row>
      {attributFields.map((attribut) => (
        <Col md={6} key={attribut.id}>
          <Form.Group controlId={`attribut-${attribut.id}`}>
            <Form.Label>{attribut.nom}</Form.Label>
            <Form.Control
              type="text"
              value={attributValues[attribut.id] || ''}
              onChange={(e) => setAttributValues((prev) => ({ ...prev, [attribut.id]: e.target.value }))}
              required={attribut.obligatoire}
              isInvalid={!!error && attribut.obligatoire && !attributValues[attribut.id]}
            />
          </Form.Group>
        </Col>
      ))}
    </Row>
  </>
);

export default ProductStep3;
