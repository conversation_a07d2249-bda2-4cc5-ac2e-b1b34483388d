# Gestion des Groupes de Clients

## Introduction

Le système permet de gérer des groupes de clients qui bénéficient de remises communes. Un groupe de clients est une entité qui peut avoir plusieurs clients associés et qui bénéficie d'une remise spécifique, sans nécessairement être lié à un point de vente physique.

## Endpoints API

### Récupérer tous les groupes de clients

```
GET /api/groupes-clients
```

Retourne la liste de tous les groupes de clients avec le nombre de clients associés.

#### Exemple de réponse

```json
[
  {
    "id": 1,
    "nom": "Clients Premium",
    "description": "Groupe de clients premium",
    "remise": 8.50,
    "statut": "actif",
    "created_at": "2025-04-01T11:00:00.000000Z",
    "updated_at": "2025-04-01T11:00:00.000000Z",
    "users_count": 5
  },
  {
    "id": 2,
    "nom": "Clients Entreprises",
    "description": "Groupe de clients professionnels",
    "remise": 12.00,
    "statut": "actif",
    "created_at": "2025-04-01T11:30:00.000000Z",
    "updated_at": "2025-04-01T11:30:00.000000Z",
    "users_count": 3
  }
]
```

### Récupérer un groupe de clients spécifique

```
GET /api/groupes-clients/{id}
```

Retourne les détails d'un groupe de clients spécifique avec la liste des clients associés.

#### Exemple de réponse

```json
{
  "id": 1,
  "nom": "Clients Premium",
  "description": "Groupe de clients premium",
  "remise": 8.50,
  "statut": "actif",
  "created_at": "2025-04-01T11:00:00.000000Z",
  "updated_at": "2025-04-01T11:00:00.000000Z",
  "users": [
    {
      "id": 8,
      "name": "Leila Ben Salah",
      "email": "<EMAIL>",
      "roles": ["client"],
      "type_client": "groupe"
    },
    {
      "id": 9,
      "name": "Karim Mejri",
      "email": "<EMAIL>",
      "roles": ["client"],
      "type_client": "groupe"
    }
  ]
}
```

### Créer un nouveau groupe de clients

```
POST /api/groupes-clients
```

Crée un nouveau groupe de clients.

#### Paramètres de la requête

| Paramètre   | Type    | Description                                |
|-------------|---------|--------------------------------------------|
| nom         | string  | Nom du groupe de clients                   |
| description | string  | Description du groupe (optionnel)          |
| remise      | decimal | Pourcentage de remise (0-100)              |
| statut      | string  | Statut du groupe (actif/inactif)           |

#### Exemple de requête

```json
{
  "nom": "Clients Fidèles",
  "description": "Groupe de clients fidèles",
  "remise": 5.00,
  "statut": "actif"
}
```

#### Exemple de réponse

```json
{
  "id": 3,
  "nom": "Clients Fidèles",
  "description": "Groupe de clients fidèles",
  "remise": 5.00,
  "statut": "actif",
  "created_at": "2025-04-04T16:00:00.000000Z",
  "updated_at": "2025-04-04T16:00:00.000000Z"
}
```

### Mettre à jour un groupe de clients

```
PUT /api/groupes-clients/{id}
```

Met à jour les informations d'un groupe de clients existant.

#### Paramètres de la requête

Mêmes paramètres que pour la création, tous optionnels.

#### Exemple de requête

```json
{
  "remise": 6.50,
  "description": "Groupe de clients fidèles mis à jour"
}
```

### Supprimer un groupe de clients

```
DELETE /api/groupes-clients/{id}
```

Supprime un groupe de clients existant. Cette action met à jour tous les clients associés en les définissant comme clients normaux.

### Ajouter un client à un groupe

```
POST /api/groupes-clients/{id}/clients
```

Associe un client existant à un groupe de clients.

#### Paramètres de la requête

| Paramètre | Type    | Description                                |
|-----------|---------|--------------------------------------------|
| user_id   | integer | ID de l'utilisateur à associer             |

#### Exemple de requête

```json
{
  "user_id": 10
}
```

#### Exemple de réponse

```json
{
  "message": "Utilisateur ajouté au groupe de clients avec succès",
  "user": {
    "id": 10,
    "name": "Nadia Mansour",
    "email": "<EMAIL>",
    "roles": ["client"],
    "groupe_client_id": 1,
    "type_client": "groupe"
  }
}
```

### Retirer un client d'un groupe

```
DELETE /api/groupes-clients/{id}/clients/{userId}
```

Retire l'association entre un client et un groupe de clients.

#### Exemple de réponse

```json
{
  "message": "Utilisateur retiré du groupe de clients avec succès"
}
```

## Logique de remise

Lorsqu'un utilisateur est associé à un groupe de clients:

1. Son type_client est défini sur "groupe"
2. Son groupe_client_id est défini sur l'ID du groupe
3. Le rôle "client" est ajouté à ses rôles s'il ne l'a pas déjà

Lors du calcul de la remise effective pour un client:

1. Si le client a une remise personnelle, celle-ci est utilisée en priorité
2. Sinon, si le client est de type "groupe", la remise définie pour le groupe est utilisée

Cette remise est ensuite appliquée automatiquement lors de la création de commandes par ce client, sauf si une remise spécifique à la commande est définie et est plus avantageuse.

## Différence avec les Points de Vente

Contrairement aux points de vente qui représentent des emplacements physiques, les groupes de clients sont des regroupements logiques qui permettent d'appliquer des remises communes à un ensemble de clients sans qu'ils soient nécessairement liés à un emplacement physique.

Exemples de cas d'utilisation pour les groupes de clients:
- Clients VIP
- Clients professionnels
- Clients d'une certaine région
- Clients ayant souscrit à un programme de fidélité

## Changement de type de client

Pour associer un client à un groupe, vous pouvez utiliser l'endpoint suivant:

```
PUT /api/clients/{id}/type
```

#### Exemple de requête

```json
{
  "type_client": "groupe",
  "groupe_client_id": 1
}
```

Cette action associera le client au groupe spécifié et mettra à jour son type.
