# Système de Demandes de Partenariat et de Distribution

Ce document décrit le système de demandes de partenariat et de distribution de l'application.

## Table des matières

1. [Introduction](#introduction)
2. [Modèles de données](#modèles-de-données)
3. [API pour les demandes de partenariat](#api-pour-les-demandes-de-partenariat)
4. [API pour les demandes de distribution](#api-pour-les-demandes-de-distribution)
5. [API Admin](#api-admin)
6. [Flux de travail](#flux-de-travail)

## Introduction

Le système de demandes de partenariat et de distribution permet aux utilisateurs (authentifiés ou non) de demander à devenir partenaires ou distributeurs de la plateforme. Les demandes sont ensuite examinées par les administrateurs qui peuvent les approuver ou les rejeter.

### Principales fonctionnalités

- Soumission de demandes de partenariat et de distribution (avec ou sans compte utilisateur)
- Suivi de l'état des demandes par email ou par compte utilisateur
- Approbation ou rejet des demandes par les administrateurs
- Attribution automatique des rôles et des remises après approbation
- Création automatique de comptes utilisateurs lors de l'approbation si nécessaire

## Modèles de données

### Demande de partenariat

Le modèle `PartnerRequest` représente une demande de partenariat.

```mermaid
classDiagram
    class PartnerRequest {
        +id: int
        +user_id: int (nullable)
        +name: string (nullable)
        +email: string (nullable)
        +company_name: string
        +business_type: string
        +motivation: text
        +website: string
        +phone: string
        +address: string
        +status: enum(pending, approved, rejected)
        +admin_notes: text
        +processed_at: timestamp
        +processed_by: int
        +created_at: timestamp
        +updated_at: timestamp
    }
```

### Demande de distribution

Le modèle `DistributorRequest` représente une demande de distribution.

```mermaid
classDiagram
    class DistributorRequest {
        +id: int
        +user_id: int (nullable)
        +name: string (nullable)
        +email: string (nullable)
        +company_name: string
        +business_type: string
        +motivation: text
        +website: string
        +phone: string
        +address: string
        +city: string
        +postal_code: string
        +country: string
        +tax_id: string
        +registration_number: string
        +has_physical_store: boolean
        +years_in_business: int
        +product_categories_interested: text
        +status: enum(pending, approved, rejected)
        +admin_notes: text
        +processed_at: timestamp
        +processed_by: int
        +created_at: timestamp
        +updated_at: timestamp
    }
```

### Relations

```mermaid
classDiagram
    class User {
        +id: int
        +name: string
        +email: string
        +roles: array
    }

    class PartnerRequest {
        +id: int
        +user_id: int
        +status: enum
    }

    class DistributorRequest {
        +id: int
        +user_id: int
        +status: enum
    }

    class Partenaire {
        +id: int
        +user_id: int
        +remise: decimal
    }

    class PointDeVente {
        +id: int
        +nom: string
        +remise: decimal
    }

    User "1" -- "0..*" PartnerRequest : fait
    User "1" -- "0..*" DistributorRequest : fait
    User "1" -- "0..1" Partenaire : devient
    User "0..*" -- "0..1" PointDeVente : appartient à
```

## API pour les demandes de partenariat

### Obtenir les demandes de partenariat

**Endpoint**: `GET /api/partner-requests`

**Description**: Récupère toutes les demandes de partenariat de l'utilisateur connecté, associées à un email spécifique, ou toutes les demandes si aucun email n'est spécifié.

**Paramètres**:
- `email` (optionnel): Email utilisé pour filtrer les demandes

**Exemples de requête pour utilisateur non authentifié**:
```
GET /api/partner-requests?email=<EMAIL>  # Filtre par email
GET /api/partner-requests  # Récupère toutes les demandes
```

**Réponse**:
```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "user_id": null,
      "name": "John Doe",
      "email": "<EMAIL>",
      "company_name": "Ma Société",
      "business_type": "Retail",
      "motivation": "Je souhaite devenir partenaire pour...",
      "website": "https://example.com",
      "phone": "+***********",
      "address": "123 Rue Exemple, Paris",
      "status": "pending",
      "admin_notes": null,
      "processed_at": null,
      "processed_by": null,
      "created_at": "2023-01-01T00:00:00.000000Z",
      "updated_at": "2023-01-01T00:00:00.000000Z"
    }
  ]
}
```

### Soumettre une demande de partenariat

**Endpoint**: `POST /api/partner-requests`

**Description**: Soumet une nouvelle demande de partenariat.

**Paramètres**:
- `company_name` (obligatoire): Nom de l'entreprise
- `business_type` (optionnel): Type d'activité
- `motivation` (obligatoire): Motivation pour devenir partenaire
- `website` (optionnel): Site web de l'entreprise
- `phone` (obligatoire): Numéro de téléphone
- `address` (obligatoire): Adresse de l'entreprise
- `name` (obligatoire si non authentifié): Nom du demandeur
- `email` (obligatoire si non authentifié): Email du demandeur

**Exemple de requête**:
```json
{
  "company_name": "Ma Société",
  "business_type": "Retail",
  "motivation": "Je souhaite devenir partenaire pour...",
  "website": "https://example.com",
  "phone": "+***********",
  "address": "123 Rue Exemple, Paris"
}
```

**Exemple de réponse**:
```json
{
  "status": "success",
  "message": "Demande de partenariat soumise avec succès",
  "data": {
    "id": 1,
    "user_id": 123,
    "company_name": "Ma Société",
    "business_type": "Retail",
    "motivation": "Je souhaite devenir partenaire pour...",
    "website": "https://example.com",
    "phone": "+***********",
    "address": "123 Rue Exemple, Paris",
    "status": "pending",
    "admin_notes": null,
    "processed_at": null,
    "processed_by": null,
    "created_at": "2023-01-01T00:00:00.000000Z",
    "updated_at": "2023-01-01T00:00:00.000000Z"
  }
}
```

### Obtenir le statut de la dernière demande de partenariat

**Endpoint**: `GET /api/partner-requests/status`

**Description**: Récupère le statut de la dernière demande de partenariat de l'utilisateur connecté ou associée à un email spécifique.

**Paramètres**:
- `email` (obligatoire si non authentifié): Email utilisé pour soumettre la demande

**Exemple de requête pour utilisateur non authentifié**:
```
GET /api/partner-requests/status?email=<EMAIL>
```

**Exemple de réponse**:
```json
{
  "status": "success",
  "data": {
    "has_request": true,
    "is_partner": false,
    "request": {
      "id": 1,
      "status": "pending",
      "created_at": "2023-01-01T00:00:00.000000Z",
      "processed_at": null
    }
  }
}
```

### Obtenir une demande de partenariat spécifique

**Endpoint**: `GET /api/partner-requests/{id}`

**Description**: Récupère les détails d'une demande de partenariat spécifique.

**Paramètres**:
- `email` (obligatoire si non authentifié): Email utilisé pour soumettre la demande

**Exemple de requête pour utilisateur non authentifié**:
```
GET /api/partner-requests/1?email=<EMAIL>
```

**Exemple de réponse**:
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "user_id": null,
    "name": "John Doe",
    "email": "<EMAIL>",
    "company_name": "Ma Société",
    "business_type": "Retail",
    "motivation": "Je souhaite devenir partenaire pour...",
    "website": "https://example.com",
    "phone": "+***********",
    "address": "123 Rue Exemple, Paris",
    "status": "pending",
    "admin_notes": null,
    "processed_at": null,
    "processed_by": null,
    "created_at": "2023-01-01T00:00:00.000000Z",
    "updated_at": "2023-01-01T00:00:00.000000Z"
  }
}
```

## API pour les demandes de distribution

### Obtenir les demandes de distribution

**Endpoint**: `GET /api/distributor-requests`

**Description**: Récupère toutes les demandes de distribution de l'utilisateur connecté, associées à un email spécifique, ou toutes les demandes si aucun email n'est spécifié.

**Paramètres**:
- `email` (optionnel): Email utilisé pour filtrer les demandes

**Exemples de requête pour utilisateur non authentifié**:
```
GET /api/distributor-requests?email=<EMAIL>  # Filtre par email
GET /api/distributor-requests  # Récupère toutes les demandes
```

**Réponse**:
```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "user_id": null,
      "name": "John Doe",
      "email": "<EMAIL>",
      "company_name": "Ma Société",
      "business_type": "Retail",
      "motivation": "Je souhaite devenir distributeur pour...",
      "website": "https://example.com",
      "phone": "+***********",
      "address": "123 Rue Exemple",
      "city": "Paris",
      "postal_code": "75001",
      "country": "France",
      "tax_id": "FR123456789",
      "registration_number": "RCS123456",
      "has_physical_store": true,
      "years_in_business": 5,
      "product_categories_interested": "Électronique, Mode",
      "status": "pending",
      "admin_notes": null,
      "processed_at": null,
      "processed_by": null,
      "created_at": "2023-01-01T00:00:00.000000Z",
      "updated_at": "2023-01-01T00:00:00.000000Z"
    }
  ]
}
```

### Soumettre une demande de distribution

**Endpoint**: `POST /api/distributor-requests`

**Description**: Soumet une nouvelle demande de distribution.

**Paramètres**:
- `company_name` (obligatoire): Nom de l'entreprise
- `business_type` (optionnel): Type d'activité
- `motivation` (optionnel): Motivation pour devenir distributeur
- `website` (optionnel): Site web de l'entreprise
- `phone` (obligatoire): Numéro de téléphone
- `address` (obligatoire): Adresse de l'entreprise
- `city` (obligatoire): Ville
- `postal_code` (optionnel): Code postal
- `country` (obligatoire): Pays
- `tax_id` (optionnel): Numéro de TVA
- `registration_number` (optionnel): Numéro d'immatriculation
- `has_physical_store` (optionnel): Possède un magasin physique
- `years_in_business` (optionnel): Années d'activité
- `product_categories_interested` (optionnel): Catégories de produits intéressées
- `name` (obligatoire si non authentifié): Nom du demandeur
- `email` (obligatoire si non authentifié): Email du demandeur

**Exemple de requête**:
```json
{
  "company_name": "Ma Société",
  "business_type": "Retail",
  "motivation": "Je souhaite devenir distributeur pour...",
  "website": "https://example.com",
  "phone": "+***********",
  "address": "123 Rue Exemple",
  "city": "Paris",
  "postal_code": "75001",
  "country": "France",
  "tax_id": "FR123456789",
  "registration_number": "RCS123456",
  "has_physical_store": true,
  "years_in_business": 5,
  "product_categories_interested": "Électronique, Mode"
}
```

**Exemple de réponse**:
```json
{
  "status": "success",
  "message": "Demande de distributeur soumise avec succès",
  "data": {
    "id": 1,
    "user_id": 123,
    "company_name": "Ma Société",
    "business_type": "Retail",
    "motivation": "Je souhaite devenir distributeur pour...",
    "website": "https://example.com",
    "phone": "+***********",
    "address": "123 Rue Exemple",
    "city": "Paris",
    "postal_code": "75001",
    "country": "France",
    "tax_id": "FR123456789",
    "registration_number": "RCS123456",
    "has_physical_store": true,
    "years_in_business": 5,
    "product_categories_interested": "Électronique, Mode",
    "status": "pending",
    "admin_notes": null,
    "processed_at": null,
    "processed_by": null,
    "created_at": "2023-01-01T00:00:00.000000Z",
    "updated_at": "2023-01-01T00:00:00.000000Z"
  }
}
```

### Obtenir le statut de la dernière demande de distribution

**Endpoint**: `GET /api/distributor-requests/status`

**Description**: Récupère le statut de la dernière demande de distribution de l'utilisateur connecté ou associée à un email spécifique.

**Paramètres**:
- `email` (obligatoire si non authentifié): Email utilisé pour soumettre la demande

**Exemple de requête pour utilisateur non authentifié**:
```
GET /api/distributor-requests/status?email=<EMAIL>
```

**Exemple de réponse**:
```json
{
  "status": "success",
  "data": {
    "has_request": true,
    "is_distributor": false,
    "request": {
      "id": 1,
      "status": "pending",
      "created_at": "2023-01-01T00:00:00.000000Z",
      "processed_at": null
    }
  }
}
```

### Obtenir une demande de distribution spécifique

**Endpoint**: `GET /api/distributor-requests/{id}`

**Description**: Récupère les détails d'une demande de distribution spécifique.

**Paramètres**:
- `email` (obligatoire si non authentifié): Email utilisé pour soumettre la demande

**Exemple de requête pour utilisateur non authentifié**:
```
GET /api/distributor-requests/1?email=<EMAIL>
```

**Exemple de réponse**:
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "user_id": null,
    "name": "John Doe",
    "email": "<EMAIL>",
    "company_name": "Ma Société",
    "business_type": "Retail",
    "motivation": "Je souhaite devenir distributeur pour...",
    "website": "https://example.com",
    "phone": "+***********",
    "address": "123 Rue Exemple",
    "city": "Paris",
    "postal_code": "75001",
    "country": "France",
    "tax_id": "FR123456789",
    "registration_number": "RCS123456",
    "has_physical_store": true,
    "years_in_business": 5,
    "product_categories_interested": "Électronique, Mode",
    "status": "pending",
    "admin_notes": null,
    "processed_at": null,
    "processed_by": null,
    "created_at": "2023-01-01T00:00:00.000000Z",
    "updated_at": "2023-01-01T00:00:00.000000Z"
  }
}
```

## API Admin

### Obtenir toutes les demandes de partenariat

**Endpoint**: `GET /api/admin/partner-requests`

**Description**: Récupère toutes les demandes de partenariat (admin uniquement).

**Paramètres**:
- `status` (optionnel): Filtre par statut (pending, approved, rejected, all)
- `per_page` (optionnel): Nombre de résultats par page

**Exemple de réponse**:
```json
{
  "status": "success",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "user_id": 123,
        "company_name": "Ma Société",
        "business_type": "Retail",
        "motivation": "Je souhaite devenir partenaire pour...",
        "website": "https://example.com",
        "phone": "+***********",
        "address": "123 Rue Exemple, Paris",
        "status": "pending",
        "admin_notes": null,
        "processed_at": null,
        "processed_by": null,
        "created_at": "2023-01-01T00:00:00.000000Z",
        "updated_at": "2023-01-01T00:00:00.000000Z",
        "user": {
          "id": 123,
          "name": "John Doe",
          "email": "<EMAIL>"
        }
      }
    ],
    "first_page_url": "http://example.com/api/admin/partner-requests?page=1",
    "from": 1,
    "last_page": 1,
    "last_page_url": "http://example.com/api/admin/partner-requests?page=1",
    "links": [
      {
        "url": null,
        "label": "&laquo; Previous",
        "active": false
      },
      {
        "url": "http://example.com/api/admin/partner-requests?page=1",
        "label": "1",
        "active": true
      },
      {
        "url": null,
        "label": "Next &raquo;",
        "active": false
      }
    ],
    "next_page_url": null,
    "path": "http://example.com/api/admin/partner-requests",
    "per_page": 15,
    "prev_page_url": null,
    "to": 1,
    "total": 1
  }
}
```

### Approuver une demande de partenariat

**Endpoint**: `POST /api/admin/partner-requests/{id}/approve`

**Description**: Approuve une demande de partenariat (admin uniquement).

**Paramètres**:
- `remise` (obligatoire): Pourcentage de remise à accorder au partenaire
- `admin_notes` (optionnel): Notes de l'administrateur

**Exemple de requête**:
```json
{
  "remise": 10.5,
  "admin_notes": "Partenaire approuvé avec une remise de 10.5%"
}
```

**Exemple de réponse**:
```json
{
  "status": "success",
  "message": "Demande de partenariat approuvée avec succès",
  "data": {
    "id": 1,
    "user_id": 123,
    "company_name": "Ma Société",
    "business_type": "Retail",
    "motivation": "Je souhaite devenir partenaire pour...",
    "website": "https://example.com",
    "phone": "+***********",
    "address": "123 Rue Exemple, Paris",
    "status": "approved",
    "admin_notes": "Partenaire approuvé avec une remise de 10.5%",
    "processed_at": "2023-01-02T00:00:00.000000Z",
    "processed_by": 456,
    "created_at": "2023-01-01T00:00:00.000000Z",
    "updated_at": "2023-01-02T00:00:00.000000Z"
  }
}
```

### Rejeter une demande de partenariat

**Endpoint**: `POST /api/admin/partner-requests/{id}/reject`

**Description**: Rejette une demande de partenariat (admin uniquement).

**Paramètres**:
- `admin_notes` (obligatoire): Raison du rejet

**Exemple de requête**:
```json
{
  "admin_notes": "Informations insuffisantes pour approuver la demande"
}
```

**Exemple de réponse**:
```json
{
  "status": "success",
  "message": "Demande de partenariat rejetée",
  "data": {
    "id": 1,
    "user_id": 123,
    "company_name": "Ma Société",
    "business_type": "Retail",
    "motivation": "Je souhaite devenir partenaire pour...",
    "website": "https://example.com",
    "phone": "+***********",
    "address": "123 Rue Exemple, Paris",
    "status": "rejected",
    "admin_notes": "Informations insuffisantes pour approuver la demande",
    "processed_at": "2023-01-02T00:00:00.000000Z",
    "processed_by": 456,
    "created_at": "2023-01-01T00:00:00.000000Z",
    "updated_at": "2023-01-02T00:00:00.000000Z"
  }
}
```

### Obtenir toutes les demandes de distribution

**Endpoint**: `GET /api/admin/distributor-requests`

**Description**: Récupère toutes les demandes de distribution (admin uniquement).

**Paramètres**:
- `status` (optionnel): Filtre par statut (pending, approved, rejected, all)
- `per_page` (optionnel): Nombre de résultats par page

**Exemple de réponse**:
```json
{
  "status": "success",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "user_id": 123,
        "company_name": "Ma Société",
        "business_type": "Retail",
        "motivation": "Je souhaite devenir distributeur pour...",
        "website": "https://example.com",
        "phone": "+***********",
        "address": "123 Rue Exemple",
        "city": "Paris",
        "postal_code": "75001",
        "country": "France",
        "tax_id": "FR123456789",
        "registration_number": "RCS123456",
        "has_physical_store": true,
        "years_in_business": 5,
        "product_categories_interested": "Électronique, Mode",
        "status": "pending",
        "admin_notes": null,
        "processed_at": null,
        "processed_by": null,
        "created_at": "2023-01-01T00:00:00.000000Z",
        "updated_at": "2023-01-01T00:00:00.000000Z",
        "user": {
          "id": 123,
          "name": "John Doe",
          "email": "<EMAIL>"
        }
      }
    ],
    "first_page_url": "http://example.com/api/admin/distributor-requests?page=1",
    "from": 1,
    "last_page": 1,
    "last_page_url": "http://example.com/api/admin/distributor-requests?page=1",
    "links": [
      {
        "url": null,
        "label": "&laquo; Previous",
        "active": false
      },
      {
        "url": "http://example.com/api/admin/distributor-requests?page=1",
        "label": "1",
        "active": true
      },
      {
        "url": null,
        "label": "Next &raquo;",
        "active": false
      }
    ],
    "next_page_url": null,
    "path": "http://example.com/api/admin/distributor-requests",
    "per_page": 15,
    "prev_page_url": null,
    "to": 1,
    "total": 1
  }
}
```

### Approuver une demande de distribution

**Endpoint**: `POST /api/admin/distributor-requests/{id}/approve`

**Description**: Approuve une demande de distribution (admin uniquement).

**Paramètres**:
- `remise` (obligatoire): Pourcentage de remise à accorder au distributeur
- `admin_notes` (optionnel): Notes de l'administrateur

**Exemple de requête**:
```json
{
  "remise": 15.0,
  "admin_notes": "Distributeur approuvé avec une remise de 15%"
}
```

**Exemple de réponse**:
```json
{
  "status": "success",
  "message": "Demande de distributeur approuvée avec succès",
  "data": {
    "id": 1,
    "user_id": 123,
    "company_name": "Ma Société",
    "business_type": "Retail",
    "motivation": "Je souhaite devenir distributeur pour...",
    "website": "https://example.com",
    "phone": "+***********",
    "address": "123 Rue Exemple",
    "city": "Paris",
    "postal_code": "75001",
    "country": "France",
    "tax_id": "FR123456789",
    "registration_number": "RCS123456",
    "has_physical_store": true,
    "years_in_business": 5,
    "product_categories_interested": "Électronique, Mode",
    "status": "approved",
    "admin_notes": "Distributeur approuvé avec une remise de 15%",
    "processed_at": "2023-01-02T00:00:00.000000Z",
    "processed_by": 456,
    "created_at": "2023-01-01T00:00:00.000000Z",
    "updated_at": "2023-01-02T00:00:00.000000Z"
  }
}
```

### Rejeter une demande de distribution

**Endpoint**: `POST /api/admin/distributor-requests/{id}/reject`

**Description**: Rejette une demande de distribution (admin uniquement).

**Paramètres**:
- `admin_notes` (obligatoire): Raison du rejet

**Exemple de requête**:
```json
{
  "admin_notes": "Informations insuffisantes pour approuver la demande"
}
```

**Exemple de réponse**:
```json
{
  "status": "success",
  "message": "Demande de distributeur rejetée",
  "data": {
    "id": 1,
    "user_id": 123,
    "company_name": "Ma Société",
    "business_type": "Retail",
    "motivation": "Je souhaite devenir distributeur pour...",
    "website": "https://example.com",
    "phone": "+***********",
    "address": "123 Rue Exemple",
    "city": "Paris",
    "postal_code": "75001",
    "country": "France",
    "tax_id": "FR123456789",
    "registration_number": "RCS123456",
    "has_physical_store": true,
    "years_in_business": 5,
    "product_categories_interested": "Électronique, Mode",
    "status": "rejected",
    "admin_notes": "Informations insuffisantes pour approuver la demande",
    "processed_at": "2023-01-02T00:00:00.000000Z",
    "processed_by": 456,
    "created_at": "2023-01-01T00:00:00.000000Z",
    "updated_at": "2023-01-02T00:00:00.000000Z"
  }
}
```

## Flux de travail

### Demande de partenariat

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Admin

    Client->>API: POST /api/partner-requests
    API->>Client: Demande soumise (status: pending)

    Admin->>API: GET /api/admin/partner-requests
    API->>Admin: Liste des demandes en attente

    Admin->>API: GET /api/admin/partner-requests/{id}
    API->>Admin: Détails de la demande

    alt Approbation
        Admin->>API: POST /api/admin/partner-requests/{id}/approve
        API->>Admin: Demande approuvée

        alt Utilisateur existant
            Note over API: Création d'un enregistrement Partenaire
            Note over API: Attribution du rôle 'partenaire' à l'utilisateur
        else Utilisateur non existant
            Note over API: Création d'un nouvel utilisateur
            Note over API: Création d'un enregistrement Partenaire
            Note over API: Attribution du rôle 'partenaire' à l'utilisateur
        end
    else Rejet
        Admin->>API: POST /api/admin/partner-requests/{id}/reject
        API->>Admin: Demande rejetée
    end

    Client->>API: GET /api/partner-requests/status
    API->>Client: Statut de la demande (pending/approved/rejected)
```

### Demande de distribution

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Admin

    Client->>API: POST /api/distributor-requests
    API->>Client: Demande soumise (status: pending)

    Admin->>API: GET /api/admin/distributor-requests
    API->>Admin: Liste des demandes en attente

    Admin->>API: GET /api/admin/distributor-requests/{id}
    API->>Admin: Détails de la demande

    alt Approbation
        Admin->>API: POST /api/admin/distributor-requests/{id}/approve
        API->>Admin: Demande approuvée

        alt Utilisateur existant
            Note over API: Création d'un enregistrement PointDeVente
            Note over API: Attribution du rôle 'point_de_vente' à l'utilisateur
        else Utilisateur non existant
            Note over API: Création d'un nouvel utilisateur
            Note over API: Création d'un enregistrement PointDeVente
            Note over API: Attribution du rôle 'point_de_vente' à l'utilisateur
        end
    else Rejet
        Admin->>API: POST /api/admin/distributor-requests/{id}/reject
        API->>Admin: Demande rejetée
    end

    Client->>API: GET /api/distributor-requests/status
    API->>Client: Statut de la demande (pending/approved/rejected)
```
