import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>er, Card, Table, <PERSON><PERSON>, Alert, Spinner, Badge, Modal, Form, Row, Col, Breadcrumb, InputGroup } from 'react-bootstrap';
import { Box, Typography } from '@mui/material';
import { FaPlus, FaPencilAlt, FaTrashAlt, FaEye, FaHome, FaLayerGroup, FaSearch, FaCalendarAlt } from 'react-icons/fa';

// Design system
import { COLORS, TYPOGRAPHY } from '../../themes/designSystem';
import MainCard from '../../ui-component/cards/MainCard';
import StandardButton from '../../components/StandardButton';
import StandardTable from '../../components/StandardTable';
import StandardCard from '../../components/StandardCard';
import {
  fetchCollections,
  createCollection,
  updateCollection,
  deleteCollection,
  fetchCollectionProducts
} from '../../services/collectionService';
import 'ui-component/extended/ProfessionalModal.css';
import TablePagination from 'components/TablePagination';

const ListCollection = () => {
  // Columns for collections table
  const collectionsColumns = [
    { id: 'nom', label: 'Nom', minWidth: 200 },
    { id: 'description', label: 'Description', minWidth: 250 },
    { id: 'statut', label: 'Statut', minWidth: 100 },
    { id: 'dates', label: 'Dates', minWidth: 180 },
    { id: 'produits', label: 'Produits', minWidth: 100 },
    { id: 'actions', label: 'Actions', minWidth: 200, align: 'center' }
  ];

  // States
  const [collections, setCollections] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Modal states
  const [showModal, setShowModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedCollection, setSelectedCollection] = useState(null);
  const [collectionProducts, setCollectionProducts] = useState([]);
  const [modalLoading, setModalLoading] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    nom: '',
    description: '',
    image: '',
    active: true,
    date_debut: '',
    date_fin: ''
  });
  const [editingId, setEditingId] = useState(null);

  // Load collections on component mount
  useEffect(() => {
    loadCollections();
  }, []);

  const loadCollections = async () => {
    try {
      setLoading(true);
      setError('');
      const response = await fetchCollections();
      console.log('Collections loaded:', response);

      // Handle different response formats
      const collectionsData = response.data || response;

      // Debug: Log the first collection to see its structure
      if (Array.isArray(collectionsData) && collectionsData.length > 0) {
        console.log('First collection structure:', collectionsData[0]);
        console.log('Available product count fields:', {
          products_count: collectionsData[0].products_count,
          produits_count: collectionsData[0].produits_count,
          produits_length: collectionsData[0].produits?.length,
          produits: collectionsData[0].produits
        });
      }

      const collections = Array.isArray(collectionsData) ? collectionsData : [];
      setCollections(collections);

      // If collections don't have product counts, fetch them in the background
      const needsProductCounts = collections.some((collection) => getProductCount(collection) === null);

      if (needsProductCounts) {
        console.log('Some collections missing product counts, fetching in background...');
        // Fetch product counts in background without blocking the UI
        setTimeout(async () => {
          try {
            const collectionsWithCounts = await Promise.all(
              collections.map(async (collection) => {
                const currentCount = getProductCount(collection);

                // If we don't have a product count, try to fetch it
                if (currentCount === null) {
                  try {
                    const products = await fetchCollectionProducts(collection.id);
                    const productCount = (products.data || products || []).length;
                    return { ...collection, products_count: productCount };
                  } catch (error) {
                    console.warn(`Failed to fetch product count for collection ${collection.id}:`, error);
                    return collection;
                  }
                }

                return collection;
              })
            );

            setCollections(collectionsWithCounts);
          } catch (error) {
            console.error('Error fetching product counts:', error);
          }
        }, 100);
      }
    } catch (err) {
      console.error('Error loading collections:', err);
      setError('Erreur lors du chargement des collections: ' + err.message);
      setCollections([]);
    } finally {
      setLoading(false);
    }
  };

  // Filter collections based on search term
  const filteredCollections = collections.filter(
    (collection) =>
      collection.nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      collection.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle form changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Handle form submit
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.nom.trim()) {
      setError('Le nom de la collection est obligatoire');
      return;
    }

    try {
      setModalLoading(true);
      setError('');

      if (editingId) {
        await updateCollection(editingId, formData);
        setSuccess('Collection mise à jour avec succès!');
      } else {
        await createCollection(formData);
        setSuccess('Collection créée avec succès!');
      }

      handleCloseModal();
      loadCollections();
    } catch (err) {
      console.error('Error saving collection:', err);
      setError('Erreur lors de la sauvegarde: ' + err.message);
    } finally {
      setModalLoading(false);
    }
  };

  // Handle edit
  const handleEdit = (collection) => {
    setFormData({
      nom: collection.nom || '',
      description: collection.description || '',
      image: collection.image || '',
      active: collection.active !== false,
      date_debut: collection.date_debut || '',
      date_fin: collection.date_fin || ''
    });
    setEditingId(collection.id);
    setShowModal(true);
  };

  // Handle delete
  const handleDelete = async () => {
    if (!selectedCollection) return;

    try {
      setModalLoading(true);
      await deleteCollection(selectedCollection.id);
      setSuccess('Collection supprimée avec succès!');
      setShowDeleteModal(false);
      setSelectedCollection(null);
      loadCollections();
    } catch (err) {
      console.error('Error deleting collection:', err);
      setError('Erreur lors de la suppression: ' + err.message);
    } finally {
      setModalLoading(false);
    }
  };

  // Handle view details
  const handleViewDetails = async (collection) => {
    setSelectedCollection(collection);
    setShowDetailsModal(true);

    try {
      setModalLoading(true);
      const products = await fetchCollectionProducts(collection.id);
      setCollectionProducts(products.data || products || []);
    } catch (err) {
      console.error('Error loading collection products:', err);
      setCollectionProducts([]);
    } finally {
      setModalLoading(false);
    }
  };

  // Handle close modal
  const handleCloseModal = () => {
    setShowModal(false);
    setFormData({
      nom: '',
      description: '',
      image: '',
      active: true,
      date_debut: '',
      date_fin: ''
    });
    setEditingId(null);
    setError('');
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  // Get product count for a collection
  const getProductCount = (collection) => {
    // Try different possible fields for product count
    if (typeof collection.products_count === 'number') {
      return collection.products_count;
    }
    if (typeof collection.produits_count === 'number') {
      return collection.produits_count;
    }
    if (Array.isArray(collection.produits)) {
      return collection.produits.length;
    }
    if (Array.isArray(collection.products)) {
      return collection.products.length;
    }
    return null; // Return null instead of 0 to indicate unknown count
  };

  // Render product count badge
  const renderProductCount = (collection) => {
    const count = getProductCount(collection);

    if (count === null) {
      return (
        <Badge bg="secondary" className="rounded-pill">
          <Spinner size="sm" animation="border" style={{ width: '12px', height: '12px' }} className="me-1" />
          Chargement...
        </Badge>
      );
    }

    return (
      <Badge bg="info" className="rounded-pill">
        {count} produit(s)
      </Badge>
    );
  };

  // Pagination logic
  const totalPages = Math.ceil(filteredCollections.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentCollections = filteredCollections.slice(startIndex, endIndex);

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Handle pagination
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const handleItemsPerPageChange = (newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page
  };

  // Render cell functions for collections table
  const renderCollectionsCell = (column, row) => {
    switch (column.id) {
      case 'nom':
        return (
          <Box>
            <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
              {row.nom}
            </Typography>
            {row.image && (
              <Typography variant="caption" sx={{ color: COLORS.text.secondary }}>
                Image: {row.image}
              </Typography>
            )}
          </Box>
        );

      case 'description':
        return (
          <Typography
            variant="body2"
            sx={{
              maxWidth: 250,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              color: row.description ? COLORS.text.primary : COLORS.text.secondary,
              fontStyle: row.description ? 'normal' : 'italic'
            }}
          >
            {row.description || 'Aucune description'}
          </Typography>
        );

      case 'statut':
        return (
          <Box
            sx={{
              display: 'inline-flex',
              alignItems: 'center',
              px: 1,
              py: 0.5,
              borderRadius: 1,
              bgcolor: row.active ? COLORS.success.light : COLORS.text.secondary + '20',
              color: row.active ? COLORS.success.main : COLORS.text.secondary
            }}
          >
            <Typography variant="caption" sx={{ fontWeight: 'medium' }}>
              {row.active ? 'Active' : 'Inactive'}
            </Typography>
          </Box>
        );

      case 'dates':
        return (
          <Box>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
              <FaCalendarAlt style={{ marginRight: 4, fontSize: '0.75rem', color: COLORS.text.secondary }} />
              <Typography variant="caption" sx={{ color: COLORS.text.secondary }}>
                Début: {formatDate(row.date_debut)}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <FaCalendarAlt style={{ marginRight: 4, fontSize: '0.75rem', color: COLORS.text.secondary }} />
              <Typography variant="caption" sx={{ color: COLORS.text.secondary }}>
                Fin: {formatDate(row.date_fin)}
              </Typography>
            </Box>
          </Box>
        );

      case 'produits':
        return renderProductCount(row);

      case 'actions':
        return (
          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
            <StandardButton
              variant="info"
              size="small"
              onClick={() => handleViewDetails(row)}
              title="Voir les détails"
              sx={{ minWidth: 'auto', padding: '4px 8px' }}
            >
              <FaEye />
            </StandardButton>
            <StandardButton
              variant="outline"
              size="small"
              onClick={() => handleEdit(row)}
              title="Modifier"
              sx={{ minWidth: 'auto', padding: '4px 8px' }}
            >
              <FaPencilAlt />
            </StandardButton>
            <StandardButton
              variant="error"
              size="small"
              onClick={() => {
                setSelectedCollection(row);
                setShowDeleteModal(true);
              }}
              title="Supprimer"
              sx={{ minWidth: 'auto', padding: '4px 8px' }}
            >
              <FaTrashAlt />
            </StandardButton>
          </Box>
        );

      default:
        return row[column.id];
    }
  };

  return (
    <MainCard>
      <Box sx={{ width: '100%' }}>
        {/* Breadcrumb - Design System Style */}
        <Box sx={{ mb: 2 }}>
          <Typography
            variant="body2"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              color: COLORS.text.secondary,
              fontSize: TYPOGRAPHY.fontSize.sm
            }}
          >
            Accueil &gt; Gestion des Collections
          </Typography>
        </Box>

        {/* Header - Design System Style */}
        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h3"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontWeight: TYPOGRAPHY.fontWeight.bold,
              color: COLORS.text.dark,
              mb: 1
            }}
          >
            Gestion des Collections
          </Typography>
          <Typography
            variant="body1"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              color: COLORS.text.secondary,
              fontSize: TYPOGRAPHY.fontSize.md
            }}
          >
            Gérez toutes vos collections de produits en un seul endroit
          </Typography>
        </Box>

        {/* Error and Success Messages */}
        {error && (
          <Box sx={{ mb: 3 }}>
            <Alert severity="error">{error}</Alert>
          </Box>
        )}
        {success && (
          <Box sx={{ mb: 3 }}>
            <Alert severity="success">{success}</Alert>
          </Box>
        )}

        {/* Search and Add Button */}
        <StandardCard sx={{ mb: 3 }}>
          <Box sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
              <Box sx={{ flex: 1, maxWidth: 400 }}>
                <InputGroup>
                  <InputGroup.Text>
                    <FaSearch />
                  </InputGroup.Text>
                  <Form.Control
                    type="text"
                    placeholder="Rechercher une collection..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </InputGroup>
              </Box>
              <StandardButton variant="primary" onClick={() => setShowModal(true)} startIcon={<FaPlus />} size="medium">
                Ajouter une Collection
              </StandardButton>
            </Box>
          </Box>
        </StandardCard>

        {/* Collections Table */}
        <StandardTable
          columns={collectionsColumns}
          data={currentCollections}
          loading={loading}
          error={error}
          emptyMessage={
            searchTerm
              ? 'Aucune collection ne correspond à votre recherche.'
              : 'Aucune collection trouvée. Créez votre première collection pour commencer.'
          }
          renderCell={renderCollectionsCell}
          hover={true}
          pagination={
            <TablePagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={filteredCollections.length}
              itemsPerPage={itemsPerPage}
              startIndex={startIndex}
              endIndex={endIndex}
              onPageChange={handlePageChange}
              onItemsPerPageChange={handleItemsPerPageChange}
              showDirectPageInput={totalPages > 5}
            />
          }
        />

        {/* Add/Edit Modal */}
        <Modal
          show={showModal}
          onHide={handleCloseModal}
          size="md"
          className="professional-modal"
          dialogClassName="professional-modal-dialog"
        >
          <Modal.Header closeButton className="professional-modal-header bg-primary text-white">
            <Modal.Title>{editingId ? 'Modifier la Collection' : 'Ajouter une Collection'}</Modal.Title>
          </Modal.Header>
          <Form onSubmit={handleSubmit}>
            <Modal.Body className="professional-modal-body">
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Nom de la collection *</Form.Label>
                    <Form.Control
                      type="text"
                      name="nom"
                      value={formData.nom}
                      onChange={handleChange}
                      required
                      placeholder="Nom de la collection"
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Image</Form.Label>
                    <Form.Control type="text" name="image" value={formData.image} onChange={handleChange} placeholder="URL de l'image" />
                  </Form.Group>
                </Col>
              </Row>

              <Form.Group className="mb-3">
                <Form.Label>Description</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  placeholder="Description de la collection"
                />
              </Form.Group>

              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Date de début</Form.Label>
                    <Form.Control type="date" name="date_debut" value={formData.date_debut} onChange={handleChange} />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Date de fin</Form.Label>
                    <Form.Control type="date" name="date_fin" value={formData.date_fin} onChange={handleChange} />
                  </Form.Group>
                </Col>
              </Row>

              <Form.Group className="mb-3">
                <Form.Check type="checkbox" name="active" checked={formData.active} onChange={handleChange} label="Collection active" />
              </Form.Group>
            </Modal.Body>
            <Modal.Footer className="professional-modal-footer">
              <StandardButton variant="secondary" onClick={handleCloseModal}>
                Annuler
              </StandardButton>
              <StandardButton type="submit" variant="primary" disabled={modalLoading} loading={modalLoading}>
                {editingId ? 'Mettre à jour' : 'Créer'}
              </StandardButton>
            </Modal.Footer>
          </Form>
        </Modal>

        {/* Delete Confirmation Modal */}
        <Modal
          show={showDeleteModal}
          onHide={() => setShowDeleteModal(false)}
          className="professional-modal"
          dialogClassName="professional-modal-dialog"
        >
          <Modal.Header closeButton className="professional-modal-header bg-danger text-white">
            <Modal.Title>Confirmer la suppression</Modal.Title>
          </Modal.Header>
          <Modal.Body className="professional-modal-body">
            <p>
              Êtes-vous sûr de vouloir supprimer la collection <strong>"{selectedCollection?.nom}"</strong> ?
            </p>
            <p className="text-muted small">Cette action est irréversible et supprimera également tous les liens avec les produits.</p>
          </Modal.Body>
          <Modal.Footer className="professional-modal-footer">
            <StandardButton variant="secondary" onClick={() => setShowDeleteModal(false)}>
              Annuler
            </StandardButton>
            <StandardButton variant="error" onClick={handleDelete} disabled={modalLoading} loading={modalLoading}>
              Supprimer
            </StandardButton>
          </Modal.Footer>
        </Modal>

        {/* Details Modal */}
        <Modal
          show={showDetailsModal}
          onHide={() => setShowDetailsModal(false)}
          size="lg"
          className="professional-modal"
          dialogClassName="professional-modal-dialog"
        >
          <Modal.Header closeButton className="professional-modal-header bg-info text-white">
            <Modal.Title>Détails de la collection: {selectedCollection?.nom}</Modal.Title>
          </Modal.Header>
          <Modal.Body className="professional-modal-body">
            {selectedCollection && (
              <Row>
                <Col md={6}>
                  <Card className="h-100">
                    <Card.Header>
                      <h6 className="mb-0">Informations générales</h6>
                    </Card.Header>
                    <Card.Body>
                      <div className="mb-3">
                        <strong>Nom:</strong> {selectedCollection.nom}
                      </div>
                      <div className="mb-3">
                        <strong>Description:</strong> {selectedCollection.description || 'Aucune description'}
                      </div>
                      <div className="mb-3">
                        <strong>Statut:</strong>{' '}
                        <Badge bg={selectedCollection.active ? 'success' : 'secondary'}>
                          {selectedCollection.active ? 'Active' : 'Inactive'}
                        </Badge>
                      </div>
                      <div className="mb-3">
                        <strong>Date de début:</strong> {formatDate(selectedCollection.date_debut)}
                      </div>
                      <div className="mb-3">
                        <strong>Date de fin:</strong> {formatDate(selectedCollection.date_fin)}
                      </div>
                      {selectedCollection.image && (
                        <div className="mb-3">
                          <strong>Image:</strong> {selectedCollection.image}
                        </div>
                      )}
                      <div className="mb-3">
                        <strong>Créée le:</strong> {formatDate(selectedCollection.created_at)}
                      </div>
                      <div>
                        <strong>Modifiée le:</strong> {formatDate(selectedCollection.updated_at)}
                      </div>
                    </Card.Body>
                  </Card>
                </Col>
                <Col md={6}>
                  <Card className="h-100">
                    <Card.Header>
                      <h6 className="mb-0">Produits ({collectionProducts.length})</h6>
                    </Card.Header>
                    <Card.Body>
                      {modalLoading ? (
                        <div className="text-center py-3">
                          <Spinner animation="border" size="sm" />
                          <p className="mt-2 small text-muted">Chargement des produits...</p>
                        </div>
                      ) : collectionProducts.length === 0 ? (
                        <div className="text-center py-3 text-muted">
                          <FaLayerGroup size={32} className="mb-2" />
                          <p>Aucun produit dans cette collection</p>
                        </div>
                      ) : (
                        <div className="table-responsive">
                          <Table size="sm" hover>
                            <thead>
                              <tr>
                                <th>Produit</th>
                                <th>Prix</th>
                                <th>Ordre</th>
                                <th>Vedette</th>
                              </tr>
                            </thead>
                            <tbody>
                              {collectionProducts.map((product) => (
                                <tr key={product.id}>
                                  <td>
                                    <div className="fw-medium">{product.nom_produit}</div>
                                    <small className="text-muted">ID: {product.id}</small>
                                  </td>
                                  <td>{product.prix_produit}€</td>
                                  <td>
                                    <Badge bg="secondary" className="rounded-pill">
                                      {product.pivot?.ordre || '-'}
                                    </Badge>
                                  </td>
                                  <td>
                                    {product.pivot?.featured ? <Badge bg="warning">Vedette</Badge> : <span className="text-muted">-</span>}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </Table>
                        </div>
                      )}
                    </Card.Body>
                  </Card>
                </Col>
              </Row>
            )}
          </Modal.Body>
          <Modal.Footer className="professional-modal-footer">
            <StandardButton variant="secondary" onClick={() => setShowDetailsModal(false)}>
              Fermer
            </StandardButton>
          </Modal.Footer>
        </Modal>
      </Box>
    </MainCard>
  );
};

export default ListCollection;
