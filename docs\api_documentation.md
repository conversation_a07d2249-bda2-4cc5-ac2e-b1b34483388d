# Documentation de l'API

Cette documentation présente les différentes routes API disponibles dans l'application.

## Table des matières

1. [Authentification](#authentification)
2. [Marques](#marques)
3. [Produits](#produits)
4. [Attributs et Variantes](#attributs-et-variantes)
5. [Catégories](#catégories)
6. [Sous-Catégories](#sous-catégories)
7. [Sous-Sous-Catégories](#sous-sous-catégories)
8. [Commandes](#commandes)
9. [Clients](#clients)
10. [Partenaires](#partenaires)
11. [Points de Vente](#points-de-vente)
12. [Groupes de Clients](#groupes-de-clients)
13. [Collections](#collections)
14. [Paiements](#paiements)
15. [Promotions et Remises](#promotions-et-remises)
16. [Panier](#panier)
17. [Images](#images)
18. [Gestion du Contenu](#gestion-du-contenu)

## Authentification

| Méthode | URL | Description |
|---------|-----|-------------|
| POST | `/api/auth/verify` | Vérifier les informations d'authentification |
| POST | `/api/auth/logout` | Déconnexion |
| POST | `/api/auth/refresh` | Rafraîchir le token d'authentification |
| GET | `/api/auth/user` | Obtenir les informations de l'utilisateur connecté |

## Marques

| Méthode | URL | Description |
|---------|-----|-------------|
| GET | `/api/marques` | Liste de toutes les marques |
| POST | `/api/marques` | Créer une nouvelle marque |
| GET | `/api/marques/{id}` | Détails d'une marque spécifique |
| GET | `/api/marques/{id}/produits` | Produits d'une marque spécifique |
| PUT | `/api/marques/{id}` | Mettre à jour une marque |
| DELETE | `/api/marques/{id}` | Supprimer une marque |

## Produits

| Méthode | URL | Description |
|---------|-----|-------------|
| GET | `/api/produits` | Liste de tous les produits |
| POST | `/api/produits` | Créer un nouveau produit |
| GET | `/api/produits/{id}` | Détails d'un produit spécifique |
| PUT | `/api/produits/{id}` | Mettre à jour un produit |
| DELETE | `/api/produits/{id}` | Supprimer un produit |
| GET | `/api/produits/search` | Rechercher des produits |
| GET | `/api/produits/perpages` | Liste paginée des produits |
| GET | `/api/produits/filtrer` | Filtrer les produits par attributs |
| POST | `/api/produits/{produit}/promotions` | Associer une promotion à un produit |
| DELETE | `/api/produits/{produit}/promotions/{promotion}` | Dissocier une promotion d'un produit |

## Attributs et Variantes

### Attributs de Produits

| Méthode | URL | Description |
|---------|-----|-------------|
| GET | `/api/produits/{id}/attributs` | Obtenir les attributs d'un produit |
| POST | `/api/produits/{id}/attributs` | Définir les attributs d'un produit |
| PUT | `/api/produits/{id}/attributs/{attributId}` | Mettre à jour un attribut d'un produit |
| DELETE | `/api/produits/{id}/attributs/{attributId}` | Supprimer un attribut d'un produit |
| GET | `/api/attributs/filtrables` | Obtenir les attributs filtrables |

### Variantes de Produits

| Méthode | URL | Description |
|---------|-----|-------------|
| GET | `/api/produits/{id}/variantes` | Obtenir les variantes d'un produit |
| POST | `/api/produits/{produit_id}/variantes` | Créer une nouvelle variante pour un produit |
| GET | `/api/variantes/{id}` | Détails d'une variante spécifique |
| PUT | `/api/variantes/{id}` | Mettre à jour une variante |
| DELETE | `/api/variantes/{id}` | Supprimer une variante |
| PATCH | `/api/variantes/{id}/stock` | Mettre à jour le stock d'une variante |

### Administration des Attributs

| Méthode | URL | Description |
|---------|-----|-------------|
| GET | `/api/admin/groupes-attributs` | Liste de tous les groupes d'attributs |
| POST | `/api/admin/groupes-attributs` | Créer un nouveau groupe d'attributs |
| GET | `/api/admin/groupes-attributs/{id}` | Détails d'un groupe d'attributs spécifique |
| PUT | `/api/admin/groupes-attributs/{id}` | Mettre à jour un groupe d'attributs |
| DELETE | `/api/admin/groupes-attributs/{id}` | Supprimer un groupe d'attributs |
| GET | `/api/admin/attributs` | Liste de tous les attributs |
| POST | `/api/admin/attributs` | Créer un nouvel attribut |
| GET | `/api/admin/attributs/{id}` | Détails d'un attribut spécifique |
| PUT | `/api/admin/attributs/{id}` | Mettre à jour un attribut |
| DELETE | `/api/admin/attributs/{id}` | Supprimer un attribut |
| POST | `/api/admin/attributs/{id}/sous-categories/{sousCategorieId}` | Associer un attribut à une sous-catégorie |
| DELETE | `/api/admin/attributs/{id}/sous-categories/{sousCategorieId}` | Dissocier un attribut d'une sous-catégorie |

## Catégories

| Méthode | URL | Description |
|---------|-----|-------------|
| GET | `/api/categories` | Liste de toutes les catégories |
| POST | `/api/categories` | Créer une nouvelle catégorie |
| GET | `/api/categories/{id}` | Détails d'une catégorie spécifique |
| GET | `/api/categories/{id}/sousCategories` | Sous-catégories d'une catégorie spécifique |
| PUT | `/api/categories/{id}` | Mettre à jour une catégorie |
| DELETE | `/api/categories/{id}` | Supprimer une catégorie |
| GET | `/api/categories/featured` | Récupérer les catégories mises en avant |
| PUT | `/api/categories/{id}/featured` | Définir une catégorie comme mise en avant |
| POST | `/api/categories/featured/reorder` | Réorganiser les catégories mises en avant |

## Sous-Catégories

| Méthode | URL | Description |
|---------|-----|-------------|
| GET | `/api/sousCategories` | Liste de toutes les sous-catégories |
| POST | `/api/sousCategories` | Créer une nouvelle sous-catégorie |
| GET | `/api/sousCategories/{id}` | Détails d'une sous-catégorie spécifique |
| PUT | `/api/sousCategories/{id}` | Mettre à jour une sous-catégorie |
| DELETE | `/api/sousCategories/{id}` | Supprimer une sous-catégorie |
| GET | `/api/sousCategories/{id}/caracteristiques` | *(Déprécié)* Caractéristiques d'une sous-catégorie spécifique |

## Sous-Sous-Catégories

| Méthode | URL | Description |
|---------|-----|-------------|
| GET | `/api/sous_sousCategories` | Liste de toutes les sous-sous-catégories |
| POST | `/api/sous_sousCategories` | Créer une nouvelle sous-sous-catégorie |
| GET | `/api/sous_sousCategories/{id}` | Détails d'une sous-sous-catégorie spécifique |
| GET | `/api/sous_sousCategories/{id}/attributs` | Attributs d'une sous-sous-catégorie spécifique (hérités de la sous-catégorie parente) |
| PUT | `/api/sous_sousCategories/{id}` | Mettre à jour une sous-sous-catégorie |
| DELETE | `/api/sous_sousCategories/{id}` | Supprimer une sous-sous-catégorie |

## Commandes

| Méthode | URL | Description |
|---------|-----|-------------|
| GET | `/api/commandes` | Liste de toutes les commandes |
| POST | `/api/commandes` | Créer une nouvelle commande |
| GET | `/api/commandes/{id}` | Détails d'une commande spécifique |
| PUT | `/api/commandes/{id}` | Mettre à jour une commande |
| DELETE | `/api/commandes/{id}` | Supprimer une commande |

## Clients

| Méthode | URL | Description |
|---------|-----|-------------|
| GET | `/api/clients` | Liste de tous les clients |
| GET | `/api/clients/{id}` | Détails d'un client spécifique |
| GET | `/api/clients/{id}/derniere-commande` | Dernière commande d'un client spécifique |
| GET | `/api/clients/{id}/commandes` | Commandes d'un client spécifique |
| PUT | `/api/clients/{id}/remise` | Mettre à jour la remise d'un client |
| PUT | `/api/clients/{id}/profil-remise` | Mettre à jour le profil de remise d'un client |
| PUT | `/api/clients/{id}/type` | *(Déprécié)* Mettre à jour le type d'un client |

## Partenaires

| Méthode | URL | Description |
|---------|-----|-------------|
| GET | `/api/partenaires` | Liste de tous les partenaires |
| POST | `/api/partenaires` | Créer un nouveau partenaire |
| GET | `/api/partenaires/{id}` | Détails d'un partenaire spécifique |
| PUT | `/api/partenaires/{id}` | Mettre à jour un partenaire |
| DELETE | `/api/partenaires/{id}` | Supprimer un partenaire |

## Points de Vente

| Méthode | URL | Description |
|---------|-----|-------------|
| GET | `/api/points-de-vente` | Liste de tous les points de vente |
| POST | `/api/points-de-vente` | Créer un nouveau point de vente |
| GET | `/api/points-de-vente/{id}` | Détails d'un point de vente spécifique |
| PUT | `/api/points-de-vente/{id}` | Mettre à jour un point de vente |
| DELETE | `/api/points-de-vente/{id}` | Supprimer un point de vente |
| POST | `/api/points-de-vente/{id}/clients` | Ajouter un client à un point de vente |
| DELETE | `/api/points-de-vente/{id}/clients/{userId}` | Supprimer un client d'un point de vente |

## Groupes de Clients

| Méthode | URL | Description |
|---------|-----|-------------|
| GET | `/api/groupes-clients` | Liste de tous les groupes de clients |
| POST | `/api/groupes-clients` | Créer un nouveau groupe de clients |
| GET | `/api/groupes-clients/{id}` | Détails d'un groupe de clients spécifique |
| PUT | `/api/groupes-clients/{id}` | Mettre à jour un groupe de clients |
| DELETE | `/api/groupes-clients/{id}` | Supprimer un groupe de clients |
| POST | `/api/groupes-clients/{id}/clients` | Ajouter un client à un groupe de clients |
| DELETE | `/api/groupes-clients/{id}/clients/{userId}` | Supprimer un client d'un groupe de clients |

## Collections

| Méthode | URL | Description |
|---------|-----|-------------|
| GET | `/api/collections` | Liste de toutes les collections |
| POST | `/api/collections` | Créer une nouvelle collection |
| GET | `/api/collections/{id}` | Détails d'une collection spécifique |
| PUT | `/api/collections/{id}` | Mettre à jour une collection |
| DELETE | `/api/collections/{id}` | Supprimer une collection |
| POST | `/api/collections/{id}/produits` | Ajouter des produits à une collection |
| DELETE | `/api/collections/{id}/produits/{produitId}` | Supprimer un produit d'une collection |
| GET | `/api/collections/{id}/produits` | Produits d'une collection spécifique |
| GET | `/api/collections/{id}/produits-featured` | Produits mis en avant d'une collection spécifique |
| POST | `/api/collections/{collection}/promotions` | Associer une promotion à une collection |
| DELETE | `/api/collections/{collection}/promotions/{promotion}` | Dissocier une promotion d'une collection |

## Paiements

| Méthode | URL | Description |
|---------|-----|-------------|
| GET | `/api/paiements` | Liste de tous les paiements |
| POST | `/api/paiements` | Créer un nouveau paiement |
| GET | `/api/paiements/{id}` | Détails d'un paiement spécifique |
| PUT | `/api/paiements/{id}` | Mettre à jour un paiement |
| DELETE | `/api/paiements/{id}` | Supprimer un paiement |

## Promotions et Remises

| Méthode | URL | Description |
|---------|-----|-------------|
| GET | `/api/promotions` | Liste de toutes les promotions |
| POST | `/api/promotions` | Créer une nouvelle promotion |
| GET | `/api/promotions/{id}` | Détails d'une promotion spécifique |
| PUT | `/api/promotions/{id}` | Mettre à jour une promotion |
| DELETE | `/api/promotions/{id}` | Supprimer une promotion |
| GET | `/api/regle-remises` | Liste de toutes les règles de remise |
| POST | `/api/regle-remises` | Créer une nouvelle règle de remise |
| GET | `/api/regle-remises/{id}` | Détails d'une règle de remise spécifique |
| PUT | `/api/regle-remises/{id}` | Mettre à jour une règle de remise |
| DELETE | `/api/regle-remises/{id}` | Supprimer une règle de remise |

## Panier

| Méthode | URL | Description |
|---------|-----|-------------|
| GET | `/api/panier` | Obtenir le contenu du panier |
| POST | `/api/panier/ajouter` | Ajouter un article au panier |
| PUT | `/api/panier/items/{itemId}` | Mettre à jour un article du panier |
| DELETE | `/api/panier/items/{itemId}` | Supprimer un article du panier |
| DELETE | `/api/panier/vider` | Vider le panier |

## Images

| Méthode | URL | Description |
|---------|-----|-------------|
| POST | `/api/images/upload` | Télécharger une image |
| POST | `/api/images/upload-multiple` | Télécharger plusieurs images |
| GET | `/api/images/get` | Obtenir les images d'un modèle |
| PUT | `/api/images/{id}` | Mettre à jour une image |
| DELETE | `/api/images/{id}` | Supprimer une image |
| POST | `/api/images/reorder` | Réorganiser les images |
| GET | `/api/images/serve/{id}` | Servir une image par son ID |
| GET | `/api/images/thumbnail/{id}/{size}` | Servir une miniature d'image (tailles: small, medium, large) |
| GET | `/api/images/file/{path}` | Servir une image par son chemin |

## Détails des API spécifiques

### Récupérer les attributs d'une sous-sous-catégorie

**Endpoint**: `GET /api/sous_sousCategories/{id}/attributs`

**Description**: Récupère les attributs associés à la sous-catégorie parente d'une sous-sous-catégorie.

**Paramètres de chemin**:
- `id` (obligatoire): ID de la sous-sous-catégorie

**Réponse**:
```json
[
  {
    "id": 1,
    "nom": "Couleur",
    "description": "Couleur du produit",
    "type_valeur": "texte",
    "groupe": {
      "id": 1,
      "nom": "Caractéristiques physiques"
    },
    "obligatoire": true,
    "filtrable": true,
    "comparable": true,
    "affichable": true,
    "ordre": 1
  },
  {
    "id": 2,
    "nom": "Taille",
    "description": "Taille du produit",
    "type_valeur": "texte",
    "groupe": {
      "id": 1,
      "nom": "Caractéristiques physiques"
    },
    "obligatoire": true,
    "filtrable": true,
    "comparable": true,
    "affichable": true,
    "ordre": 2
  }
]
```

**Codes de statut**:
- `200 OK`: Les attributs ont été récupérés avec succès
- `404 Not Found`: La sous-sous-catégorie ou sa sous-catégorie parente n'existe pas
- `500 Internal Server Error`: Une erreur s'est produite lors de la récupération des attributs

### Système d'images

Le système d'images permet de gérer les images pour différents modèles (produits, catégories, sous-catégories, sous-sous-catégories, collections, marques, variantes de produits).

#### Télécharger une image

**Endpoint**: `POST /api/images/upload`

**Paramètres**:
- `model_type` (obligatoire): Type de modèle (produit, categorie, sous_categorie, sous_sous_categorie, collection, marque, produit_variante)
- `model_id` (obligatoire): ID du modèle
- `image` (obligatoire): Fichier image
- `is_primary` (optionnel): Définir comme image principale (true/false)
- `alt_text` (optionnel): Texte alternatif
- `title` (optionnel): Titre de l'image

**Réponse**:
```json
{
  "message": "Image uploaded successfully",
  "image": {
    "id": 1,
    "path": "produits/1/image.jpg",
    "filename": "image.jpg",
    "disk": "s3",
    "mime_type": "image/jpeg",
    "size": 123456,
    "alt_text": "Description de l'image",
    "title": "Titre de l'image",
    "imageable_type": "App\\Models\\Produit",
    "imageable_id": 1,
    "is_primary": true,
    "order": 0,
    "metadata": {
      "width": 800,
      "height": 600,
      "original_filename": "image.jpg",
      "extension": "jpg"
    },
    "created_at": "2023-01-01T00:00:00.000000Z",
    "updated_at": "2023-01-01T00:00:00.000000Z"
  },
  "url": "https://example.com/api/images/serve/1",
  "direct_url": "https://example.com/api/images/file/produits/1/image.jpg"
}
```

#### Obtenir les images d'un modèle

**Endpoint**: `GET /api/images/get`

**Paramètres**:
- `model_type` (obligatoire): Type de modèle (produit, categorie, sous_categorie, sous_sous_categorie, collection, marque, produit_variante)
- `model_id` (obligatoire): ID du modèle

**Réponse**:
```json
{
  "images": [
    {
      "id": 1,
      "path": "produits/1/image.jpg",
      "filename": "image.jpg",
      "disk": "s3",
      "mime_type": "image/jpeg",
      "size": 123456,
      "alt_text": "Description de l'image",
      "title": "Titre de l'image",
      "imageable_type": "App\\Models\\Produit",
      "imageable_id": 1,
      "is_primary": true,
      "order": 0,
      "metadata": {
        "width": 800,
        "height": 600,
        "original_filename": "image.jpg",
        "extension": "jpg"
      },
      "created_at": "2023-01-01T00:00:00.000000Z",
      "updated_at": "2023-01-01T00:00:00.000000Z",
      "url": "https://example.com/api/images/serve/1",
      "direct_url": "https://example.com/api/images/file/produits/1/image.jpg",
      "thumbnail_small": "https://example.com/api/images/thumbnail/1/small",
      "thumbnail_medium": "https://example.com/api/images/thumbnail/1/medium",
      "thumbnail_large": "https://example.com/api/images/thumbnail/1/large"
    }
  ]
}
```

### Système de panier

Le système de panier utilise des cookies pour stocker l'ID de session du panier. Les routes du panier sont protégées par le middleware `CookieCartMiddleware` qui gère la création et la récupération de l'ID de session.

#### Obtenir le contenu du panier

**Endpoint**: `GET /api/panier`

**Réponse**:
```json
{
  "panier": {
    "id": 1,
    "session_id": "abc123",
    "client_id": null,
    "created_at": "2023-01-01T00:00:00.000000Z",
    "updated_at": "2023-01-01T00:00:00.000000Z",
    "items": [
      {
        "id": 1,
        "panier_id": 1,
        "produit_id": 1,
        "variante_id": 2,
        "quantite": 2,
        "prix_unitaire": 19.99,
        "created_at": "2023-01-01T00:00:00.000000Z",
        "updated_at": "2023-01-01T00:00:00.000000Z",
        "produit": {
          "id": 1,
          "nom_produit": "Produit 1",
          "prix_produit": 19.99,
          "image_produit": "produit1.jpg"
        },
        "variante": {
          "id": 2,
          "produit_parent_id": 1,
          "sku": "PROD1-VAR2",
          "prix": 19.99,
          "stock": 10
        }
      }
    ]
  },
  "total": 39.98
}
```

#### Ajouter un article au panier

**Endpoint**: `POST /api/panier/ajouter`

**Paramètres**:
- `produit_id` (obligatoire): ID du produit
- `quantite` (obligatoire): Quantité
- `variante_id` (optionnel): ID de la variante

**Réponse**:
```json
{
  "message": "Article ajouté au panier",
  "panier": {
    "id": 1,
    "session_id": "abc123",
    "client_id": null,
    "created_at": "2023-01-01T00:00:00.000000Z",
    "updated_at": "2023-01-01T00:00:00.000000Z",
    "items": [
      {
        "id": 1,
        "panier_id": 1,
        "produit_id": 1,
        "variante_id": 2,
        "quantite": 2,
        "prix_unitaire": 19.99,
        "created_at": "2023-01-01T00:00:00.000000Z",
        "updated_at": "2023-01-01T00:00:00.000000Z"
      }
    ]
  },
  "total": 39.98
}
```

## Notes importantes

1. **Système d'attributs vs. Caractéristiques**: Le système de caractéristiques est déprécié et a été remplacé par le système d'attributs. Utilisez les routes d'attributs au lieu des routes de caractéristiques.

2. **Type de client vs. Profil de remise**: La route `/api/clients/{id}/type` est dépréciée. Utilisez `/api/clients/{id}/profil-remise` à la place.

3. **Authentification**: Certaines routes nécessitent une authentification et des rôles spécifiques. Ces routes sont protégées par les middlewares `protected` et `role`.

4. **Images**: Le système d'images utilise un proxy pour servir les images. Utilisez les URLs retournées par l'API pour accéder aux images.

5. **Panier**: Le système de panier utilise des cookies pour stocker l'ID de session du panier. Assurez-vous que votre client gère correctement les cookies.

## Gestion du Contenu

La gestion du contenu comprend les carousels et les catégories mises en avant. Pour plus de détails, consultez la [documentation dédiée](content_management.md).

### Carousels

| Méthode | URL | Description |
|---------|-----|-------------|
| GET | `/api/carousels` | Liste de tous les carousels |
| POST | `/api/carousels` | Créer un nouveau carousel |
| GET | `/api/carousels/{id}` | Détails d'un carousel spécifique |
| PUT | `/api/carousels/{id}` | Mettre à jour un carousel |
| DELETE | `/api/carousels/{id}` | Supprimer un carousel |
| GET | `/api/carousels/actifs` | Récupérer les carousels actifs |

### Slides de Carousel

| Méthode | URL | Description |
|---------|-----|-------------|
| GET | `/api/carousels/slides` | Liste des slides (paramètre carousel_id requis) |
| POST | `/api/carousels/slides` | Créer un nouveau slide |
| GET | `/api/carousels/slides/{id}` | Détails d'un slide spécifique |
| PUT | `/api/carousels/slides/{id}` | Mettre à jour un slide |
| DELETE | `/api/carousels/slides/{id}` | Supprimer un slide |
| POST | `/api/carousels/slides/reorder` | Réorganiser les slides |
