# Cart System Update Documentation

## Overview

We've updated the cart system to no longer rely on session IDs. Instead, we now use a more robust approach with guest IDs for anonymous users and client IDs for authenticated users. This change improves the reliability of the cart system, especially for authenticated users, and ensures that carts are properly associated with users across different devices and sessions.

## Key Changes

### 1. Database Structure

- Removed the `session_id` column from the `paniers` table
- Added a `guest_id` column (UUID type) to the `paniers` table
- Added a unique constraint on `guest_id` to ensure each guest has only one cart

### 2. <PERSON><PERSON> Management

- Changed the cookie name from `cart_session` to `cart_guest_id`
- The cookie still has the same lifetime (30 days) and security settings

### 3. API Response Format

The cart API responses now include `guest_id` instead of `session_id`:

```json
{
  "status": "success",
  "data": {
    "id": 123,
    "guest_id": "550e8400-e29b-41d4-a716-************", // For guest users
    "client_id": null, // For guest users
    // OR
    "guest_id": null, // For authenticated users
    "client_id": 456, // For authenticated users
    "items": [...],
    "nombre_items": 2,
    "sous_total": 199.98,
    "total": 199.98,
    "date_creation": "2023-04-24T12:34:56.000000Z",
    "date_modification": "2023-04-24T12:34:56.000000Z"
  }
}
```

### 4. Cart Merging

The cart merging functionality has been updated to work with guest IDs instead of session IDs. The API endpoint for merging carts now expects a `guest_id` parameter instead of `guest_cart_id`:

```
POST /api/panier/merge
{
  "guest_id": "550e8400-e29b-41d4-a716-************"
}
```

## Impact on Frontend Implementation

### What You Need to Update

1. **Cookie Handling**:
   - Update your code to read and store the `cart_guest_id` cookie instead of `cart_session`
   - The cookie is still set automatically by the backend

2. **API Requests**:
   - Update any code that references `session_id` in API responses to use `guest_id` instead
   - Update the cart merge API call to send `guest_id` instead of `guest_cart_id`

3. **Authentication Flow**:
   - No changes needed to the authentication flow itself
   - The backend will automatically associate guest carts with user accounts upon login

### Example Code Updates

#### Before:

```javascript
// Reading cart data
const cartData = response.data;
const cartId = cartData.id;
const sessionId = cartData.session_id;

// Merging carts
const mergeResponse = await api.post('/api/panier/merge', {
  guest_cart_id: cartId
});
```

#### After:

```javascript
// Reading cart data
const cartData = response.data;
const cartId = cartData.id;
const guestId = cartData.guest_id;

// Merging carts
const mergeResponse = await api.post('/api/panier/merge', {
  guest_id: guestId
});
```

## Authentication and Cart Association

The cart system now handles authentication more robustly:

1. For **guest users**, a unique `guest_id` is generated and stored in a cookie
2. For **authenticated users**, the cart is associated with their `client_id` and the `guest_id` is set to null
3. When a guest user logs in, their cart is automatically associated with their user account
4. If a user already has a cart and logs in from a different device with another cart, the carts are automatically merged

## Testing the Changes

To test these changes:

1. Clear your browser cookies to start fresh
2. Add items to your cart as a guest user
3. Log in to your account
4. Verify that your cart items are still there
5. Log in from a different device or browser
6. Verify that your cart is consistent across devices

## Troubleshooting

If you encounter any issues:

1. Check the browser console for errors
2. Verify that the `cart_guest_id` cookie is being set correctly
3. Check that your API requests include the correct authentication headers
4. For cross-origin requests, ensure that your frontend is configured to include credentials:
   ```javascript
   axios.defaults.withCredentials = true;
   ```
   or
   ```javascript
   fetch(url, {
     credentials: 'include'
   });
   ```

## Additional Notes

- The old `cart_session` cookie will be automatically removed if it exists
- The backend will continue to support both cookie-based and token-based authentication
- The cart system now works more reliably across different devices and browsers

If you have any questions or encounter any issues with these changes, please let us know.
