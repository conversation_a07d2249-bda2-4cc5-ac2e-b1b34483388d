import React, { useState, useEffect } from 'react';
import { Ta<PERSON>, Tab, Card, Container, Row, Col, Button, Form, Badge, Pa<PERSON>ation, <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css';
import { fetchClients, createClient, updateClient, deleteClient, fetchClientGroups } from '../../services/clientService';
import ProfessionalModal from '../../ui-component/extended/ProfessionalModal';
import { FormModal, ConfirmationModal } from '../../ui-component/extended/ModalVariants';
import { FaPlus, FaEdit, FaTrash } from 'react-icons/fa';

const fontStyle = {
  fontFamily: "'Montserrat', sans-serif",
  fontWeight: 500
};

const colors = {
  primaryDark: '#2a3f5f',
  primaryLight: '#3a537b',
  accent: '#3a8dde',
  partner: '#e74c3c',
  loyal: '#f39c12',
  regular: '#2ecc71',
  architect: '#8e44ad',
  hotel: '#16a085',
  jline: '#3498db',
  location: '#27ae60'
};

const ClientManagement = () => {
  // États
  const [activeTab, setActiveTab] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(6);
  const [showModal, setShowModal] = useState(false);
  const [modalAction, setModalAction] = useState('add');
  const [currentClient, setCurrentClient] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    category: 'regular',
    partnerType: '',
    company: '',
    location: 'Tunis',
    sellerGroup: '',
    sellerBrand: ''
  });

  const [clients, setClients] = useState([]);
  const [clientGroups, setClientGroups] = useState([]);

  // Load clients from API
  const loadClients = async () => {
    try {
      setLoading(true);
      setError('');

      const params = {
        page: currentPage,
        per_page: itemsPerPage
      };

      if (searchTerm) {
        params.search = searchTerm;
      }

      const response = await fetchClients(params);
      console.log('API Response:', response);

      // Handle different response structures
      if (response.data) {
        setClients(response.data);
      } else if (Array.isArray(response)) {
        setClients(response);
      } else {
        setClients([]);
      }
    } catch (err) {
      console.error('Error loading clients:', err);
      setError('Erreur lors du chargement des clients: ' + err.message);
      setClients([]);
    } finally {
      setLoading(false);
    }
  };

  // Load client groups from API
  const loadClientGroups = async () => {
    try {
      const response = await fetchClientGroups();
      if (response.data) {
        setClientGroups(response.data);
      } else if (Array.isArray(response)) {
        setClientGroups(response);
      }
    } catch (err) {
      console.error('Error loading client groups:', err);
    }
  };

  // Load data on component mount and when dependencies change
  useEffect(() => {
    loadClients();
  }, [currentPage, searchTerm]);

  useEffect(() => {
    loadClientGroups();
  }, []);

  // Filtrer les clients selon l'onglet actif et la recherche
  const filteredClients = clients.filter((client) => {
    const matchesCategory =
      activeTab === 'all' ||
      (activeTab === 'jline' ? (client.seller_group || client.sellerGroup) === 'jihenLine GROUPE' : activeTab === client.category);

    const matchesSearch =
      searchTerm === '' ||
      (client.name && client.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (client.email && client.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (client.company && client.company.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (client.location && client.location.toLowerCase().includes(searchTerm.toLowerCase())) ||
      ((client.seller_group || client.sellerGroup) &&
        (client.seller_group || client.sellerGroup).toLowerCase().includes(searchTerm.toLowerCase())) ||
      ((client.seller_brand || client.sellerBrand) &&
        (client.seller_brand || client.sellerBrand).toLowerCase().includes(searchTerm.toLowerCase()));

    return matchesCategory && matchesSearch;
  });

  // Pagination
  const indexOfLastClient = currentPage * itemsPerPage;
  const indexOfFirstClient = indexOfLastClient - itemsPerPage;
  const currentClients = filteredClients.slice(indexOfFirstClient, indexOfLastClient);
  const totalPages = Math.ceil(filteredClients.length / itemsPerPage);

  // Obtenir le libellé de la catégorie
  const getCategoryLabel = (category) => {
    switch (category) {
      case 'partner':
        return 'Partenaire';
      case 'loyal':
        return 'Client fidèle';
      case 'regular':
        return 'Client simple';
      default:
        return '';
    }
  };

  // Obtenir la couleur de la catégorie
  const getCategoryColor = (category, partnerType = '', sellerGroup = '') => {
    const group = sellerGroup || '';
    const pType = partnerType || '';

    if (group === 'jihenLine GROUPE') return colors.jline;

    if (category === 'partner' && pType) {
      switch (pType) {
        case 'architect':
          return colors.architect;
        case 'hotel':
          return colors.hotel;
        default:
          return colors.partner;
      }
    }

    switch (category) {
      case 'partner':
        return colors.partner;
      case 'loyal':
        return colors.loyal;
      case 'regular':
        return colors.regular;
      default:
        return '';
    }
  };

  // Obtenir l'icône de la catégorie
  const getCategoryIcon = (category, partnerType = '', sellerGroup = '') => {
    if (sellerGroup === 'jihenLine GROUPE') {
      return <i className="fas fa-store" style={{ color: colors.jline, fontSize: '1.5rem' }}></i>;
    }

    if (category === 'partner' && partnerType) {
      switch (partnerType) {
        case 'architect':
          return <i className="fas fa-drafting-compass" style={{ color: colors.architect, fontSize: '1.5rem' }}></i>;
        case 'hotel':
          return <i className="fas fa-hotel" style={{ color: colors.hotel, fontSize: '1.5rem' }}></i>;
        default:
          return <i className="fas fa-handshake" style={{ color: colors.partner, fontSize: '1.5rem' }}></i>;
      }
    }

    switch (category) {
      case 'partner':
        return <i className="fas fa-handshake" style={{ color: colors.partner, fontSize: '1.5rem' }}></i>;
      case 'loyal':
        return <i className="fas fa-star" style={{ color: colors.loyal, fontSize: '1.5rem' }}></i>;
      default:
        return null;
    }
  };

  // Obtenir le libellé complet de la catégorie (avec type de partenaire si applicable)
  const getFullCategoryLabel = (client) => {
    const sellerGroup = client.seller_group || client.sellerGroup;
    const partnerType = client.partner_type || client.partnerType;

    if (sellerGroup === 'jihenLine GROUPE') {
      return 'jihenLine GROUPE';
    }

    let label = getCategoryLabel(client.category);

    if (client.category === 'partner' && partnerType) {
      switch (partnerType) {
        case 'architect':
          return `${label} (Architecte)`;
        case 'hotel':
          return `${label} (Hôtel)`;
        default:
          return label;
      }
    }

    return label;
  };

  // Gérer l'ouverture du modal pour ajouter un client
  const handleAddClient = () => {
    setModalAction('add');
    setFormData({
      name: '',
      email: '',
      phone: '',
      category: 'regular',
      partnerType: '',
      company: '',
      location: 'Tunis',
      sellerGroup: '',
      sellerBrand: ''
    });
    setShowModal(true);
  };

  // Gérer l'ouverture du modal pour modifier un client
  const handleEditClient = (client) => {
    setModalAction('edit');
    setCurrentClient(client);
    setFormData({
      name: client.name || '',
      email: client.email || '',
      phone: client.phone || '',
      category: client.category || 'regular',
      partnerType: client.partner_type || client.partnerType || '',
      company: client.company || '',
      location: client.location || 'Tunis',
      sellerGroup: client.seller_group || client.sellerGroup || '',
      sellerBrand: client.seller_brand || client.sellerBrand || ''
    });
    setShowModal(true);
  };

  // Gérer l'ouverture du modal pour supprimer un client
  const handleDeleteClient = (client) => {
    setModalAction('delete');
    setCurrentClient(client);
    setShowModal(true);
  };

  // Gérer la soumission du formulaire
  const handleFormSubmit = async () => {
    try {
      setSubmitting(true);
      setError('');

      if (modalAction === 'add') {
        const clientData = {
          name: formData.name,
          email: formData.email,
          phone: formData.phone,
          category: formData.category,
          partner_type: formData.partnerType,
          company: formData.company,
          location: formData.location,
          seller_group: formData.sellerGroup,
          seller_brand: formData.sellerBrand
        };

        await createClient(clientData);
        await loadClients(); // Reload the list
      } else if (modalAction === 'edit' && currentClient) {
        const clientData = {
          name: formData.name,
          email: formData.email,
          phone: formData.phone,
          category: formData.category,
          partner_type: formData.partnerType,
          company: formData.company,
          location: formData.location,
          seller_group: formData.sellerGroup,
          seller_brand: formData.sellerBrand
        };

        await updateClient(currentClient.id, clientData);
        await loadClients(); // Reload the list
      } else if (modalAction === 'delete' && currentClient) {
        await deleteClient(currentClient.id);
        await loadClients(); // Reload the list
      }

      setShowModal(false);
    } catch (err) {
      console.error('Error submitting form:', err);
      setError('Erreur lors de la sauvegarde: ' + err.message);
    } finally {
      setSubmitting(false);
    }
  };

  // Gérer le changement de page
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  // Effet pour réinitialiser la page lors des filtres
  useEffect(() => {
    setCurrentPage(1);
  }, [activeTab, searchTerm]);

  // Gérer le changement des champs du formulaire
  const handleFormChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  return (
    <Container className="py-4" style={fontStyle}>
      {/* Titre principal */}
      <div className="text-center mb-5 position-relative">
        <h1
          className="mb-3"
          style={{
            fontSize: '2.2rem',
            fontWeight: 600,
            color: colors.primaryDark,
            letterSpacing: '1px'
          }}
        >
          GESTION DES CLIENTS
        </h1>
        <div
          className="mx-auto"
          style={{
            height: '3px',
            width: '120px',
            background: 'linear-gradient(90deg, rgba(58,83,155,0.2) 0%, rgba(58,83,155,1) 50%, rgba(58,83,155,0.2) 100%)',
            borderRadius: '3px'
          }}
        />
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="danger" dismissible onClose={() => setError('')} className="mb-4">
          <Alert.Heading>Erreur</Alert.Heading>
          <p>{error}</p>
        </Alert>
      )}

      {/* Barre de recherche et bouton d'ajout */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <Form.Group style={{ width: '300px' }}>
          <Form.Control
            type="text"
            placeholder="Rechercher un client..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            disabled={loading}
            style={{
              borderRadius: '20px',
              padding: '10px 20px'
            }}
          />
        </Form.Group>

        <div className="d-flex align-items-center">
          <Badge bg="light" text="dark" className="me-3">
            {loading ? 'Chargement...' : `${filteredClients.length} client(s) trouvé(s)`}
          </Badge>

          <Button variant="primary" onClick={handleAddClient} disabled={loading}>
            <i className="fas fa-plus me-2"></i> Ajouter un client
          </Button>
        </div>
      </div>

      {/* Onglets de catégories */}
      <Tabs
        activeKey={activeTab}
        onSelect={(k) => setActiveTab(k)}
        className="mb-4"
        style={{
          borderBottom: '2px solid #dee2e6'
        }}
      >
        <Tab eventKey="all" title={<span style={fontStyle}>Tous les clients</span>} />
        <Tab eventKey="partner" title={<span style={fontStyle}>Partenaires</span>} />
        <Tab eventKey="loyal" title={<span style={fontStyle}>Clients fidèles</span>} />
        <Tab eventKey="regular" title={<span style={fontStyle}>Clients simples</span>} />
        <Tab eventKey="jline" title={<span style={fontStyle}>jihenLine GROUPES</span>} />
      </Tabs>

      {/* Loading Spinner */}
      {loading && (
        <div className="text-center py-5">
          <Spinner animation="border" role="status" style={{ color: colors.primaryDark }}>
            <span className="visually-hidden">Chargement...</span>
          </Spinner>
          <div className="mt-3" style={{ color: colors.primaryDark }}>
            Chargement des clients...
          </div>
        </div>
      )}

      {/* Liste des clients */}
      {!loading && currentClients.length > 0 ? (
        <>
          <Row className="g-4">
            {currentClients.map((client) => (
              <Col key={client.id} xs={12} md={6} lg={4}>
                <Card className="h-100 border-0 shadow-sm">
                  <Card.Body>
                    <div className="d-flex justify-content-between align-items-start mb-3">
                      <div>
                        <Card.Title
                          style={{
                            fontSize: '1.2rem',
                            fontWeight: 600,
                            color: colors.primaryDark,
                            marginBottom: '0.5rem'
                          }}
                        >
                          {client.name}
                        </Card.Title>
                        <Badge
                          style={{
                            backgroundColor: getCategoryColor(
                              client.category,
                              client.partner_type || client.partnerType,
                              client.seller_group || client.sellerGroup
                            ),
                            fontSize: '0.75rem',
                            padding: '0.35em 0.65em'
                          }}
                        >
                          {getFullCategoryLabel(client)}
                        </Badge>
                        <Badge
                          className="ms-2"
                          style={{
                            backgroundColor: colors.location,
                            fontSize: '0.75rem',
                            padding: '0.35em 0.65em'
                          }}
                        >
                          {client.location}
                        </Badge>
                      </div>
                      {getCategoryIcon(
                        client.category,
                        client.partner_type || client.partnerType,
                        client.seller_group || client.sellerGroup
                      )}
                    </div>

                    <div className="mb-3">
                      <div className="d-flex align-items-center mb-2">
                        <i className="fas fa-envelope me-2" style={{ color: colors.accent }}></i>
                        <span>{client.email}</span>
                      </div>
                      <div className="d-flex align-items-center mb-2">
                        <i className="fas fa-phone me-2" style={{ color: colors.accent }}></i>
                        <span>{client.phone}</span>
                      </div>
                      {client.company && (
                        <div className="d-flex align-items-center mb-2">
                          <i className="fas fa-building me-2" style={{ color: colors.accent }}></i>
                          <span>{client.company}</span>
                        </div>
                      )}
                      {(client.seller_group || client.sellerGroup) && (
                        <div className="d-flex align-items-center mb-2">
                          <i className="fas fa-store me-2" style={{ color: colors.accent }}></i>
                          <span>{client.seller_group || client.sellerGroup}</span>
                        </div>
                      )}
                      {(client.seller_brand || client.sellerBrand) && (
                        <div className="d-flex align-items-center mb-2">
                          <i className="fas fa-tag me-2" style={{ color: colors.accent }}></i>
                          <span>{client.seller_brand || client.sellerBrand}</span>
                        </div>
                      )}
                    </div>

                    <div className="d-flex justify-content-between small text-muted">
                      <div>
                        <i className="fas fa-shopping-cart me-1"></i>
                        {client.total_purchases || client.totalPurchases || 0} achat(s)
                      </div>
                      <div>
                        Dernier achat:{' '}
                        {client.last_purchase || client.lastPurchase
                          ? new Date(client.last_purchase || client.lastPurchase).toLocaleDateString()
                          : 'Aucun'}
                      </div>
                    </div>
                  </Card.Body>
                  <Card.Footer className="bg-white border-0">
                    <div className="d-flex justify-content-end">
                      <Button variant="outline-primary" size="sm" className="me-2">
                        <i className="fas fa-eye me-1"></i> Voir
                      </Button>
                      <Button variant="outline-secondary" size="sm" className="me-2" onClick={() => handleEditClient(client)}>
                        <i className="fas fa-edit me-1"></i> Modifier
                      </Button>
                      <Button variant="outline-danger" size="sm" onClick={() => handleDeleteClient(client)}>
                        <i className="fas fa-trash-alt me-1"></i> Supprimer
                      </Button>
                    </div>
                  </Card.Footer>
                </Card>
              </Col>
            ))}
          </Row>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="d-flex justify-content-center mt-4">
              <Pagination>
                <Pagination.First onClick={() => handlePageChange(1)} disabled={currentPage === 1} />
                <Pagination.Prev onClick={() => handlePageChange(currentPage - 1)} disabled={currentPage === 1} />

                {Array.from({ length: totalPages }, (_, i) => i + 1).map((pageNumber) => (
                  <Pagination.Item key={pageNumber} active={pageNumber === currentPage} onClick={() => handlePageChange(pageNumber)}>
                    {pageNumber}
                  </Pagination.Item>
                ))}

                <Pagination.Next onClick={() => handlePageChange(currentPage + 1)} disabled={currentPage === totalPages} />
                <Pagination.Last onClick={() => handlePageChange(totalPages)} disabled={currentPage === totalPages} />
              </Pagination>
            </div>
          )}
        </>
      ) : !loading ? (
        <Card className="text-center py-5 border-0 shadow-sm">
          <Card.Body>
            <i className="fas fa-users-slash mb-3" style={{ fontSize: '3rem', color: colors.primaryLight }}></i>
            <Card.Title style={{ color: colors.primaryLight }}>Aucun client trouvé</Card.Title>
            <Card.Text className="text-muted">
              {searchTerm ? "Essayez avec d'autres termes de recherche" : 'Ajoutez votre premier client'}
            </Card.Text>
            <Button variant="primary" className="mt-3" onClick={handleAddClient}>
              <i className="fas fa-plus me-2"></i> Ajouter un client
            </Button>
          </Card.Body>
        </Card>
      ) : null}

      {/* Professional Modal for Add/Edit */}
      {modalAction !== 'delete' && (
        <FormModal
          show={showModal}
          onHide={() => setShowModal(false)}
          onSubmit={handleFormSubmit}
          title={modalAction === 'add' ? 'Ajouter un client' : 'Modifier le client'}
          isEdit={modalAction === 'edit'}
          loading={submitting}
          size="md"
        >
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Nom complet</Form.Label>
              <Form.Control type="text" name="name" value={formData.name} onChange={handleFormChange} required />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Email</Form.Label>
              <Form.Control type="email" name="email" value={formData.email} onChange={handleFormChange} required />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Téléphone</Form.Label>
              <Form.Control type="text" name="phone" value={formData.phone} onChange={handleFormChange} required />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Entreprise</Form.Label>
              <Form.Control type="text" name="company" value={formData.company} onChange={handleFormChange} />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Catégorie</Form.Label>
              <Form.Select name="category" value={formData.category} onChange={handleFormChange}>
                <option value="regular">Client simple</option>
                <option value="loyal">Client fidèle</option>
                <option value="partner">Partenaire</option>
              </Form.Select>
            </Form.Group>

            {formData.category === 'partner' && (
              <Form.Group className="mb-3">
                <Form.Label>Type de partenaire</Form.Label>
                <Form.Select name="partnerType" value={formData.partnerType} onChange={handleFormChange}>
                  <option value="">Général</option>
                  <option value="architect">Architecte</option>
                  <option value="hotel">Hôtel</option>
                </Form.Select>
              </Form.Group>
            )}

            <Form.Group className="mb-3">
              <Form.Label>Ville</Form.Label>
              <Form.Select name="location" value={formData.location} onChange={handleFormChange}>
                <option value="Tunis">Tunis</option>
                <option value="Sfax">Sfax</option>
                <option value="Sousse">Sousse</option>
                <option value="Monastir">Monastir</option>
                <option value="Hammamet">Hammamet</option>
              </Form.Select>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Groupe</Form.Label>
              <Form.Select name="sellerGroup" value={formData.sellerGroup} onChange={handleFormChange}>
                <option value="">Aucun</option>
                <option value="jihenLine GROUPE">jihenLine GROUPE</option>
              </Form.Select>
            </Form.Group>

            {formData.sellerGroup === 'jihenLine GROUPE' && (
              <Form.Group className="mb-3">
                <Form.Label>Marque</Form.Label>
                <Form.Select name="sellerBrand" value={formData.sellerBrand} onChange={handleFormChange}>
                  <option value="">Sélectionner une marque</option>
                  <option value="Luxoria">Luxoria</option>
                  <option value="Modernio">Modernio</option>
                  <option value="Eleganto">Eleganto</option>
                </Form.Select>
              </Form.Group>
            )}
          </Form>
        </FormModal>
      )}

      {/* Professional Confirmation Modal for Delete */}
      {modalAction === 'delete' && (
        <ConfirmationModal
          show={showModal}
          onHide={() => setShowModal(false)}
          onConfirm={handleFormSubmit}
          title="Supprimer le client"
          message={
            <>
              Êtes-vous sûr de vouloir supprimer le client <strong>{currentClient?.name}</strong> ?
              <br />
              <small className="text-muted">Cette action est irréversible.</small>
            </>
          }
          confirmText="Supprimer"
          cancelText="Annuler"
          variant="danger"
          loading={submitting}
        />
      )}
    </Container>
  );
};

export default ClientManagement;
