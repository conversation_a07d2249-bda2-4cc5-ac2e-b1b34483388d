import { useEffect, useState } from 'react';
import { Box, Button, Typography, Paper, Container } from '@mui/material';
import { redirectToKeycloakLogin } from '../../../utils/keycloak';

// ================================|| KEYCLOAK LOGIN ||================================ //

export default function Login() {
  const [userLoggedOut, setUserLoggedOut] = useState(false);

  useEffect(() => {
    // Check if user just logged out
    const loggedOut = sessionStorage.getItem('user_logged_out') === 'true';

    if (loggedOut) {
      console.log('🔐 User just logged out - showing logout confirmation');
      setUserLoggedOut(true);
      // Clear the logout flag so it doesn't persist
      sessionStorage.removeItem('user_logged_out');
    } else {
      console.log('🔐 Login page accessed - redirecting to Keycloak immediately');
      redirectToKeycloakLogin();
    }
  }, []);

  const handleLoginClick = () => {
    console.log('🔐 User clicked login button - redirecting to Keycloak');
    redirectToKeycloakLogin();
  };

  // Show logout confirmation if user just logged out
  if (userLoggedOut) {
    return (
      <Container maxWidth="sm" sx={{ mt: 8 }}>
        <Paper elevation={3} sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h4" gutterBottom color="primary">
            Logout Successful
          </Typography>
          <Typography variant="body1" sx={{ mb: 3 }}>
            You have been successfully logged out from the system.
          </Typography>
          <Button variant="contained" size="large" onClick={handleLoginClick} sx={{ mt: 2 }}>
            Login Again
          </Button>
        </Paper>
      </Container>
    );
  }

  // This component should never render for normal login flow since we redirect immediately
  return null;
}
