import React, { useState, useEffect, useMemo, useCallback, memo } from 'react';
import {
  Container,
  Card,
  Button,
  Form,
  Alert,
  Spinner,
  Badge,
  Row,
  Col,
  Breadcrumb,
  InputGroup,
  Dropdown,
  OverlayTrigger,
  Tooltip,
  Tabs,
  Tab
} from 'react-bootstrap';
import ProfessionalModal from '../../ui-component/extended/ProfessionalModal';
import { FormModal, ConfirmationModal, InfoModal } from '../../ui-component/extended/ModalVariants';
import { Box, Typography, TextField, InputAdornment, Chip, IconButton } from '@mui/material';
import {
  FaPlus,
  FaPencilAlt,
  FaTrashAlt,
  FaEye,
  FaHome,
  FaBox,
  FaSearch,
  FaFilter,
  FaTags,
  FaTag,
  FaLayerGroup,
  FaSort,
  FaSortUp,
  FaSortDown,
  FaInfoCircle
} from 'react-icons/fa';

// Standardized components
import StandardTable from '../../components/StandardTable';
import StandardCard from '../../components/StandardCard';
import StandardButton from '../../components/StandardButton';
import MainCard from '../../ui-component/cards/MainCard';

// Design system
import { COLORS, TYPOGRAPHY } from '../../themes/designSystem';
import { fetchProducts, createProduct, updateProduct, deleteProduct, searchProducts } from '../../services/productService';
import { fetchBrands } from '../../services/brandService';
import { fetchCategories, fetchAllSousCategories, fetchAllSousSousCategories, fetchModelImages } from '../../services/categoryService';
import { fetchProductAttributs, fetchSousSousCategorieAttributs } from '../../services/attributService';
import axios from 'axios';
import TablePagination from 'components/TablePagination';
import 'ui-component/extended/ProfessionalModal.css';

// ⚡ PERFORMANCE FIX: Memoized Lazy Image Component (outside main component to prevent recreation)
const LazyProductImage = memo(({ productId, productName, productImages, onImageLoad, className = '', style = {} }) => {
  const [imageUrl, setImageUrl] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);

  useEffect(() => {
    // Check if image is already cached
    const cachedImages = productImages[productId];
    if (cachedImages && cachedImages.length > 0) {
      const primaryImage = cachedImages.find((img) => img.is_primary) || cachedImages[0];
      const url = primaryImage?.thumbnail_medium || primaryImage?.direct_url;
      if (url !== imageUrl) {
        setImageUrl(url);
        setHasLoaded(true);
      }
      return;
    }

    // Load image if not cached and not already loading
    if (!hasLoaded && !isLoading) {
      setIsLoading(true);

      fetchModelImages('produit', productId)
        .then((images) => {
          // Notify parent component to update cache
          if (onImageLoad) {
            onImageLoad(productId, images);
          }

          const primaryImage = images.find((img) => img.is_primary) || images[0];
          const url = primaryImage?.thumbnail_medium || primaryImage?.direct_url;
          setImageUrl(url);
          setHasLoaded(true);
        })
        .catch((error) => {
          console.error(`Error loading image for product ${productId}:`, error);
          setHasLoaded(true); // Mark as loaded even on error to prevent retry
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [productId, productImages, hasLoaded, isLoading, imageUrl, onImageLoad]);

  if (isLoading) {
    return (
      <div
        className={`d-flex align-items-center justify-content-center bg-light rounded border ${className}`}
        style={{ width: '60px', height: '60px', ...style }}
      >
        <Spinner animation="border" size="sm" variant="primary" />
      </div>
    );
  }

  if (imageUrl) {
    return (
      <img
        src={imageUrl}
        alt={productName}
        className={`rounded border ${className}`}
        style={{ width: '60px', height: '60px', objectFit: 'cover', ...style }}
        onError={() => setImageUrl(null)} // Handle broken images
      />
    );
  }

  return (
    <div
      className={`d-flex align-items-center justify-content-center bg-light rounded border ${className}`}
      style={{ width: '60px', height: '60px', ...style }}
    >
      <FaBox className="text-muted" size={24} />
    </div>
  );
});

LazyProductImage.displayName = 'LazyProductImage';

const ProductManagement = () => {
  // State management
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [brands, setBrands] = useState([]);
  const [categories, setCategories] = useState([]);
  const [sousCategories, setSousCategories] = useState([]);
  const [sousSousCategories, setSousSousCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false); // Separate loading state for form submission
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Modal states
  const [showModal, setShowModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [modalAction, setModalAction] = useState('create'); // 'create' or 'edit'
  const [currentProduct, setCurrentProduct] = useState(null);
  const [productToDelete, setProductToDelete] = useState(null);
  const [productToView, setProductToView] = useState(null);
  const [activeTab, setActiveTab] = useState('basic'); // 'basic', 'images', 'attributes', 'variants'

  // Filter and search states - Restored for actual API structure
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBrand, setSelectedBrand] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedSousCategory, setSelectedSousCategory] = useState('');
  const [selectedSousSousCategory, setSelectedSousSousCategory] = useState('');
  const [stockFilter, setStockFilter] = useState(''); // 'in_stock', 'out_of_stock', ''
  const [priceRange, setPriceRange] = useState({ min: '', max: '' });
  const [sortField, setSortField] = useState('nom_produit'); // Restored to actual API field name
  const [sortDirection, setSortDirection] = useState('asc');

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Form data - Updated to match actual API response structure
  const [formData, setFormData] = useState({
    nom_produit: '',
    description_produit: '',
    prix_produit: '',
    quantite_produit: '',
    reference: '',
    marque_id: '',
    sous_sous_categorie_id: '', // API actually uses 3-level hierarchy
    // UI-only fields for category hierarchy (not sent to API)
    categorie_id: '',
    sous_categorie_id: ''
  });

  // Image states - Multiple images support
  const [images, setImages] = useState([]);
  const [primaryImageIndex, setPrimaryImageIndex] = useState(0);

  // Attributes states
  const [availableAttributes, setAvailableAttributes] = useState([]);
  const [attributeValues, setAttributeValues] = useState({});

  // Variants states
  const [variants, setVariants] = useState([]);
  const [showVariantModal, setShowVariantModal] = useState(false);
  const [newVariant, setNewVariant] = useState({
    sku: '',
    prix_supplement: 0,
    stock: 0,
    attributs: {}
  });

  // Filtered categories for modal hierarchy - Restored 3-level hierarchy
  const [filteredSousCategories, setFilteredSousCategories] = useState([]);
  const [filteredSousSousCategories, setFilteredSousSousCategories] = useState([]);

  // Product images state
  const [productImages, setProductImages] = useState({});
  const [existingImages, setExistingImages] = useState([]);
  const [updatingImageId, setUpdatingImageId] = useState(null);

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  // Auto-hide success messages after 5 seconds
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => {
        setSuccess('');
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [success]);

  // ⚡ PERFORMANCE FIX: Debounced filtering to prevent excessive re-renders
  const debouncedApplyFilters = useCallback(() => {
    const timeoutId = setTimeout(() => {
      applyFilters();
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [
    products,
    searchTerm,
    selectedBrand,
    selectedCategory,
    selectedSousCategory,
    selectedSousSousCategory,
    stockFilter,
    priceRange,
    sortField,
    sortDirection
  ]);

  useEffect(() => {
    const cleanup = debouncedApplyFilters();
    return cleanup;
  }, [debouncedApplyFilters]);

  // Filter sous-categories when category changes in modal - Restored for 3-level hierarchy
  useEffect(() => {
    console.log('🔄 Category changed:', formData.categorie_id);
    console.log('📋 Available sous-categories:', sousCategories.length);

    if (formData.categorie_id) {
      const filtered = sousCategories.filter((sc) => sc.categorie_id === parseInt(formData.categorie_id));
      console.log('✅ Filtered sous-categories:', filtered.length);
      setFilteredSousCategories(filtered);
    } else {
      setFilteredSousCategories([]);
    }
    // Reset dependent fields when category changes
    if (formData.categorie_id) {
      setFormData((prev) => ({ ...prev, sous_categorie_id: '', sous_sous_categorie_id: '' }));
      setFilteredSousSousCategories([]);
    }
  }, [formData.categorie_id, sousCategories]);

  // Filter sous-sous-categories when sous-category changes in modal
  useEffect(() => {
    console.log('🔄 Sous-category changed:', formData.sous_categorie_id);
    console.log('📋 Available sous-sous-categories:', sousSousCategories.length);

    if (formData.sous_categorie_id) {
      const filtered = sousSousCategories.filter((ssc) => ssc.sous_categorie_id === parseInt(formData.sous_categorie_id));
      console.log('✅ Filtered sous-sous-categories:', filtered.length);
      setFilteredSousSousCategories(filtered);
    } else {
      setFilteredSousSousCategories([]);
    }
    // Reset dependent field when sous-category changes
    if (formData.sous_categorie_id) {
      setFormData((prev) => ({ ...prev, sous_sous_categorie_id: '' }));
    }
  }, [formData.sous_categorie_id, sousSousCategories]);

  // Load attributes when sous-sous-category changes
  useEffect(() => {
    if (formData.sous_sous_categorie_id) {
      loadAttributesForCategory(formData.sous_sous_categorie_id);
    } else {
      setAvailableAttributes([]);
      setAttributeValues({});
    }
  }, [formData.sous_sous_categorie_id]);

  // Function to fetch images for products
  const fetchProductImages = async (products) => {
    const imagePromises = products.map(async (product) => {
      try {
        const images = await fetchModelImages('produit', product.id);
        return { productId: product.id, images };
      } catch (error) {
        console.error(`Error fetching images for product ${product.id}:`, error);
        return { productId: product.id, images: [] };
      }
    });

    const imageResults = await Promise.all(imagePromises);
    const imageMap = {};
    imageResults.forEach(({ productId, images }) => {
      imageMap[productId] = images;
    });

    setProductImages(imageMap);
  };

  // ⚡ PERFORMANCE FIX: Callback for image loading from LazyProductImage
  const handleImageLoad = useCallback((productId, images) => {
    setProductImages((prev) => ({
      ...prev,
      [productId]: images
    }));
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      setError('');

      console.log('🔄 Loading initial data...');
      console.log('🌐 API URLs being used:');
      console.log('- Products API:', import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api');
      console.log('- Environment:', import.meta.env.MODE);

      // Load data with individual error handling
      const results = await Promise.allSettled([
        fetchProducts(),
        fetchBrands(),
        fetchCategories(),
        fetchAllSousCategories(),
        fetchAllSousSousCategories()
      ]);

      const [productsRes, brandsRes, categoriesRes, sousCategoriesRes, sousSousCategoriesRes] = results;

      // Handle products
      if (productsRes.status === 'fulfilled') {
        const products = productsRes.value.data || productsRes.value;
        const productArray = Array.isArray(products) ? products : [];
        setProducts(productArray);
        console.log('✅ Products loaded:', productArray.length);
        if (productArray.length > 0) {
          console.log('📋 Sample product structure:', productArray[0]);
          // ⚠️ PERFORMANCE FIX: Don't load all images at once to prevent freezing
          console.log('🖼️ Skipping mass image loading to prevent UI freeze');
          console.log('💡 Images will be loaded on-demand when needed');

          // ⚡ PERFORMANCE OPTIMIZATION: Pre-load images for first page only
          setTimeout(() => {
            const firstPageProducts = productArray.slice(0, itemsPerPage);
            console.log(`🖼️ Pre-loading images for first ${firstPageProducts.length} products`);
            firstPageProducts.forEach((product, index) => {
              // Stagger the loading to prevent overwhelming the API
              setTimeout(() => {
                if (!productImages[product.id]) {
                  fetchModelImages('produit', product.id)
                    .then((images) => {
                      setProductImages((prev) => ({
                        ...prev,
                        [product.id]: images
                      }));
                    })
                    .catch((error) => {
                      console.error(`Error pre-loading image for product ${product.id}:`, error);
                    });
                }
              }, index * 100); // 100ms delay between each request
            });
          }, 1000); // Wait 1 second after page load
        }
      } else {
        console.error('❌ Failed to load products:', productsRes.reason);
        setProducts([]);
      }

      // Handle brands
      if (brandsRes.status === 'fulfilled') {
        const brands = brandsRes.value.data || brandsRes.value;
        setBrands(Array.isArray(brands) ? brands : []);
        console.log('✅ Brands loaded:', brands.length);
      } else {
        console.error('❌ Failed to load brands:', brandsRes.reason);
        setBrands([]);
      }

      // Handle categories
      if (categoriesRes.status === 'fulfilled') {
        const categories = categoriesRes.value.data || categoriesRes.value;
        setCategories(Array.isArray(categories) ? categories : []);
        console.log('✅ Categories loaded:', categories.length);
        if (categories.length > 0) {
          console.log('📋 Sample category structure:', categories[0]);
        }
      } else {
        console.error('❌ Failed to load categories:', categoriesRes.reason);
        setCategories([]);
      }

      // Handle sous-categories
      if (sousCategoriesRes.status === 'fulfilled') {
        const sousCategories = sousCategoriesRes.value.data || sousCategoriesRes.value;
        setSousCategories(Array.isArray(sousCategories) ? sousCategories : []);
        console.log('✅ Sous-categories loaded:', sousCategories.length);
        if (sousCategories.length > 0) {
          console.log('📋 Sample sous-category structure:', sousCategories[0]);
        }
      } else {
        console.error('❌ Failed to load sous-categories:', sousCategoriesRes.reason);
        setSousCategories([]);
      }

      // Handle sous-sous-categories
      if (sousSousCategoriesRes.status === 'fulfilled') {
        const sousSousCategories = sousSousCategoriesRes.value.data || sousSousCategoriesRes.value;
        setSousSousCategories(Array.isArray(sousSousCategories) ? sousSousCategories : []);
        console.log('✅ Sous-sous-categories loaded:', sousSousCategories.length);
        if (sousSousCategories.length > 0) {
          console.log('📋 Sample sous-sous-category structure:', sousSousCategories[0]);
        }
      } else {
        console.error('❌ Failed to load sous-sous-categories:', sousSousCategoriesRes.reason);
        setSousSousCategories([]);
      }

      // Check if any critical data failed to load
      const failedRequests = results.filter((result) => result.status === 'rejected');
      if (failedRequests.length > 0) {
        const errorMessages = failedRequests.map((result) => result.reason?.message || 'Erreur inconnue');
        console.warn('⚠️ Some data failed to load:', errorMessages);

        // Only show error if products failed to load (critical)
        if (productsRes.status === 'rejected') {
          setError(`Impossible de charger les produits: ${productsRes.reason?.message || 'Erreur inconnue'}`);
        } else if (failedRequests.length === results.length) {
          setError(`Toutes les données ont échoué à charger. Vérifiez votre connexion.`);
        } else {
          setError(`Certaines données n'ont pas pu être chargées: ${errorMessages.join(', ')}`);
        }
      }

      console.log('✅ Initial data loading completed');
    } catch (err) {
      console.error('❌ Critical error loading data:', err);
      setError(`Erreur critique lors du chargement des données: ${err.message}`);

      // Fallback: Set empty arrays to prevent crashes
      setProducts([]);
      setBrands([]);
      setCategories([]);
      setSousCategories([]);
      setSousSousCategories([]);
    } finally {
      setLoading(false);
    }
  };

  // ⚡ PERFORMANCE FIX: Memoized category mappings to avoid recalculation
  const categoryMappings = useMemo(() => {
    const sousIdsByCategory = new Map();
    const sousSousIdsBySous = new Map();
    const sousSousIdsByCategory = new Map();

    // Pre-calculate mappings
    sousCategories.forEach((sc) => {
      if (!sousIdsByCategory.has(sc.categorie_id)) {
        sousIdsByCategory.set(sc.categorie_id, []);
      }
      sousIdsByCategory.get(sc.categorie_id).push(sc.id);
    });

    sousSousCategories.forEach((ssc) => {
      if (!sousSousIdsBySous.has(ssc.sous_categorie_id)) {
        sousSousIdsBySous.set(ssc.sous_categorie_id, []);
      }
      sousSousIdsBySous.get(ssc.sous_categorie_id).push(ssc.id);
    });

    // Calculate sous-sous-categories by main category
    sousIdsByCategory.forEach((sousIds, categoryId) => {
      const allSousSousIds = [];
      sousIds.forEach((sousId) => {
        const sousSousIds = sousSousIdsBySous.get(sousId) || [];
        allSousSousIds.push(...sousSousIds);
      });
      sousSousIdsByCategory.set(categoryId, allSousSousIds);
    });

    return { sousIdsByCategory, sousSousIdsBySous, sousSousIdsByCategory };
  }, [sousCategories, sousSousCategories]);

  // ⚡ PERFORMANCE FIX: Optimized filtering function
  const applyFilters = useCallback(() => {
    console.log('🔄 Applying filters to', products.length, 'products');
    let filtered = [...products];

    // Search filter - Optimized with early return
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (product) =>
          product.nom_produit?.toLowerCase().includes(term) ||
          product.reference?.toLowerCase().includes(term) ||
          product.description_produit?.toLowerCase().includes(term)
      );
    }

    // Brand filter - Optimized with parseInt cache
    if (selectedBrand) {
      const brandId = parseInt(selectedBrand);
      filtered = filtered.filter((product) => product.marque_id === brandId);
    }

    // Category filters - Optimized with pre-calculated mappings
    if (selectedSousSousCategory) {
      const sousSousCategoryId = parseInt(selectedSousSousCategory);
      filtered = filtered.filter((product) => product.sous_sous_categorie_id === sousSousCategoryId);
    } else if (selectedSousCategory) {
      const sousIds = categoryMappings.sousSousIdsBySous.get(parseInt(selectedSousCategory)) || [];
      if (sousIds.length > 0) {
        filtered = filtered.filter((product) => sousIds.includes(product.sous_sous_categorie_id));
      }
    } else if (selectedCategory) {
      const sousSousIds = categoryMappings.sousSousIdsByCategory.get(parseInt(selectedCategory)) || [];
      if (sousSousIds.length > 0) {
        filtered = filtered.filter((product) => sousSousIds.includes(product.sous_sous_categorie_id));
      }
    }

    // Stock filter - Optimized
    if (stockFilter === 'in_stock') {
      filtered = filtered.filter((product) => product.quantite_produit > 0);
    } else if (stockFilter === 'out_of_stock') {
      filtered = filtered.filter((product) => product.quantite_produit <= 0);
    }

    // Price range filter - Optimized with cached values
    const minPrice = priceRange.min !== '' ? parseFloat(priceRange.min) : null;
    const maxPrice = priceRange.max !== '' ? parseFloat(priceRange.max) : null;

    if (minPrice !== null) {
      filtered = filtered.filter((product) => parseFloat(product.prix_produit) >= minPrice);
    }
    if (maxPrice !== null) {
      filtered = filtered.filter((product) => parseFloat(product.prix_produit) <= maxPrice);
    }

    // Optimized sorting
    if (filtered.length > 0) {
      filtered.sort((a, b) => {
        let aValue = a[sortField];
        let bValue = b[sortField];

        // Handle numeric fields
        if (sortField === 'prix_produit' || sortField === 'quantite_produit') {
          aValue = parseFloat(aValue) || 0;
          bValue = parseFloat(bValue) || 0;
        } else {
          aValue = aValue?.toString().toLowerCase() || '';
          bValue = bValue?.toString().toLowerCase() || '';
        }

        return sortDirection === 'asc' ? (aValue > bValue ? 1 : -1) : aValue < bValue ? 1 : -1;
      });
    }

    console.log('✅ Filtered products:', filtered.length);
    setFilteredProducts(filtered);
    setCurrentPage(1);
  }, [
    products,
    searchTerm,
    selectedBrand,
    selectedCategory,
    selectedSousCategory,
    selectedSousSousCategory,
    stockFilter,
    priceRange,
    sortField,
    sortDirection,
    categoryMappings
  ]);

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const getSortIcon = (field) => {
    if (sortField !== field) return <FaSort className="text-muted" />;
    return sortDirection === 'asc' ? <FaSortUp className="text-primary" /> : <FaSortDown className="text-primary" />;
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Multiple images handling
  const handleImagesChange = (e) => {
    const files = Array.from(e.target.files);
    const newImages = files.filter((f) => !images.some((img) => img.name === f.name));
    setImages((prev) => [...prev, ...newImages]);

    // If no primary selected, set the first as primary
    if (images.length === 0 && newImages.length > 0) {
      setPrimaryImageIndex(0);
    }
  };

  const removeImage = (index) => {
    setImages((prev) => prev.filter((_, i) => i !== index));

    // Adjust primary index if needed
    if (index === primaryImageIndex) {
      setPrimaryImageIndex(0);
    } else if (index < primaryImageIndex) {
      setPrimaryImageIndex((prev) => prev - 1);
    }
  };

  const setPrimaryImage = (index) => {
    setPrimaryImageIndex(index);
  };

  // Enhanced attributes handling with new system
  const loadAttributesForCategory = async (sousSousCategorieId) => {
    if (!sousSousCategorieId) {
      setAvailableAttributes([]);
      return;
    }

    try {
      // Use the service function instead of direct fetch
      console.log('🏷️ Loading attributes for category:', sousSousCategorieId);
      const categoryAttributes = await fetchSousSousCategorieAttributs(sousSousCategorieId);
      const attributes = categoryAttributes.data || categoryAttributes || [];
      console.log('✅ Loaded attributes for category:', attributes.length);
      setAvailableAttributes(attributes);
    } catch (error) {
      console.error('Error loading attributes:', error);
      setAvailableAttributes([]);
    }
  };

  const handleAttributeChange = (attributId, value) => {
    setAttributeValues((prev) => ({
      ...prev,
      [attributId]: value
    }));
  };

  // Enhanced attribute input renderer for different types
  const renderAttributeInput = (attribute) => {
    const value = attributeValues[attribute.id] || '';
    const isRequired = attribute.obligatoire && formData.sous_sous_categorie_id;

    switch (attribute.type_valeur) {
      case 'nombre':
        return (
          <Form.Control
            name={`attribute_${attribute.id}`}
            type="number"
            step="0.01"
            value={value}
            onChange={(e) => handleAttributeChange(attribute.id, e.target.value)}
            required={isRequired}
            placeholder={`Entrez ${attribute.nom.toLowerCase()}`}
          />
        );

      case 'date':
        return (
          <Form.Control
            name={`attribute_${attribute.id}`}
            type="date"
            value={value}
            onChange={(e) => handleAttributeChange(attribute.id, e.target.value)}
            required={isRequired}
          />
        );

      case 'booleen':
        return (
          <Form.Check
            name={`attribute_${attribute.id}`}
            type="checkbox"
            checked={value === 'true' || value === true}
            onChange={(e) => handleAttributeChange(attribute.id, e.target.checked)}
            label={`Activer ${attribute.nom.toLowerCase()}`}
            required={isRequired}
          />
        );

      case 'liste':
        // For list type, you might want to fetch available options from API
        return (
          <Form.Select
            name={`attribute_${attribute.id}`}
            value={value}
            onChange={(e) => handleAttributeChange(attribute.id, e.target.value)}
            required={isRequired}
          >
            <option value="">Sélectionnez {attribute.nom.toLowerCase()}</option>
            {/* Add options here - could be fetched from API */}
            <option value="option1">Option 1</option>
            <option value="option2">Option 2</option>
          </Form.Select>
        );

      case 'texte':
      default:
        return (
          <Form.Control
            name={`attribute_${attribute.id}`}
            type="text"
            value={value}
            onChange={(e) => handleAttributeChange(attribute.id, e.target.value)}
            required={isRequired}
            placeholder={`Entrez ${attribute.nom.toLowerCase()}`}
          />
        );
    }
  };

  // Variants handling
  const addVariant = () => {
    if (!newVariant.sku) {
      setError('Le SKU est obligatoire pour la variante');
      return;
    }

    setVariants((prev) => [...prev, { ...newVariant, id: Date.now() }]);
    setNewVariant({
      sku: '',
      prix_supplement: 0,
      stock: 0,
      attributs: {}
    });
    setShowVariantModal(false);
  };

  const removeVariant = (index) => {
    setVariants((prev) => prev.filter((_, i) => i !== index));
  };

  // Function to set an existing image as primary
  const setExistingImageAsPrimary = async (imageId, productId) => {
    try {
      setUpdatingImageId(imageId);
      console.log(`🔄 Setting image ${imageId} as primary for product ${productId}`);

      // Try the alternative endpoint first (like in EditProduit.jsx)
      const token = localStorage.getItem('access_token');
      const headers = {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` })
      };

      console.log('🔑 Using auth token:', !!token);
      console.log('📋 Headers:', headers);

      // NOUVELLE APPROCHE: Gérer la logique métier côté client
      console.log('📋 Current existing images:', existingImages);

      // Étape 1: Désactiver l'image principale actuelle
      const currentPrimaryImage = existingImages.find((img) => img.is_primary);
      if (currentPrimaryImage && currentPrimaryImage.id !== imageId) {
        console.log(`🔄 Removing primary status from image ${currentPrimaryImage.id}`);
        try {
          await axios.put(`https://laravel-api.fly.dev/api/images/${currentPrimaryImage.id}`, { is_primary: false }, { headers });
          console.log('✅ Removed primary status from current image');
        } catch (removePrimaryError) {
          console.log('⚠️ Failed to remove primary status:', removePrimaryError.response?.status);
          // Continue anyway, the server might handle this automatically
        }
      }

      // Étape 2: Définir la nouvelle image comme principale
      try {
        // Try the set-primary endpoint first
        await axios.patch(
          `https://laravel-api.fly.dev/api/images/set-primary`,
          {
            model_type: 'produit',
            model_id: productId,
            image_id: imageId
          },
          { headers }
        );
        console.log('✅ Used set-primary endpoint successfully');
      } catch (setPrimaryError) {
        console.log('⚠️ set-primary failed, trying direct PUT:', setPrimaryError.response?.status);
        console.log('⚠️ set-primary error details:', setPrimaryError.response?.data);

        // Fallback to direct PUT method
        try {
          await axios.put(`https://laravel-api.fly.dev/api/images/${imageId}`, { is_primary: true }, { headers });
          console.log('✅ Used direct PUT endpoint successfully');
        } catch (directPutError) {
          console.log('⚠️ Direct PUT also failed, trying reorder endpoint:', directPutError.response?.status);
          console.log('⚠️ Direct PUT error details:', directPutError.response?.data);

          // Last resort: Use reorder endpoint to set primary
          const imageOrder = existingImages.map((img) => ({
            id: img.id,
            order: img.id === imageId ? 0 : img.order || 1,
            is_primary: img.id === imageId
          }));

          await axios.post(
            `https://laravel-api.fly.dev/api/images/reorder`,
            {
              model_type: 'produit',
              model_id: productId,
              images: imageOrder
            },
            { headers }
          );
          console.log('✅ Used reorder endpoint successfully');
        }
      }

      console.log('✅ Image set as primary successfully');
      setSuccess('Image principale mise à jour avec succès');

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(''), 3000);

      // Reload existing images to reflect the change
      if (modalAction === 'edit' && currentProduct) {
        const updatedImages = await fetchModelImages('produit', currentProduct.id);
        setExistingImages(updatedImages);
      }

      // Reload product images for the table and details modal
      const productToUpdate = currentProduct || productToView;
      if (productToUpdate) {
        await fetchProductImages([productToUpdate]);
      }
    } catch (error) {
      console.error('❌ Error setting image as primary:', error);
      console.error('❌ Error response:', error.response);
      console.error('❌ Error data:', error.response?.data);
      console.error('❌ Error status:', error.response?.status);

      const errorMessage =
        error.response?.data?.message ||
        error.response?.data?.error ||
        error.message ||
        "Erreur lors de la mise à jour de l'image principale";
      setError(`Erreur ${error.response?.status || 'API'}: ${errorMessage}`);

      // Clear error message after 5 seconds
      setTimeout(() => setError(''), 5000);
    } finally {
      setUpdatingImageId(null);
    }
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedBrand('');
    setSelectedCategory('');
    setSelectedSousCategory('');
    setSelectedSousSousCategory('');
    setStockFilter('');
    setPriceRange({ min: '', max: '' });
    setSortField('nom_produit'); // Restored for actual API field name
    setSortDirection('asc');
  };

  const handleCreate = () => {
    setModalAction('create');
    setCurrentProduct(null);
    setFormData({
      nom_produit: '',
      description_produit: '',
      prix_produit: '',
      quantite_produit: '',
      reference: '',
      marque_id: '',
      sous_sous_categorie_id: '', // Restored for actual API structure
      // UI-only fields for category hierarchy
      categorie_id: '',
      sous_categorie_id: ''
    });

    // Reset all states
    setImages([]);
    setExistingImages([]);
    setPrimaryImageIndex(0);
    setAvailableAttributes([]);
    setAttributeValues({});
    setVariants([]);
    setFilteredSousCategories([]);
    setFilteredSousSousCategories([]);
    setActiveTab('basic');
    setShowModal(true);
  };

  const handleEdit = (product) => {
    setModalAction('edit');
    setCurrentProduct(product);

    // Find the category hierarchy for this product - Restored for 3-level hierarchy
    const sousSousCategorie = sousSousCategories.find((ssc) => ssc.id === product.sous_sous_categorie_id);
    const sousCategorie = sousSousCategorie ? sousCategories.find((sc) => sc.id === sousSousCategorie.sous_categorie_id) : null;
    const categorie = sousCategorie ? categories.find((c) => c.id === sousCategorie.categorie_id) : null;

    setFormData({
      nom_produit: product.nom_produit || '',
      description_produit: product.description_produit || '',
      prix_produit: product.prix_produit || '',
      quantite_produit: product.quantite_produit || '',
      reference: product.reference || '',
      marque_id: product.marque_id || '',
      sous_sous_categorie_id: product.sous_sous_categorie_id || '',
      // UI-only fields for category hierarchy
      categorie_id: categorie?.id || '',
      sous_categorie_id: sousCategorie?.id || ''
    });

    // Set filtered options based on current selection
    if (categorie) {
      const filteredSous = sousCategories.filter((sc) => sc.categorie_id === categorie.id);
      setFilteredSousCategories(filteredSous);
    }
    if (sousCategorie) {
      const filteredSousSous = sousSousCategories.filter((ssc) => ssc.sous_categorie_id === sousCategorie.id);
      setFilteredSousSousCategories(filteredSousSous);
    }

    // Load existing images and attributes for edit
    const loadExistingData = async () => {
      try {
        // Load existing images
        const existingImages = await fetchModelImages('produit', product.id);
        setExistingImages(existingImages);
        console.log('📸 Loaded existing images for product:', existingImages.length);

        // Load existing attributes
        console.log('🏷️ Loading existing attributes for product:', product.id);
        const productAttributes = await fetchProductAttributs(product.id);
        const attributes = productAttributes.data || productAttributes || [];
        console.log('🏷️ Loaded product attributes:', attributes);

        // Initialize attribute values from existing product attributes
        const initialAttributeValues = {};
        attributes.forEach((attr) => {
          const attributeId = attr.attribut_id || attr.id;
          console.log('🔍 Processing attribute for edit:', {
            id: attributeId,
            valeur_texte: attr.valeur_texte,
            valeur_nombre: attr.valeur_nombre,
            valeur_booleen: attr.valeur_booleen,
            valeur_date: attr.valeur_date,
            valeur: attr.valeur
          });

          if (attr.valeur_texte !== null && attr.valeur_texte !== undefined) {
            initialAttributeValues[attributeId] = attr.valeur_texte;
          } else if (attr.valeur_nombre !== null && attr.valeur_nombre !== undefined) {
            initialAttributeValues[attributeId] = attr.valeur_nombre;
          } else if (attr.valeur_booleen !== null && attr.valeur_booleen !== undefined) {
            initialAttributeValues[attributeId] = attr.valeur_booleen;
          } else if (attr.valeur_date !== null && attr.valeur_date !== undefined) {
            initialAttributeValues[attributeId] = attr.valeur_date;
          } else if (attr.valeur !== null && attr.valeur !== undefined) {
            initialAttributeValues[attributeId] = attr.valeur;
          }
        });

        console.log('✅ Initial attribute values for edit:', initialAttributeValues);
        setAttributeValues(initialAttributeValues);

        // Load available attributes for the category
        if (product.sous_sous_categorie_id) {
          console.log('🏷️ Loading available attributes for category:', product.sous_sous_categorie_id);
          const categoryAttributes = await fetchSousSousCategorieAttributs(product.sous_sous_categorie_id);
          const availableAttrs = categoryAttributes.data || categoryAttributes || [];
          setAvailableAttributes(availableAttrs);
          console.log('🏷️ Loaded available attributes for category:', availableAttrs.length);
        }
      } catch (error) {
        console.error('Error loading existing data:', error);
        setExistingImages([]);
        setAttributeValues({});
        setAvailableAttributes([]);
      }
    };

    setImages([]); // Reset new images for edit
    setPrimaryImageIndex(0);
    setActiveTab('basic');
    loadExistingData();
    setShowModal(true);
  };

  const handleDelete = (product) => {
    setProductToDelete(product);
    setShowDeleteModal(true);
  };

  const handleViewDetails = (product) => {
    setProductToView(product);
    setShowDetailsModal(true);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    console.log('🚀 Form submission started');
    console.log('📋 Form data:', formData);
    console.log('🖼️ Images:', images.length);

    try {
      setSubmitting(true); // Use separate submitting state
      setError('');
      setSuccess(''); // Clear previous success messages

      // Validate required fields - Restored for actual API field names
      if (
        !formData.nom_produit ||
        !formData.prix_produit ||
        !formData.quantite_produit ||
        !formData.marque_id ||
        !formData.sous_sous_categorie_id
      ) {
        throw new Error('Veuillez remplir tous les champs obligatoires');
      }

      // Prepare API data - Restored to match actual API structure
      const apiData = {
        nom_produit: formData.nom_produit,
        description_produit: formData.description_produit,
        prix_produit: formData.prix_produit,
        quantite_produit: formData.quantite_produit,
        reference: formData.reference,
        marque_id: formData.marque_id,
        sous_sous_categorie_id: formData.sous_sous_categorie_id
      };

      // Add attributes if any - Updated to match documentation structure
      if (Object.keys(attributeValues).length > 0) {
        apiData.attributs = Object.entries(attributeValues)
          .filter(([_, value]) => value !== '' && value !== null && value !== undefined)
          .map(([attribut_id, valeur]) => {
            const attribut = availableAttributes.find((attr) => attr.id == attribut_id);
            const attributData = {
              attribut_id: parseInt(attribut_id)
            };

            // Set the appropriate value field based on attribute type
            if (attribut && attribut.type_valeur) {
              switch (attribut.type_valeur) {
                case 'nombre':
                  attributData.valeur_nombre = parseFloat(valeur) || 0;
                  break;
                case 'booleen':
                  attributData.valeur_booleen = Boolean(valeur);
                  break;
                case 'date':
                  attributData.valeur_date = valeur;
                  break;
                default:
                  attributData.valeur_texte = String(valeur);
              }
            } else {
              // Default to text if type is unknown
              attributData.valeur_texte = String(valeur);
            }

            return attributData;
          });
      }

      // Add variants if any
      if (variants.length > 0) {
        apiData.variants = variants;
      }

      console.log('📝 API Data being sent:', apiData);
      console.log('🖼️ Images being sent:', images.length);
      console.log('🏷️ Attributes being sent:', attributeValues);
      console.log('🔄 Variants being sent:', variants);

      if (modalAction === 'create') {
        console.log('🚀 Starting product creation...');
        console.log('📊 Has images:', images.length > 0);
        console.log('📋 JSON data:', JSON.stringify(apiData, null, 2));

        // Step 1: Create the product (always JSON)
        console.log('📤 Creating product...');
        const result = await createProduct(apiData);
        console.log('✅ Product created via service:', result);

        // 📋 DOCUMENTATION FIX: Handle correct response structure
        let createdProduct, productId;

        if (result.status === 'success' && result.data) {
          // Documentation format: { "status": "success", "data": {...}, "message": "..." }
          createdProduct = result.data;
          productId = createdProduct.id;
          console.log('📋 Using documented response format');
        } else if (result.data) {
          // Alternative format: { "data": {...} }
          createdProduct = result.data;
          productId = createdProduct.id;
          console.log('📋 Using alternative response format');
        } else if (result.id) {
          // Direct format: { "id": ..., ... }
          createdProduct = result;
          productId = result.id;
          console.log('📋 Using direct response format');
        } else {
          console.error('❌ Unexpected response structure:', result);
          throw new Error('Produit créé mais structure de réponse inattendue');
        }

        if (!productId) {
          console.error('❌ No product ID in response:', result);
          throw new Error('Produit créé mais ID non récupéré');
        }

        console.log('📋 Created product ID:', productId);

        // Step 2: Upload images if any (separate API call)
        if (images.length > 0) {
          console.log('🖼️ Uploading images for product:', productId);

          try {
            const imagesFormData = new FormData();
            images.forEach((img) => {
              imagesFormData.append('images[]', img);
            });
            imagesFormData.append('model_type', 'produit');
            imagesFormData.append('model_id', productId);
            imagesFormData.append('primary_index', primaryImageIndex);

            // Get authentication token for image upload
            const token = localStorage.getItem('access_token');
            const imageHeaders = {
              Accept: 'application/json'
            };

            if (token) {
              imageHeaders['Authorization'] = `Bearer ${token}`;
            }

            console.log('📤 Uploading images...');
            const imageResponse = await fetch(
              `${import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api'}/images/upload-multiple`,
              {
                method: 'POST',
                headers: imageHeaders,
                body: imagesFormData
              }
            );

            if (!imageResponse.ok) {
              const errorImg = await imageResponse.text();
              console.warn('⚠️ Image upload failed:', errorImg);
              // Don't throw error, product was created successfully
              setSuccess('Produit créé avec succès (images non uploadées)');
            } else {
              // 📋 DOCUMENTATION FIX: Handle image upload response structure
              const imageResult = await imageResponse.json();
              console.log('✅ Images uploaded successfully:', imageResult);

              if (imageResult.success && imageResult.images) {
                console.log(`📸 ${imageResult.images.length} images uploaded successfully`);
                setSuccess(`Produit créé avec succès (${imageResult.images.length} images uploadées)`);
              } else if (imageResult.message) {
                console.log('📸 Images uploaded with message:', imageResult.message);
                setSuccess('Produit créé avec succès');
              } else {
                console.log('📸 Images uploaded (unknown format)');
                setSuccess('Produit créé avec succès');
              }
            }
          } catch (imgErr) {
            console.warn('⚠️ Image upload error:', imgErr);
            // Don't throw error, product was created successfully
            setSuccess('Produit créé avec succès (erreur upload images)');
          }
        } else {
          setSuccess('Produit créé avec succès');
        }

        console.log('✅ Product creation process completed');
      } else {
        // Update logic: always use JSON for product update, handle images separately
        console.log('🔄 Starting product update...');
        console.log('📊 Has images:', images.length > 0);
        console.log('📋 JSON data:', JSON.stringify(apiData, null, 2));

        // Step 1: Update the product (always JSON)
        await updateProduct(currentProduct.id, apiData);
        console.log('✅ Product updated via service');

        // Step 2: Upload new images if any (separate API call)
        if (images.length > 0) {
          console.log('🖼️ Uploading new images for product:', currentProduct.id);

          try {
            const imagesFormData = new FormData();
            images.forEach((img) => {
              imagesFormData.append('images[]', img);
            });
            imagesFormData.append('model_type', 'produit');
            imagesFormData.append('model_id', currentProduct.id);
            imagesFormData.append('primary_index', primaryImageIndex);

            // Get authentication token for image upload
            const token = localStorage.getItem('access_token');
            const imageHeaders = {
              Accept: 'application/json'
            };

            if (token) {
              imageHeaders['Authorization'] = `Bearer ${token}`;
            }

            const imageResponse = await fetch(
              `${import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api'}/images/upload-multiple`,
              {
                method: 'POST',
                headers: imageHeaders,
                body: imagesFormData
              }
            );

            if (!imageResponse.ok) {
              const errorImg = await imageResponse.json();
              console.warn('⚠️ Image upload failed:', errorImg);
              // Don't throw error, product was updated successfully
              setSuccess('Produit mis à jour avec succès (images non uploadées)');
            } else {
              // 📋 DOCUMENTATION FIX: Handle image upload response structure
              const imageResult = await imageResponse.json();
              console.log('✅ Images uploaded successfully:', imageResult);

              if (imageResult.success && imageResult.images) {
                console.log(`📸 ${imageResult.images.length} images uploaded successfully`);
                setSuccess(`Produit mis à jour avec succès (${imageResult.images.length} images uploadées)`);
              } else if (imageResult.message) {
                console.log('📸 Images uploaded with message:', imageResult.message);
                setSuccess('Produit mis à jour avec succès');
              } else {
                console.log('📸 Images uploaded (unknown format)');
                setSuccess('Produit mis à jour avec succès');
              }
            }
          } catch (imgErr) {
            console.warn('⚠️ Image upload error:', imgErr);
            // Don't throw error, product was updated successfully
            setSuccess('Produit mis à jour avec succès (erreur upload images)');
          }
        } else {
          setSuccess('Produit mis à jour avec succès');
        }

        // For update: reload from server to ensure data consistency
        console.log('🔄 Reloading products from server after update...');
        try {
          // Reload products list from server to ensure we have the latest data including attributes
          const productsResponse = await fetchProducts();
          const newProducts = productsResponse.data || productsResponse;
          const productArray = Array.isArray(newProducts) ? newProducts : [];
          setProducts(productArray);
          setFilteredProducts(productArray);
          console.log('✅ Products list reloaded from server:', productArray.length);

          // Note: We don't fetch images here to avoid freezing the UI
          // Images will be loaded on-demand when needed
        } catch (reloadError) {
          console.warn('⚠️ Failed to reload products list, using local update fallback:', reloadError);
          // Fallback: update local state if server reload fails
          try {
            const updatedProducts = products.map((p) => (p.id === currentProduct.id ? { ...p, ...apiData, id: currentProduct.id } : p));
            setProducts(updatedProducts);
            setFilteredProducts(updatedProducts);
            console.log('✅ Local product state updated as fallback for product ID:', currentProduct.id);
          } catch (localUpdateError) {
            console.warn('⚠️ Both server reload and local update failed:', localUpdateError);
            // Don't show error, the product was updated successfully
          }
        }
      }

      // Close modal and reset form
      console.log('🔄 Closing modal and resetting form...');
      setShowModal(false);
      setImages([]);
      setExistingImages([]);
      setPrimaryImageIndex(0);
      setActiveTab('basic');

      // Reset form data - Restored for actual API field names
      setFormData({
        nom_produit: '',
        description_produit: '',
        prix_produit: '',
        quantite_produit: '',
        reference: '',
        marque_id: '',
        sous_sous_categorie_id: '',
        categorie_id: '',
        sous_categorie_id: ''
      });

      // Reset other form states
      setAttributeValues({});
      setVariants([]);
      setAvailableAttributes([]);
      setFilteredSousCategories([]);
      setFilteredSousSousCategories([]);

      console.log('✅ Form reset completed');
    } catch (err) {
      console.error('❌ Error saving product:', err);
      console.error('❌ Error name:', err.name);
      console.error('❌ Error message:', err.message);
      console.error('❌ Error stack:', err.stack);

      // More detailed error handling
      let errorMessage = 'Erreur lors de la sauvegarde';

      if (err.name === 'TypeError' && err.message.includes('fetch')) {
        errorMessage = 'Impossible de se connecter au serveur. Vérifiez votre connexion internet.';
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
    } finally {
      setSubmitting(false); // Reset submitting state
    }
  };

  const confirmDelete = async () => {
    try {
      setLoading(true);
      setError(''); // Clear any previous errors

      await deleteProduct(productToDelete.id);

      // Remove the product from the local state immediately
      const updatedProducts = products.filter((p) => p.id !== productToDelete.id);
      setProducts(updatedProducts);
      setFilteredProducts(updatedProducts);

      setSuccess('Produit supprimé avec succès');
      setShowDeleteModal(false);

      // Optionally reload data to ensure consistency
      // await loadInitialData();
    } catch (err) {
      console.error('Error deleting product:', err);

      // Handle specific error cases
      if (err.message.includes("n'existe pas") || err.message.includes('déjà été supprimé')) {
        // Product was already deleted, remove it from local state
        const updatedProducts = products.filter((p) => p.id !== productToDelete.id);
        setProducts(updatedProducts);
        setFilteredProducts(updatedProducts);
        setSuccess('Produit supprimé (était déjà supprimé du serveur)');
        setShowDeleteModal(false);
      } else {
        setError(err.message || 'Erreur lors de la suppression');
      }
    } finally {
      setLoading(false);
    }
  };

  const getBrandName = (marqueId) => {
    const brand = brands.find((b) => b.id === marqueId);
    return brand ? brand.nom_marque : 'N/A';
  };

  const getCategoryPath = (sousSousCategorieId) => {
    if (!sousSousCategorieId) return 'N/A';

    const sousSousCategorie = sousSousCategories.find((ssc) => ssc.id === sousSousCategorieId);
    if (!sousSousCategorie) return 'N/A';

    // Handle both possible field names for sous-sous-categories
    const sousSousCatName = sousSousCategorie.nom || sousSousCategorie.nom_sous_sous_categorie;

    const sousCategorie = sousCategories.find((sc) => sc.id === sousSousCategorie.sous_categorie_id);
    if (!sousCategorie) return sousSousCatName || 'N/A';

    // Handle both possible field names for sous-categories
    const sousCatName = sousCategorie.nom || sousCategorie.nom_sous_categorie;

    const categorie = categories.find((c) => c.id === sousCategorie.categorie_id);
    if (!categorie) return `${sousCatName} > ${sousSousCatName}`;

    // Handle both possible field names for categories
    const catName = categorie.nom || categorie.nom_categorie;

    return `${catName} > ${sousCatName} > ${sousSousCatName}`;
  };

  // Pagination
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentProducts = filteredProducts.slice(startIndex, endIndex);

  // Table columns configuration
  const columns = [
    { id: 'image', label: 'Image', minWidth: 80 },
    { id: 'nom_produit', label: 'Produit', minWidth: 200 },
    { id: 'reference', label: 'Référence', minWidth: 120 },
    { id: 'marque_id', label: 'Marque', minWidth: 120 },
    { id: 'sous_sous_categorie_id', label: 'Catégorie', minWidth: 180 },
    { id: 'prix_produit', label: 'Prix', minWidth: 100 },
    { id: 'quantite_produit', label: 'Stock', minWidth: 80 },
    { id: 'actions', label: 'Actions', minWidth: 150 }
  ];

  // Custom cell renderer
  const renderCell = (column, row, value) => {
    switch (column.id) {
      case 'image':
        return (
          <Box sx={{ width: '60px', height: '60px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <LazyProductImage
              productId={row.id}
              productName={row.nom_produit}
              productImages={productImages}
              onImageLoad={handleImageLoad}
              style={{ borderRadius: '8px' }}
            />
          </Box>
        );
      case 'nom_produit':
        return (
          <Box>
            <Typography
              variant="body2"
              sx={{
                fontFamily: TYPOGRAPHY.fontFamily.primary,
                fontWeight: TYPOGRAPHY.fontWeight.semibold,
                color: COLORS.text.dark
              }}
            >
              {row.nom_produit}
            </Typography>
            {row.description_produit && (
              <Typography variant="caption" sx={{ color: COLORS.text.secondary }}>
                {row.description_produit.length > 50 ? `${row.description_produit.substring(0, 50)}...` : row.description_produit}
              </Typography>
            )}
          </Box>
        );
      case 'reference':
        return (
          <Chip
            label={row.reference || 'N/A'}
            size="small"
            sx={{
              backgroundColor: COLORS.grey[200],
              color: COLORS.text.dark,
              fontFamily: TYPOGRAPHY.fontFamily.primary
            }}
          />
        );
      case 'marque_id':
        return (
          <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary }}>
            {getBrandName(row.marque_id)}
          </Typography>
        );
      case 'sous_sous_categorie_id':
        return (
          <Typography variant="body2" sx={{ fontFamily: TYPOGRAPHY.fontFamily.primary, fontSize: TYPOGRAPHY.fontSize.xs }}>
            {getCategoryPath(row.sous_sous_categorie_id)}
          </Typography>
        );
      case 'prix_produit':
        return (
          <Typography
            variant="body2"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontWeight: TYPOGRAPHY.fontWeight.semibold,
              color: COLORS.success.main
            }}
          >
            {parseFloat(row.prix_produit).toFixed(2)} DT
          </Typography>
        );
      case 'quantite_produit':
        const stock = parseInt(row.quantite_produit);
        const stockColor = stock > 10 ? COLORS.success.main : stock > 0 ? COLORS.warning.main : COLORS.error.main;
        return (
          <Chip
            label={stock}
            size="small"
            sx={{
              backgroundColor: stockColor,
              color: 'white',
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontWeight: TYPOGRAPHY.fontWeight.medium
            }}
          />
        );
      case 'actions':
        return (
          <Box sx={{ display: 'flex', gap: 1 }}>
            <IconButton size="small" onClick={() => handleViewDetails(row)} sx={{ color: COLORS.primary.main }} title="Voir les détails">
              <FaEye />
            </IconButton>
            <IconButton size="small" onClick={() => handleEdit(row)} sx={{ color: COLORS.secondary.main }} title="Modifier">
              <FaPencilAlt />
            </IconButton>
            <IconButton size="small" onClick={() => handleDelete(row)} sx={{ color: COLORS.error.main }} title="Supprimer">
              <FaTrashAlt />
            </IconButton>
          </Box>
        );
      default:
        return value || 'N/A';
    }
  };

  // Show loading spinner while initial data is loading
  if (loading) {
    return (
      <Container fluid className="py-4">
        <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '60vh' }}>
          <div className="text-center">
            <Spinner animation="border" variant="primary" style={{ width: '4rem', height: '4rem' }} />
            <div className="mt-3 fw-medium text-primary">Chargement des produits...</div>
            <div className="text-muted small">Veuillez patienter</div>
          </div>
        </div>
      </Container>
    );
  }

  return (
    <Container fluid className="py-4">
      {/* Breadcrumb - Design System Style */}
      <Box sx={{ mb: 2 }}>
        <Typography
          variant="body2"
          sx={{
            fontFamily: TYPOGRAPHY.fontFamily.primary,
            color: COLORS.text.secondary,
            fontSize: TYPOGRAPHY.fontSize.sm
          }}
        >
          Accueil &gt; Gestion des Produits
        </Typography>
      </Box>

      {/* Alerts */}
      {error && (
        <Alert variant="danger" className="mb-4">
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <strong>Erreur:</strong> {error}
            </div>
            <div>
              <Button variant="outline-danger" size="sm" onClick={() => window.location.reload()} className="me-2">
                Réessayer
              </Button>
              <Button variant="outline-secondary" size="sm" onClick={() => setError('')}>
                Fermer
              </Button>
            </div>
          </div>
        </Alert>
      )}
      {success && (
        <Alert variant="success" onClose={() => setSuccess('')} dismissible className="mb-4">
          {success}
        </Alert>
      )}

      {/* Header - Design System Style */}
      <Box sx={{ mb: 4 }}>
        {/* Page Title and Description */}
        <Box sx={{ mb: 3 }}>
          <Typography
            variant="h3"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontWeight: TYPOGRAPHY.fontWeight.bold,
              color: COLORS.text.dark,
              mb: 1
            }}
          >
            Gestion des Produits
          </Typography>
          <Typography
            variant="body1"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              color: COLORS.text.secondary,
              fontSize: TYPOGRAPHY.fontSize.md
            }}
          >
            Gérez tous vos produits en un seul endroit
          </Typography>
        </Box>

        {/* Action Button */}
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 3 }}>
          <StandardButton variant="primary" onClick={handleCreate} disabled={submitting} startIcon={<FaPlus />}>
            Ajouter un Produit
          </StandardButton>
        </Box>
      </Box>

      {/* Filters - Design System Style */}
      <StandardCard title="Filtres et Recherche" size="medium" sx={{ mb: 3 }}>
        <Box sx={{ p: 2 }}>
          <Row className="g-3">
            {/* Search */}
            <Col md={4}>
              <Form.Label className="fw-medium">Recherche</Form.Label>
              <InputGroup>
                <InputGroup.Text>
                  <FaSearch />
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="Nom, référence, description..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </InputGroup>
            </Col>

            {/* Brand Filter */}
            <Col md={2}>
              <Form.Label className="fw-medium">Marque</Form.Label>
              <Form.Select value={selectedBrand} onChange={(e) => setSelectedBrand(e.target.value)}>
                <option value="">Toutes les marques</option>
                {brands.map((brand) => (
                  <option key={brand.id} value={brand.id}>
                    {brand.nom_marque}
                  </option>
                ))}
              </Form.Select>
            </Col>

            {/* Category Filter */}
            <Col md={2}>
              <Form.Label className="fw-medium">Catégorie</Form.Label>
              <Form.Select
                value={selectedCategory}
                onChange={(e) => {
                  setSelectedCategory(e.target.value);
                  setSelectedSousCategory('');
                  setSelectedSousSousCategory('');
                }}
              >
                <option value="">Toutes les catégories</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.nom || category.nom_categorie}
                  </option>
                ))}
              </Form.Select>
            </Col>

            {/* Sous-Category Filter */}
            <Col md={2}>
              <Form.Label className="fw-medium">Sous-catégorie</Form.Label>
              <Form.Select
                value={selectedSousCategory}
                onChange={(e) => {
                  setSelectedSousCategory(e.target.value);
                  setSelectedSousSousCategory('');
                }}
                disabled={!selectedCategory}
              >
                <option value="">Toutes</option>
                {sousCategories
                  .filter((sc) => sc.categorie_id === parseInt(selectedCategory))
                  .map((sousCategory) => (
                    <option key={sousCategory.id} value={sousCategory.id}>
                      {sousCategory.nom || sousCategory.nom_sous_categorie}
                    </option>
                  ))}
              </Form.Select>
            </Col>

            {/* Sous-Sous-Category Filter */}
            <Col md={2}>
              <Form.Label className="fw-medium">Sous-sous-catégorie</Form.Label>
              <Form.Select
                value={selectedSousSousCategory}
                onChange={(e) => setSelectedSousSousCategory(e.target.value)}
                disabled={!selectedSousCategory}
              >
                <option value="">Toutes</option>
                {sousSousCategories
                  .filter((ssc) => ssc.sous_categorie_id === parseInt(selectedSousCategory))
                  .map((sousSousCategory) => (
                    <option key={sousSousCategory.id} value={sousSousCategory.id}>
                      {sousSousCategory.nom || sousSousCategory.nom_sous_sous_categorie}
                    </option>
                  ))}
              </Form.Select>
            </Col>
          </Row>

          <Row className="g-3 mt-2">
            {/* Stock Filter */}
            <Col md={2}>
              <Form.Label className="fw-medium">Stock</Form.Label>
              <Form.Select value={stockFilter} onChange={(e) => setStockFilter(e.target.value)}>
                <option value="">Tous</option>
                <option value="in_stock">En stock</option>
                <option value="out_of_stock">Rupture</option>
              </Form.Select>
            </Col>

            {/* Price Range */}
            <Col md={3}>
              <Form.Label className="fw-medium">Prix (DT)</Form.Label>
              <Row>
                <Col>
                  <Form.Control
                    type="number"
                    placeholder="Min"
                    value={priceRange.min}
                    onChange={(e) => setPriceRange({ ...priceRange, min: e.target.value })}
                  />
                </Col>
                <Col xs="auto" className="d-flex align-items-center">
                  <span>-</span>
                </Col>
                <Col>
                  <Form.Control
                    type="number"
                    placeholder="Max"
                    value={priceRange.max}
                    onChange={(e) => setPriceRange({ ...priceRange, max: e.target.value })}
                  />
                </Col>
              </Row>
            </Col>

            {/* Clear Filters */}
            <Col md={2} className="d-flex align-items-end">
              <StandardButton variant="secondary" onClick={clearFilters} fullWidth>
                Effacer les filtres
              </StandardButton>
            </Col>

            {/* Results Count */}
            <Col md={5} className="d-flex align-items-end">
              <div className="text-muted">
                <strong>{filteredProducts.length}</strong> produit(s) trouvé(s) sur <strong>{products.length}</strong>
              </div>
            </Col>
          </Row>
        </Box>
      </StandardCard>

      {/* Enhanced Products Table */}
      <Box sx={{ mb: 2 }}>
        <Typography
          variant="h5"
          sx={{
            fontFamily: TYPOGRAPHY.fontFamily.primary,
            fontSize: TYPOGRAPHY.fontSize.lg,
            fontWeight: TYPOGRAPHY.fontWeight.semibold,
            color: COLORS.text.dark
          }}
        >
          Tous les Produits ({filteredProducts.length})
        </Typography>
        <Typography
          variant="body2"
          sx={{
            fontFamily: TYPOGRAPHY.fontFamily.primary,
            color: COLORS.text.secondary,
            mt: 0.5
          }}
        >
          {filteredProducts.length} produit(s) trouvé(s) sur {products.length}
        </Typography>
      </Box>

      <StandardTable
        columns={columns}
        data={currentProducts}
        loading={false}
        error={error}
        emptyMessage="Aucun produit trouvé. Essayez de modifier vos critères de recherche ou ajoutez un nouveau produit."
        renderCell={renderCell}
        hover={true}
        pagination={{
          page: currentPage,
          totalPages: totalPages,
          onPageChange: (page) => setCurrentPage(page)
        }}
      />

      {/* Additional Info */}
      {filteredProducts.length > 0 && (
        <Box sx={{ mt: 2 }}>
          <Typography
            variant="body2"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              color: COLORS.text.secondary,
              textAlign: 'center'
            }}
          >
            Affichage de {startIndex + 1} à {Math.min(endIndex, filteredProducts.length)} sur {filteredProducts.length} produit(s)
          </Typography>
        </Box>
      )}

      {/* Professional Create/Edit Product Modal */}
      <FormModal
        show={showModal}
        onHide={() => setShowModal(false)}
        onSubmit={handleSubmit}
        title={modalAction === 'create' ? 'Ajouter un Produit' : 'Modifier le Produit'}
        isEdit={modalAction === 'edit'}
        loading={submitting}
        size="lg"
        submitText={modalAction === 'create' ? 'Créer le produit' : 'Enregistrer les modifications'}
        cancelText="Annuler"
        icon={modalAction === 'create' ? <FaPlus /> : <FaPencilAlt />}
      >
        <Form onSubmit={handleSubmit}>
          {/* Loading Overlay */}
          {submitting && (
            <div
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 1000,
                borderRadius: '0.375rem'
              }}
            >
              <div className="text-center">
                <Spinner animation="border" variant="primary" style={{ width: '3rem', height: '3rem' }} />
                <div className="mt-3 fw-medium text-primary">
                  {modalAction === 'create' ? 'Création du produit en cours...' : 'Modification du produit en cours...'}
                </div>
                <div className="text-muted small">Veuillez patienter</div>
              </div>
            </div>
          )}

          <Tabs activeKey={activeTab} onSelect={(k) => setActiveTab(k)} className="mb-3">
            {/* Basic Information Tab */}
            <Tab eventKey="basic" title="Informations de base">
              <Row className="g-3">
                {/* Product Name */}
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="fw-medium">
                      Nom du produit <span className="text-danger">*</span>
                    </Form.Label>
                    <Form.Control
                      name="nom_produit"
                      type="text"
                      value={formData.nom_produit}
                      onChange={(e) => setFormData({ ...formData, nom_produit: e.target.value })}
                      required
                      placeholder="Entrez le nom du produit"
                    />
                  </Form.Group>
                </Col>

                {/* Product Reference */}
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="fw-medium">Référence produit</Form.Label>
                    <Form.Control
                      name="reference"
                      type="text"
                      value={formData.reference}
                      onChange={(e) => setFormData({ ...formData, reference: e.target.value })}
                      placeholder="Référence unique"
                    />
                  </Form.Group>
                </Col>

                {/* Description */}
                <Col md={12}>
                  <Form.Group>
                    <Form.Label className="fw-medium">Description</Form.Label>
                    <Form.Control
                      name="description_produit"
                      as="textarea"
                      rows={3}
                      value={formData.description_produit}
                      onChange={(e) => setFormData({ ...formData, description_produit: e.target.value })}
                      placeholder="Description du produit"
                    />
                  </Form.Group>
                </Col>

                {/* Price */}
                <Col md={4}>
                  <Form.Group>
                    <Form.Label className="fw-medium">
                      Prix (DT) <span className="text-danger">*</span>
                    </Form.Label>
                    <Form.Control
                      name="prix_produit"
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.prix_produit}
                      onChange={(e) => setFormData({ ...formData, prix_produit: e.target.value })}
                      required
                      placeholder="0.00"
                    />
                  </Form.Group>
                </Col>

                {/* Quantity */}
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="fw-medium">
                      Quantité <span className="text-danger">*</span>
                    </Form.Label>
                    <Form.Control
                      name="quantite_produit"
                      type="number"
                      min="0"
                      value={formData.quantite_produit}
                      onChange={(e) => setFormData({ ...formData, quantite_produit: e.target.value })}
                      required
                      placeholder="0"
                    />
                  </Form.Group>
                </Col>

                {/* Brand */}
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="fw-medium">
                      Marque <span className="text-danger">*</span>
                    </Form.Label>
                    <Form.Select
                      name="marque_id"
                      value={formData.marque_id}
                      onChange={(e) => setFormData({ ...formData, marque_id: e.target.value })}
                      required
                    >
                      <option value="">Sélectionnez une marque</option>
                      {brands.map((brand) => (
                        <option key={brand.id} value={brand.id}>
                          {brand.nom_marque}
                        </option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>

                {/* Category */}
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="fw-medium">
                      Catégorie <span className="text-danger">*</span>
                    </Form.Label>
                    <Form.Select
                      name="categorie_id"
                      value={formData.categorie_id}
                      onChange={(e) => setFormData({ ...formData, categorie_id: e.target.value })}
                      required
                    >
                      <option value="">Sélectionnez une catégorie</option>
                      {categories.map((category) => (
                        <option key={category.id} value={category.id}>
                          {category.nom || category.nom_categorie}
                        </option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>

                {/* Sous-Category */}
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="fw-medium">
                      Sous-catégorie <span className="text-danger">*</span>
                    </Form.Label>
                    <Form.Select
                      name="sous_categorie_id"
                      value={formData.sous_categorie_id}
                      onChange={(e) => setFormData({ ...formData, sous_categorie_id: e.target.value })}
                      required={!!formData.categorie_id}
                      disabled={!formData.categorie_id}
                    >
                      <option value="">
                        {!formData.categorie_id ? "Sélectionnez d'abord une catégorie" : 'Sélectionnez une sous-catégorie'}
                      </option>
                      {filteredSousCategories.map((sousCategory) => (
                        <option key={sousCategory.id} value={sousCategory.id}>
                          {sousCategory.nom || sousCategory.nom_sous_categorie}
                        </option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>

                {/* Sous-Sous-Category */}
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="fw-medium">
                      Sous-sous-catégorie <span className="text-danger">*</span>
                    </Form.Label>
                    <Form.Select
                      name="sous_sous_categorie_id"
                      value={formData.sous_sous_categorie_id}
                      onChange={(e) => setFormData({ ...formData, sous_sous_categorie_id: e.target.value })}
                      required={!!formData.sous_categorie_id}
                      disabled={!formData.sous_categorie_id}
                    >
                      <option value="">
                        {!formData.sous_categorie_id ? "Sélectionnez d'abord une sous-catégorie" : 'Sélectionnez une sous-sous-catégorie'}
                      </option>
                      {filteredSousSousCategories.map((sousSousCategory) => (
                        <option key={sousSousCategory.id} value={sousSousCategory.id}>
                          {sousSousCategory.nom || sousSousCategory.nom_sous_sous_categorie}
                        </option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
              </Row>
            </Tab>

            {/* Images Tab */}
            <Tab eventKey="images" title="Images">
              <Row className="g-3">
                <Col md={12}>
                  <Form.Group>
                    <Form.Label className="fw-medium">Images du produit</Form.Label>
                    <Form.Control type="file" multiple accept="image/*" onChange={handleImagesChange} className="mb-3" />
                    <Form.Text className="text-muted">
                      Sélectionnez plusieurs images. Cliquez sur une image pour la définir comme principale.
                    </Form.Text>

                    {/* Existing Images (for edit mode) */}
                    {existingImages.length > 0 && (
                      <div className="mt-3">
                        <h6 className="fw-medium mb-2">Images existantes</h6>
                        <div className="alert alert-info py-2 px-3 mb-3">
                          <small>
                            <FaEye className="me-1" />
                            <strong>Cliquez sur une image</strong> pour la définir comme image principale
                          </small>
                        </div>
                        <div className="d-flex flex-wrap gap-2 mb-3">
                          {existingImages.map((img) => (
                            <div
                              key={img.id}
                              className={`position-relative border rounded ${
                                img.is_primary ? 'border-primary border-3 shadow' : 'border-secondary'
                              }`}
                              style={{
                                width: '120px',
                                height: '120px',
                                cursor: img.is_primary ? 'default' : 'pointer',
                                opacity: img.is_primary ? 1 : 0.8
                              }}
                              onClick={() => !img.is_primary && setExistingImageAsPrimary(img.id, currentProduct.id)}
                              title={img.is_primary ? 'Image principale actuelle' : 'Cliquer pour définir comme image principale'}
                            >
                              <img
                                src={img.thumbnail_medium || img.direct_url}
                                alt={img.alt_text || 'Image produit'}
                                style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                                className="rounded"
                              />

                              {/* Badge principal - coin supérieur gauche */}
                              {img.is_primary && (
                                <div className="position-absolute top-0 start-0 m-1">
                                  <Badge bg="primary" className="d-flex align-items-center">
                                    <FaEye className="me-1" size={10} />
                                    Principal
                                  </Badge>
                                </div>
                              )}

                              {/* Indicateur cliquable - coin supérieur droit */}
                              {!img.is_primary && (
                                <div className="position-absolute top-0 end-0 m-1">
                                  <Badge
                                    bg={updatingImageId === img.id ? 'warning' : 'success'}
                                    className="rounded-circle"
                                    style={{
                                      width: '24px',
                                      height: '24px',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center'
                                    }}
                                  >
                                    {updatingImageId === img.id ? (
                                      <Spinner animation="border" size="sm" style={{ width: '10px', height: '10px' }} />
                                    ) : (
                                      <FaPlus size={10} />
                                    )}
                                  </Badge>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                        <hr />
                        <h6 className="fw-medium mb-2">Nouvelles images</h6>
                      </div>
                    )}

                    {images.length > 0 && (
                      <div className="d-flex mt-3 flex-wrap gap-2">
                        {images.map((img, idx) => (
                          <div
                            key={idx}
                            className={`position-relative border rounded ${
                              idx === primaryImageIndex ? 'border-primary border-3' : 'border-secondary'
                            }`}
                            style={{ cursor: 'pointer', width: '120px', height: '120px' }}
                          >
                            <img
                              src={URL.createObjectURL(img)}
                              alt={`Image ${idx + 1}`}
                              style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                              className="rounded"
                              onClick={() => setPrimaryImage(idx)}
                            />
                            {idx === primaryImageIndex && (
                              <Badge bg="primary" className="position-absolute top-0 start-0 m-1">
                                Principale
                              </Badge>
                            )}
                            <Button
                              variant="danger"
                              size="sm"
                              className="position-absolute top-0 end-0 m-1 rounded-circle"
                              style={{ width: '24px', height: '24px', padding: '0' }}
                              onClick={(e) => {
                                e.stopPropagation();
                                removeImage(idx);
                              }}
                            >
                              ×
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}

                    {images.length === 0 && (
                      <div className="text-center py-4 border rounded bg-light">
                        <FaBox size={32} className="text-muted mb-2" />
                        <p className="text-muted mb-0">Aucune image sélectionnée</p>
                      </div>
                    )}
                  </Form.Group>
                </Col>
              </Row>
            </Tab>

            {/* Attributes Tab */}
            <Tab eventKey="attributes" title="Attributs">
              <Row className="g-3">
                {availableAttributes.length > 0 ? (
                  availableAttributes.map((attribute) => (
                    <Col md={6} key={attribute.id}>
                      <Form.Group>
                        <Form.Label className="fw-medium">
                          {attribute.nom}
                          {attribute.obligatoire && <span className="text-danger"> *</span>}
                          {attribute.groupe && <small className="text-muted ms-2">({attribute.groupe.nom})</small>}
                        </Form.Label>
                        {renderAttributeInput(attribute)}
                      </Form.Group>
                    </Col>
                  ))
                ) : (
                  <Col md={12}>
                    <div className="text-center py-4">
                      <FaTags size={32} className="text-muted mb-2" />
                      <p className="text-muted mb-0">
                        {formData.sous_sous_categorie_id
                          ? 'Aucun attribut disponible pour cette catégorie'
                          : 'Sélectionnez une sous-sous-catégorie pour voir les attributs disponibles'}
                      </p>
                    </div>
                  </Col>
                )}
              </Row>
            </Tab>

            {/* Variants Tab */}
            <Tab eventKey="variants" title="Variantes">
              <Row className="g-3">
                <Col md={12}>
                  <div className="d-flex justify-content-between align-items-center mb-3">
                    <h6 className="mb-0">Variantes du produit</h6>
                    <Button variant="outline-primary" size="sm" onClick={() => setShowVariantModal(true)}>
                      <FaPlus className="me-1" />
                      Ajouter une variante
                    </Button>
                  </div>

                  {variants.length > 0 ? (
                    <Table striped bordered hover size="sm">
                      <thead>
                        <tr>
                          <th>SKU</th>
                          <th>Prix supplément</th>
                          <th>Stock</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {variants.map((variant, idx) => (
                          <tr key={variant.id}>
                            <td>{variant.sku}</td>
                            <td>{variant.prix_supplement} DT</td>
                            <td>{variant.stock}</td>
                            <td>
                              <Button variant="outline-danger" size="sm" onClick={() => removeVariant(idx)}>
                                <FaTrashAlt />
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  ) : (
                    <div className="text-center py-4 border rounded bg-light">
                      <FaLayerGroup size={32} className="text-muted mb-2" />
                      <p className="text-muted mb-0">Aucune variante créée</p>
                      <small className="text-muted">Les variantes permettent de créer différentes versions du même produit</small>
                    </div>
                  )}
                </Col>
              </Row>
            </Tab>
          </Tabs>
        </Form>
      </FormModal>

      {/* Professional Delete Confirmation Modal */}
      <ConfirmationModal
        show={showDeleteModal}
        onHide={() => setShowDeleteModal(false)}
        onConfirm={confirmDelete}
        title="Confirmer la suppression"
        message={
          productToDelete && (
            <>
              Êtes-vous sûr de vouloir supprimer le produit <strong>"{productToDelete.nom_produit}"</strong> ?<br />
              <small className="text-muted">Référence: {productToDelete.reference || 'N/A'} • Cette action est irréversible.</small>
            </>
          )
        }
        confirmText="Supprimer"
        cancelText="Annuler"
        variant="danger"
        loading={loading}
        icon={<FaTrashAlt />}
      />

      {/* Professional Variant Modal */}
      <FormModal
        show={showVariantModal}
        onHide={() => setShowVariantModal(false)}
        onSubmit={addVariant}
        title="Ajouter une Variante"
        isEdit={false}
        loading={false}
        size="md"
        submitText="Ajouter la variante"
        cancelText="Annuler"
        icon={<FaPlus />}
      >
        <Row className="g-3">
          <Col md={6}>
            <Form.Group>
              <Form.Label className="fw-medium">
                SKU <span className="text-danger">*</span>
              </Form.Label>
              <Form.Control
                name="variant_sku"
                type="text"
                value={newVariant.sku}
                onChange={(e) => setNewVariant({ ...newVariant, sku: e.target.value })}
                placeholder="Ex: PROD-001-RED-L"
                required
              />
            </Form.Group>
          </Col>
          <Col md={6}>
            <Form.Group>
              <Form.Label className="fw-medium">Prix supplément (DT)</Form.Label>
              <Form.Control
                name="variant_prix_supplement"
                type="number"
                step="0.01"
                value={newVariant.prix_supplement}
                onChange={(e) => setNewVariant({ ...newVariant, prix_supplement: parseFloat(e.target.value) || 0 })}
                placeholder="0.00"
              />
            </Form.Group>
          </Col>
          <Col md={6}>
            <Form.Group>
              <Form.Label className="fw-medium">Stock</Form.Label>
              <Form.Control
                name="variant_stock"
                type="number"
                min="0"
                value={newVariant.stock}
                onChange={(e) => setNewVariant({ ...newVariant, stock: parseInt(e.target.value) || 0 })}
                placeholder="0"
              />
            </Form.Group>
          </Col>

          {/* Variant Attributes */}
          {availableAttributes.length > 0 && (
            <Col md={12}>
              <hr />
              <h6>Attributs de la variante</h6>
              <Row className="g-2">
                {availableAttributes.map((attribute) => (
                  <Col md={6} key={attribute.id}>
                    <Form.Group>
                      <Form.Label className="fw-medium">{attribute.nom}</Form.Label>
                      <Form.Control
                        name={`variant_attribute_${attribute.id}`}
                        type={attribute.type_valeur === 'nombre' ? 'number' : 'text'}
                        value={newVariant.attributs[attribute.id] || ''}
                        onChange={(e) =>
                          setNewVariant({
                            ...newVariant,
                            attributs: { ...newVariant.attributs, [attribute.id]: e.target.value }
                          })
                        }
                        placeholder={`${attribute.nom} de cette variante`}
                      />
                    </Form.Group>
                  </Col>
                ))}
              </Row>
            </Col>
          )}
        </Row>
      </FormModal>

      {/* Professional Product Details Modal */}
      <ProfessionalModal
        show={showDetailsModal}
        onHide={() => setShowDetailsModal(false)}
        title="Détails du Produit"
        size="xl"
        variant="info"
        icon={<FaEye />}
        showFooter={true}
        primaryText="Modifier ce produit"
        secondaryText="Fermer"
        primaryAction={
          productToView
            ? () => {
                setShowDetailsModal(false);
                handleEdit(productToView);
              }
            : undefined
        }
        secondaryAction={() => setShowDetailsModal(false)}
        primaryVariant="primary"
        secondaryVariant="secondary"
      >
        {productToView && (
          <Row className="g-4">
            {/* Product Information */}
            <Col md={6}>
              <Card className="border-0 bg-light h-100">
                <Card.Body>
                  <h5 className="fw-bold mb-3">
                    <FaBox className="me-2 text-primary" />
                    Informations du produit
                  </h5>

                  <div className="mb-3">
                    <strong>Nom:</strong>
                    <div className="mt-1">{productToView.nom_produit}</div>
                  </div>

                  {productToView.description_produit && (
                    <div className="mb-3">
                      <strong>Description:</strong>
                      <div className="mt-1 text-muted">{productToView.description_produit}</div>
                    </div>
                  )}

                  <Row className="g-3">
                    <Col sm={6}>
                      <div className="mb-3">
                        <strong>Référence:</strong>
                        <div className="mt-1">
                          <code className="bg-white px-2 py-1 rounded border">{productToView.reference || 'N/A'}</code>
                        </div>
                      </div>
                    </Col>
                    <Col sm={6}>
                      <div className="mb-3">
                        <strong>Prix:</strong>
                        <div className="mt-1 fw-medium text-success">
                          {productToView.prix_produit ? `${productToView.prix_produit}€` : 'N/A'}
                        </div>
                      </div>
                    </Col>
                    <Col sm={6}>
                      <div className="mb-3">
                        <strong>Stock:</strong>
                        <div className="mt-1">
                          <Badge bg={productToView.quantite_produit > 0 ? 'success' : 'danger'} className="rounded-pill">
                            {productToView.quantite_produit > 0 ? `${productToView.quantite_produit} en stock` : 'Rupture'}
                          </Badge>
                        </div>
                      </div>
                    </Col>
                    <Col sm={6}>
                      <div className="mb-3">
                        <strong>Marque:</strong>
                        <div className="mt-1">{getBrandName(productToView.marque_id)}</div>
                      </div>
                    </Col>
                  </Row>

                  <div className="mb-3">
                    <strong>Catégorie:</strong>
                    <div className="mt-1">
                      <small className="text-muted">{getCategoryPath(productToView.sous_sous_categorie_id)}</small>
                    </div>
                  </div>
                </Card.Body>
              </Card>
            </Col>

            {/* Product Images Gallery */}
            <Col md={6}>
              <Card className="border-0 bg-light h-100">
                <Card.Body>
                  <h5 className="fw-bold mb-3">
                    <FaBox className="me-2 text-primary" />
                    Galerie d'images ({(productImages[productToView.id] || []).length})
                  </h5>

                  {(productImages[productToView.id] || []).length > 0 ? (
                    <>
                      <div className="alert alert-info py-2 px-3 mb-3">
                        <small>
                          <FaEye className="me-1" />
                          <strong>Cliquez sur une image</strong> pour la définir comme principale •<strong> Bouton œil</strong> pour voir en
                          grand
                        </small>
                      </div>
                      <div className="row g-2">
                        {(productImages[productToView.id] || []).map((image, index) => (
                          <div key={image.id} className="col-6 col-lg-4">
                            <div className="position-relative">
                              <img
                                src={image.thumbnail_medium || image.direct_url}
                                alt={image.alt_text || `Image ${index + 1}`}
                                className={`w-100 rounded border ${
                                  image.is_primary ? 'border-primary border-3 shadow' : 'border-secondary'
                                }`}
                                style={{
                                  height: '120px',
                                  objectFit: 'cover',
                                  cursor: image.is_primary ? 'default' : 'pointer',
                                  opacity: image.is_primary ? 1 : 0.8
                                }}
                                onClick={() => !image.is_primary && setExistingImageAsPrimary(image.id, productToView.id)}
                                title={image.is_primary ? 'Image principale actuelle' : 'Cliquer pour définir comme image principale'}
                              />

                              {/* Badge principal - coin supérieur gauche */}
                              {image.is_primary && (
                                <div className="position-absolute top-0 start-0 m-1">
                                  <Badge bg="primary" className="d-flex align-items-center">
                                    <FaEye className="me-1" size={10} />
                                    Principal
                                  </Badge>
                                </div>
                              )}

                              {/* Indicateur cliquable - coin inférieur gauche */}
                              {!image.is_primary && (
                                <div className="position-absolute bottom-0 start-0 m-1">
                                  <Badge
                                    bg={updatingImageId === image.id ? 'warning' : 'success'}
                                    className="rounded-circle"
                                    style={{
                                      width: '24px',
                                      height: '24px',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center'
                                    }}
                                  >
                                    {updatingImageId === image.id ? (
                                      <Spinner animation="border" size="sm" style={{ width: '10px', height: '10px' }} />
                                    ) : (
                                      <FaPlus size={10} />
                                    )}
                                  </Badge>
                                </div>
                              )}

                              {/* Bouton voir en grand - coin supérieur droit */}
                              <div className="position-absolute top-0 end-0 m-1">
                                <Button
                                  variant="light"
                                  size="sm"
                                  className="rounded-circle p-1"
                                  style={{ width: '28px', height: '28px' }}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    window.open(image.direct_url, '_blank');
                                  }}
                                  title="Voir en grand"
                                >
                                  <FaEye size={12} />
                                </Button>
                              </div>
                            </div>
                            {image.alt_text && <small className="text-muted d-block mt-1 text-truncate">{image.alt_text}</small>}
                          </div>
                        ))}
                      </div>
                    </>
                  ) : (
                    <div className="text-center py-5">
                      <FaBox size={48} className="text-muted mb-3" />
                      <h6 className="text-muted">Aucune image</h6>
                      <p className="text-muted mb-0">Ce produit n'a pas d'images associées</p>
                    </div>
                  )}
                </Card.Body>
              </Card>
            </Col>
          </Row>
        )}
      </ProfessionalModal>
    </Container>
  );
};

export default ProductManagement;
