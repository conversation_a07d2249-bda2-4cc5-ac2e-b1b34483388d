import React from 'react';
import { Box, Typography, Al<PERSON>, Button } from '@mui/material';
import { Launch as LaunchIcon } from '@mui/icons-material';
import OrderListEnhanced from '../OrderManagement/OrderListEnhanced';
import { COLORS, TYPOGRAPHY } from '../../themes/designSystem';

const OrderListDemo = () => {
  return (
    <Box sx={{ p: 3 }}>
      {/* Demo Header */}
      <Box sx={{ mb: 4 }}>
        <Typography
          variant="h3"
          sx={{
            fontFamily: TYPOGRAPHY.fontFamily.primary,
            fontWeight: TYPOGRAPHY.fontWeight.bold,
            color: COLORS.primary.main,
            mb: 2
          }}
        >
          🚀 Démonstration - Liste des Commandes Enhanced
        </Typography>

        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            ✨ Nouvelle Liste de Commandes avec API Live
          </Typography>
          <Typography variant="body2" sx={{ mb: 2 }}>
            Cette démonstration utilise l'API live : <strong>https://laravel-api.fly.dev/api/commandes</strong>
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Button
              variant="outlined"
              size="small"
              startIcon={<LaunchIcon />}
              onClick={() => window.open('https://laravel-api.fly.dev/api/commandes', '_blank')}
            >
              Voir l'API
            </Button>
          </Box>
        </Alert>

        <Box
          sx={{
            backgroundColor: COLORS.grey[50],
            p: 3,
            borderRadius: 2,
            border: `1px solid ${COLORS.grey[200]}`,
            mb: 3
          }}
        >
          <Typography variant="h6" gutterBottom sx={{ color: COLORS.primary.main }}>
            🎯 Fonctionnalités Démontrées :
          </Typography>
          <Box component="ul" sx={{ pl: 2, mb: 0 }}>
            <li>
              <strong>API Live</strong> : Données réelles depuis l'API Laravel
            </li>
            <li>
              <strong>Pagination</strong> : Navigation entre les pages de résultats
            </li>
            <li>
              <strong>Recherche</strong> : Recherche par numéro, client, email
            </li>
            <li>
              <strong>Filtres</strong> : Filtrage par statut et méthode de paiement
            </li>
            <li>
              <strong>StandardTable</strong> : Table moderne avec design system
            </li>
            <li>
              <strong>Gestion d'erreur</strong> : Système robuste avec retry automatique
            </li>
            <li>
              <strong>Responsive</strong> : Interface adaptative
            </li>
            <li>
              <strong>Performance</strong> : Chargement optimisé et debounce
            </li>
            <li>
              <strong>Actions</strong> : Consultation des détails uniquement (lecture seule)
            </li>
          </Box>
        </Box>

        <Box
          sx={{
            backgroundColor: COLORS.success.light,
            p: 2,
            borderRadius: 2,
            border: `1px solid ${COLORS.success.main}`,
            mb: 3
          }}
        >
          <Typography variant="body2" sx={{ color: COLORS.success.dark }}>
            <strong>✅ Status :</strong> API connectée et fonctionnelle - {new Date().toLocaleString('fr-FR')}
          </Typography>
        </Box>
      </Box>

      {/* Enhanced Order List Component */}
      <OrderListEnhanced />

      {/* Technical Details */}
      <Box sx={{ mt: 4, p: 3, backgroundColor: COLORS.grey[50], borderRadius: 2 }}>
        <Typography variant="h6" gutterBottom sx={{ color: COLORS.primary.main }}>
          🔧 Détails Techniques :
        </Typography>
        <Box component="ul" sx={{ pl: 2, mb: 0 }}>
          <li>
            <strong>API Endpoint</strong> : GET https://laravel-api.fly.dev/api/commandes
          </li>
          <li>
            <strong>Pagination</strong> : 15 éléments par page
          </li>
          <li>
            <strong>Paramètres</strong> : page, per_page, search, status, payment_method
          </li>
          <li>
            <strong>Format</strong> : JSON avec structure success/data/pagination
          </li>
          <li>
            <strong>Relations</strong> : user, produits, paiement incluses
          </li>
          <li>
            <strong>Gestion d'erreur</strong> : Retry automatique, logging détaillé
          </li>
          <li>
            <strong>Performance</strong> : Debounce 500ms sur la recherche
          </li>
        </Box>
      </Box>
    </Box>
  );
};

export default OrderListDemo;
