# Gestion des Commandes

## Introduction

Le système permet de gérer les commandes des clients avec application automatique des remises appropriées.

## Endpoints API

### Récupérer toutes les commandes

```
GET /api/commandes
```

Retourne la liste de toutes les commandes.

#### Paramètres de filtrage (optionnels)

| Paramètre | Type    | Description                                |
|-----------|---------|--------------------------------------------|
| user_id   | integer | Filtrer par ID d'utilisateur/client        |
| date_debut| date    | Date de début (format: YYYY-MM-DD)         |
| date_fin  | date    | Date de fin (format: YYYY-MM-DD)           |

#### Exemple de réponse

```json
[
  {
    "id": 1,
    "user_id": 1,
    "adresse_commande": "123 Rue Principale",
    "ville_commande": "Tunis",
    "code_postal_commande": "1000",
    "telephone_commande": "71234567",
    "email_commande": "<EMAIL>",
    "total_commande": 1299.99,
    "remise_commande": 10.50,
    "created_at": "2025-04-01T14:30:00.000000Z",
    "updated_at": "2025-04-01T14:30:00.000000Z"
  },
  {
    "id": 2,
    "user_id": 2,
    "adresse_commande": "45 Avenue de la République",
    "ville_commande": "Sousse",
    "code_postal_commande": "4000",
    "telephone_commande": "73456789",
    "email_commande": "<EMAIL>",
    "total_commande": 499.99,
    "remise_commande": 0,
    "created_at": "2025-04-02T10:15:00.000000Z",
    "updated_at": "2025-04-02T10:15:00.000000Z"
  }
]
```

### Récupérer une commande spécifique

```
GET /api/commandes/{id}
```

Retourne les détails d'une commande spécifique avec les produits associés.

#### Exemple de réponse

```json
{
  "id": 1,
  "user_id": 1,
  "adresse_commande": "123 Rue Principale",
  "ville_commande": "Tunis",
  "code_postal_commande": "1000",
  "telephone_commande": "71234567",
  "email_commande": "<EMAIL>",
  "total_commande": 1299.99,
  "remise_commande": 10.50,
  "created_at": "2025-04-01T14:30:00.000000Z",
  "updated_at": "2025-04-01T14:30:00.000000Z",
  "user": {
    "id": 1,
    "name": "Youssef Mrabet",
    "email": "<EMAIL>",
    "remise_personnelle": 10.50,
    "type_client": "normal"
  },
  "produits": [
    {
      "id": 1,
      "nom": "Laptop Pro X",
      "description": "Ordinateur portable haute performance",
      "prix": 1299.99,
      "pivot": {
        "commande_id": 1,
        "produit_id": 1,
        "quantite": 1,
        "prix_unitaire": 1299.99
      }
    }
  ],
  "client_remise": 10.5
}
```

### Créer une nouvelle commande

```
POST /api/commandes
```

Crée une nouvelle commande avec les produits associés.

#### Paramètres de la requête

| Paramètre            | Type    | Description                                |
|----------------------|---------|--------------------------------------------|
| user_id              | integer | ID de l'utilisateur/client                 |
| adresse_commande     | string  | Adresse de livraison                       |
| ville_commande       | string  | Ville de livraison                         |
| code_postal_commande | string  | Code postal                                |
| telephone_commande   | string  | Numéro de téléphone                        |
| email_commande       | string  | Adresse email                              |
| remise_commande      | decimal | Remise spécifique à la commande (optionnel)|
| produits             | array   | Tableau des produits commandés             |

#### Exemple de requête

```json
{
  "user_id": 1,
  "adresse_commande": "123 Rue Principale",
  "ville_commande": "Tunis",
  "code_postal_commande": "1000",
  "telephone_commande": "71234567",
  "email_commande": "<EMAIL>",
  "remise_commande": 5.0,
  "produits": [
    {
      "id": 1,
      "quantite": 1,
      "prix_unitaire": 1299.99
    },
    {
      "id": 2,
      "quantite": 2,
      "prix_unitaire": 499.99
    }
  ]
}
```

#### Exemple de réponse

```json
{
  "id": 3,
  "user_id": 1,
  "adresse_commande": "123 Rue Principale",
  "ville_commande": "Tunis",
  "code_postal_commande": "1000",
  "telephone_commande": "71234567",
  "email_commande": "<EMAIL>",
  "total_commande": 2074.97,
  "remise_commande": 5.0,
  "created_at": "2025-04-03T15:45:00.000000Z",
  "updated_at": "2025-04-03T15:45:00.000000Z",
  "produits": [
    {
      "id": 1,
      "nom": "Laptop Pro X",
      "pivot": {
        "commande_id": 3,
        "produit_id": 1,
        "quantite": 1,
        "prix_unitaire": 1299.99
      }
    },
    {
      "id": 2,
      "nom": "Smartphone XYZ",
      "pivot": {
        "commande_id": 3,
        "produit_id": 2,
        "quantite": 2,
        "prix_unitaire": 499.99
      }
    }
  ],
  "client_remise": 10.5
}
```

### Mettre à jour une commande

```
PUT /api/commandes/{id}
```

Met à jour les informations d'une commande existante.

#### Paramètres de la requête

Mêmes paramètres que pour la création, tous optionnels.

### Supprimer une commande

```
DELETE /api/commandes/{id}
```

Supprime une commande existante.

## Calcul des remises

Le système calcule automatiquement le total de la commande en appliquant la remise appropriée:

1. Le sous-total est calculé en additionnant le prix unitaire multiplié par la quantité pour chaque produit
2. La remise est déterminée en prenant la valeur la plus élevée entre:
   - La remise spécifique à la commande (remise_commande)
   - La remise effective du client (voir [Gestion des Clients](./clients.md))
3. Le montant de la remise est calculé en appliquant le pourcentage de remise au sous-total
4. Le total final est le sous-total moins le montant de la remise

## Récupérer la dernière commande d'un client

```
GET /api/clients/{id}/derniere-commande
```

Retourne la dernière commande effectuée par un client spécifique.

## Récupérer toutes les commandes d'un client

```
GET /api/clients/{id}/commandes
```

Retourne toutes les commandes effectuées par un client spécifique.

## Endpoint administrateur

Les administrateurs peuvent accéder à un endpoint spécial pour récupérer la dernière commande d'un client:

```
GET /api/v1/admin/clients/{id}/derniere-commande
```

Cet endpoint nécessite l'authentification avec un compte administrateur.
