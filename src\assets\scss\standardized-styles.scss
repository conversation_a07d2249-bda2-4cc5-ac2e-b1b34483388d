// ==============================|| STANDARDIZED STYLES ||============================== //

// Import design system variables
@import './themes-vars.module.scss';

// Root CSS variables for consistent styling
:root {
  // Colors
  --color-primary: #{$primaryMain};
  --color-primary-light: #{$primaryLight};
  --color-primary-dark: #{$primaryDark};
  --color-secondary: #{$secondaryMain};
  --color-secondary-light: #{$secondaryLight};
  --color-secondary-dark: #{$secondaryDark};
  --color-success: #{$successMain};
  --color-success-light: #{$successLight};
  --color-success-dark: #{$successDark};
  --color-error: #{$errorMain};
  --color-error-light: #{$errorLight};
  --color-error-dark: #{$errorDark};
  --color-warning: #{$warningMain};
  --color-warning-light: #{$warningLight};
  --color-warning-dark: #{$warningDark};
  
  // Grey scale
  --color-grey-50: #{$grey50};
  --color-grey-100: #{$grey100};
  --color-grey-200: #{$grey200};
  --color-grey-300: #{$grey300};
  --color-grey-500: #{$grey500};
  --color-grey-600: #{$grey600};
  --color-grey-700: #{$grey700};
  --color-grey-900: #{$grey900};
  
  // Text colors
  --color-text-primary: #{$grey700};
  --color-text-secondary: #{$grey500};
  --color-text-dark: #{$grey900};
  --color-text-hint: #{$grey100};
  
  // Background colors
  --color-bg-paper: #{$paper};
  --color-bg-light: #{$grey50};
  
  // Typography
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-secondary: 'Poppins', sans-serif;
  --font-family-mono: 'Roboto Mono', monospace;
  
  // Font sizes
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 2.125rem;
  
  // Font weights
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  // Spacing
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  
  // Border radius
  --border-radius-sm: 0.125rem;
  --border-radius-base: 0.25rem;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 0.75rem;
  
  // Shadows
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

// Global standardized classes
.std-font-primary {
  font-family: var(--font-family-primary) !important;
}

.std-font-secondary {
  font-family: var(--font-family-secondary) !important;
}

.std-font-mono {
  font-family: var(--font-family-mono) !important;
}

// Text utilities
.std-text-xs { font-size: var(--font-size-xs) !important; }
.std-text-sm { font-size: var(--font-size-sm) !important; }
.std-text-base { font-size: var(--font-size-base) !important; }
.std-text-lg { font-size: var(--font-size-lg) !important; }
.std-text-xl { font-size: var(--font-size-xl) !important; }
.std-text-2xl { font-size: var(--font-size-2xl) !important; }
.std-text-3xl { font-size: var(--font-size-3xl) !important; }

.std-font-normal { font-weight: var(--font-weight-normal) !important; }
.std-font-medium { font-weight: var(--font-weight-medium) !important; }
.std-font-semibold { font-weight: var(--font-weight-semibold) !important; }
.std-font-bold { font-weight: var(--font-weight-bold) !important; }

// Color utilities
.std-text-primary { color: var(--color-text-primary) !important; }
.std-text-secondary { color: var(--color-text-secondary) !important; }
.std-text-dark { color: var(--color-text-dark) !important; }
.std-text-success { color: var(--color-success) !important; }
.std-text-error { color: var(--color-error) !important; }
.std-text-warning { color: var(--color-warning) !important; }

// Background utilities
.std-bg-primary { background-color: var(--color-primary) !important; }
.std-bg-secondary { background-color: var(--color-secondary) !important; }
.std-bg-success { background-color: var(--color-success) !important; }
.std-bg-error { background-color: var(--color-error) !important; }
.std-bg-warning { background-color: var(--color-warning) !important; }
.std-bg-light { background-color: var(--color-bg-light) !important; }
.std-bg-paper { background-color: var(--color-bg-paper) !important; }

// Spacing utilities
.std-p-xs { padding: var(--spacing-xs) !important; }
.std-p-sm { padding: var(--spacing-sm) !important; }
.std-p-md { padding: var(--spacing-md) !important; }
.std-p-lg { padding: var(--spacing-lg) !important; }
.std-p-xl { padding: var(--spacing-xl) !important; }

.std-m-xs { margin: var(--spacing-xs) !important; }
.std-m-sm { margin: var(--spacing-sm) !important; }
.std-m-md { margin: var(--spacing-md) !important; }
.std-m-lg { margin: var(--spacing-lg) !important; }
.std-m-xl { margin: var(--spacing-xl) !important; }

// Border radius utilities
.std-rounded-sm { border-radius: var(--border-radius-sm) !important; }
.std-rounded { border-radius: var(--border-radius-base) !important; }
.std-rounded-md { border-radius: var(--border-radius-md) !important; }
.std-rounded-lg { border-radius: var(--border-radius-lg) !important; }
.std-rounded-xl { border-radius: var(--border-radius-xl) !important; }

// Shadow utilities
.std-shadow-sm { box-shadow: var(--shadow-sm) !important; }
.std-shadow { box-shadow: var(--shadow-base) !important; }
.std-shadow-md { box-shadow: var(--shadow-md) !important; }
.std-shadow-lg { box-shadow: var(--shadow-lg) !important; }

// Standardized table styles
.std-table {
  width: 100%;
  border-collapse: collapse;
  font-family: var(--font-family-primary);
  
  th {
    background-color: var(--color-bg-light);
    color: var(--color-text-dark);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-sm);
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--color-grey-200);
    text-align: left;
  }
  
  td {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--color-grey-200);
    font-size: var(--font-size-sm);
    color: var(--color-text-primary);
  }
  
  tbody tr:hover {
    background-color: rgba(33, 150, 243, 0.04);
  }
}

// Standardized button styles
.std-btn {
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-base);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-sm);
  border: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
  
  &:disabled {
    background-color: var(--color-grey-300);
    color: var(--color-grey-500);
    cursor: not-allowed;
  }
  
  &.std-btn-primary {
    background-color: var(--color-primary);
    color: white;
    
    &:hover:not(:disabled) {
      background-color: var(--color-primary-dark);
      box-shadow: 0 2px 8px rgba(33, 150, 243, 0.25);
    }
  }
  
  &.std-btn-secondary {
    background-color: var(--color-secondary);
    color: white;
    
    &:hover:not(:disabled) {
      background-color: var(--color-secondary-dark);
      box-shadow: 0 2px 8px rgba(103, 58, 183, 0.25);
    }
  }
  
  &.std-btn-success {
    background-color: var(--color-success);
    color: white;
    
    &:hover:not(:disabled) {
      background-color: var(--color-success-dark);
      box-shadow: 0 2px 8px rgba(0, 230, 118, 0.25);
    }
  }
  
  &.std-btn-error {
    background-color: var(--color-error);
    color: white;
    
    &:hover:not(:disabled) {
      background-color: var(--color-error-dark);
      box-shadow: 0 2px 8px rgba(244, 67, 54, 0.25);
    }
  }
  
  &.std-btn-outline {
    background-color: transparent;
    color: var(--color-primary);
    border: 1px solid var(--color-primary);
    
    &:hover:not(:disabled) {
      background-color: var(--color-primary);
      color: white;
    }
  }
}

// Standardized card styles
.std-card {
  background-color: var(--color-bg-paper);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-base);
  border: 1px solid var(--color-grey-200);
  padding: var(--spacing-lg);
  
  .std-card-header {
    border-bottom: 1px solid var(--color-grey-200);
    padding-bottom: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    
    .std-card-title {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--color-text-dark);
      margin: 0;
    }
    
    .std-card-subtitle {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
      margin: var(--spacing-xs) 0 0 0;
    }
  }
}
