import React from 'react';
import { Form, Row, Col } from 'react-bootstrap';

const ProductStep2 = ({
  formData,
  setFormData,
  brands = [],
  categories = [],
  sousCategories = [],
  sousSousCategories = [],
  categoriesLoading = false,
  sousCategoriesLoading = false,
  sousSousCategoriesLoading = false,
  error
}) => (
  <>
    <Row>
      <Col md={6}>
        <Form.Group controlId="marque_id">
          <Form.Label>Marque</Form.Label>
          <Form.Select
            value={formData.marque_id || ''}
            onChange={(e) =>
              setFormData((prev) => ({
                ...prev,
                marque_id: e.target.value,
                categorie_id: '',
                sous_categorie_id: '',
                sous_sous_categorie_id: ''
              }))
            }
            required
            isInvalid={!!error && !formData.marque_id}
          >
            <option value="">Sélectionner une marque</option>
            {brands.map((brand) => (
              <option key={brand.id} value={brand.id}>
                {brand.nom_marque}
              </option>
            ))}
          </Form.Select>
        </Form.Group>
      </Col>
      <Col md={6}>
        <Form.Group controlId="categorie_id">
          <Form.Label>Catégorie</Form.Label>
          <Form.Select
            value={formData.categorie_id || ''}
            onChange={(e) =>
              setFormData((prev) => ({ ...prev, categorie_id: e.target.value, sous_categorie_id: '', sous_sous_categorie_id: '' }))
            }
            required
            isInvalid={!!error && !formData.categorie_id}
            disabled={categoriesLoading}
          >
            {categoriesLoading ? (
              <option value="">Chargement des catégories...</option>
            ) : (
              <>
                <option value="">Sélectionner une catégorie</option>
                {categories.map((cat) => (
                  <option key={cat.id} value={cat.id}>
                    {cat.nom_categorie}
                  </option>
                ))}
              </>
            )}
          </Form.Select>
        </Form.Group>
      </Col>
    </Row>
    <Row className="mt-3">
      <Col md={6}>
        <Form.Group controlId="sous_categorie_id">
          <Form.Label>Sous-catégorie</Form.Label>
          <Form.Select
            value={formData.sous_categorie_id || ''}
            onChange={(e) => setFormData((prev) => ({ ...prev, sous_categorie_id: e.target.value, sous_sous_categorie_id: '' }))}
            required
            isInvalid={!!error && !formData.sous_categorie_id}
            disabled={sousCategoriesLoading}
          >
            {sousCategoriesLoading ? (
              <option value="">Chargement des sous-catégories...</option>
            ) : (
              <>
                <option value="">Sélectionner une sous-catégorie</option>
                {sousCategories.map((sub) => (
                  <option key={sub.id} value={sub.id}>
                    {sub.nom_sous_categorie || sub.nom}
                  </option>
                ))}
              </>
            )}
          </Form.Select>
        </Form.Group>
      </Col>
      <Col md={6}>
        <Form.Group controlId="sous_sous_categorie_id">
          <Form.Label>Sous-sous-catégorie</Form.Label>
          <Form.Select
            value={formData.sous_sous_categorie_id || ''}
            onChange={(e) => setFormData((prev) => ({ ...prev, sous_sous_categorie_id: e.target.value }))}
            required
            isInvalid={!!error && !formData.sous_sous_categorie_id}
            disabled={sousSousCategoriesLoading}
          >
            {sousSousCategoriesLoading ? (
              <option value="">Chargement des sous-sous-catégories...</option>
            ) : (
              <>
                <option value="">Sélectionner une sous-sous-catégorie</option>
                {sousSousCategories.map((cat) => (
                  <option key={cat.id} value={cat.id}>
                    {cat.nom_sous_sous_categorie || cat.nom}
                  </option>
                ))}
              </>
            )}
          </Form.Select>
        </Form.Group>
      </Col>
    </Row>
  </>
);

export default ProductStep2;
